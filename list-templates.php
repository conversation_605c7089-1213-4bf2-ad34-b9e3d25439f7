<?php
/**
 * Template Name: List Templates
 * Description: A template that lists all available templates
 */

get_header();
?>

<main role="main">
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Available Templates</h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <div class="s-12 m-12 l-12">
            <h2>All Page Templates</h2>
            
            <?php
            // Get all page templates
            $templates = wp_get_theme()->get_page_templates();
            
            if (!empty($templates)) {
                echo '<ul>';
                foreach ($templates as $template_file => $template_name) {
                    echo '<li><strong>' . esc_html($template_name) . '</strong> - ' . esc_html($template_file) . '</li>';
                }
                echo '</ul>';
            } else {
                echo '<p>No page templates found.</p>';
            }
            ?>
            
            <h2>All PHP Files in Theme Directory</h2>
            
            <?php
            // List all PHP files in the theme directory
            $theme_dir = get_template_directory();
            $php_files = glob($theme_dir . '/*.php');
            
            if (!empty($php_files)) {
                echo '<ul>';
                foreach ($php_files as $php_file) {
                    $file_name = basename($php_file);
                    $file_content = file_get_contents($php_file);
                    
                    // Check if the file has a template name
                    $template_name = '';
                    if (preg_match('/Template Name:(.+)$/m', $file_content, $matches)) {
                        $template_name = ' - <strong>Template Name: ' . trim($matches[1]) . '</strong>';
                    }
                    
                    echo '<li>' . esc_html($file_name) . $template_name . '</li>';
                }
                echo '</ul>';
            } else {
                echo '<p>No PHP files found.</p>';
            }
            ?>
          </div>
        </div>
      </div>
    </div>
  </article>
</main>

<?php
get_footer();