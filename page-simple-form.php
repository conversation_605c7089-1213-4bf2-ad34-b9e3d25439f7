<?php
/**
 * Template Name: Simple Form
 * 
 * A simple form template for testing form submission.
 */

get_header();
?>

<div class="container">
    <div class="section background-white">
        <h1>Simple Form Test</h1>
        
        <div id="form-message" style="margin: 20px 0; padding: 15px; border-radius: 4px; display: none;"></div>
        
        <form id="simple-form" class="customform">
            <div class="grid margin">
                <div class="s-12 m-6 l-6">
                    <label for="fname">First Name:</label>
                    <input type="text" id="fname" name="fname" required>
                </div>
                
                <div class="s-12 m-6 l-6">
                    <label for="lname">Last Name:</label>
                    <input type="text" id="lname" name="lname" required>
                </div>
            </div>
            
            <div class="grid margin">
                <div class="s-12">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" required>
                </div>
            </div>
            
            <div class="grid margin">
                <div class="s-12">
                    <button type="submit" class="button background-blue text-white">Submit Form</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    $('#simple-form').on('submit', function(e) {
        e.preventDefault();
        
        $('#form-message').html('<p>Submitting form...</p>').show();
        
        var formData = new FormData(this);
        formData.append('action', 'nkhwazi_submit_application_form');
        formData.append('nonce', nkhwazi_application_form.nonce);
        
        $.ajax({
            url: nkhwazi_application_form.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Form submission success:', response);
                
                if (response.success) {
                    $('#form-message').html('<p style="color: green;">Form submitted successfully! Server response: ' + response.data.message + '</p>').show();
                    $('#simple-form')[0].reset();
                } else {
                    $('#form-message').html('<p style="color: red;">Form submission error: ' + response.data.message + '</p>').show();
                }
            },
            error: function(xhr, status, error) {
                console.log('Form submission error:', status, error);
                console.log('Response:', xhr.responseText);
                
                $('#form-message').html('<p style="color: red;">AJAX Error: ' + status + ' - ' + error + '</p><pre>' + xhr.responseText + '</pre>').show();
            }
        });
    });
});
</script>

<?php get_footer(); ?>