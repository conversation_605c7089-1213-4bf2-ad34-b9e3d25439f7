<?php
/**
 * Template Name: AJAX Test
 * 
 * A simple template for testing AJAX functionality.
 */

get_header();
?>

<div class="container">
    <div class="section background-white">
        <h1>AJAX Test</h1>
        
        <div id="ajax-test-result" style="margin: 20px 0; padding: 15px; border-radius: 4px; display: none;"></div>
        
        <button id="ajax-test-button" class="button background-blue text-white">Test AJAX</button>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    $('#ajax-test-button').on('click', function() {
        $('#ajax-test-result').html('<p>Testing AJAX...</p>').show();
        
        $.ajax({
            url: nkhwazi_application_form.ajax_url,
            type: 'POST',
            data: {
                action: 'nkhwazi_submit_application_form',
                nonce: nkhwazi_application_form.nonce,
                test: 'This is a test'
            },
            success: function(response) {
                console.log('AJAX Success:', response);
                
                if (response.success) {
                    $('#ajax-test-result').html('<p style="color: green;">AJAX test successful! Server response: ' + response.data.message + '</p>');
                } else {
                    $('#ajax-test-result').html('<p style="color: red;">AJAX test error: ' + response.data.message + '</p>');
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', status, error);
                console.log('Response:', xhr.responseText);
                
                $('#ajax-test-result').html('<p style="color: red;">AJAX Error: ' + status + ' - ' + error + '</p><pre>' + xhr.responseText + '</pre>');
            }
        });
    });
});
</script>

<?php get_footer(); ?>