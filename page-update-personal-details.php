<?php 
require_once('includes/header.php'); 
// Include the nationalities file for dropdown options
require_once('includes/nationalities.php');
/**
 * Page Template: Update Personal Details
 *
 * This template is intended for updating student and family information
 * by removing the sidebar. It provides a narrower content area, which enhances 
 * readability, especially for text-heavy content.
 */
?>
<!-- Include jQuery UI for datepicker -->
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<script src="https://code.jquery.com/jquery-1.12.4.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script>
  $(document).ready(function() {
    // Initialize datepicker
    $('#datepicker').datepicker({ 
      dateFormat: 'dd-mm-yy',
      changeMonth: true,
      changeYear: true,
      yearRange: "-100:+0" // Allow selection of any date in the past
    });
    
    // Form validation
    $('form[name="updateForm"]').submit(function(e) {
      var isValid = true;
      
      // Check all required fields
      $(this).find('.required').each(function() {
        if ($(this).val() === '') {
          $(this).css('border-color', '#3649e2');
          isValid = false;
        } else {
          $(this).css('border-color', '');
        }
      });
      
      // Validate email fields
      $(this).find('.email').each(function() {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if ($(this).val() !== '' && !emailRegex.test($(this).val())) {
          $(this).css('border-color', '#3649e2');
          alert('Please enter a valid email address');
          isValid = false;
        }
      });
      
      if (!isValid) {
        e.preventDefault();
        $('<div class="any-error">Please fill in all required fields correctly.</div>').insertBefore($(this)).fadeOut(5000);
      }
    });
  });
</script>

<!-- MAIN -->
<main role="main">
    <div class="line">
        <header class="rounded-div section background-blue background-transparent "
            data-image-src="assets/img/parallax-02.jpg">
            <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Update Personal Details
            </h1>
        </header>
    </div>
    <!-- Content -->
    <article class="s-12 m-12 l-9 center">
        <div class="section background-white">
            <!-- put page content below, do not edit above this comment -->

            <!-- School Information -->
            <div class="s-12 m-12 l-12 margin-bottom-20">
                <h2 class="text-uppercase text-strong">Update Personal Information</h2>

                <p>Please update your personal information below. This ensures we have the most current contact details and other important information.</p>
                <ul>
                    <li>A <span class='any-error'>red</span> left border signifies a required field which must be filled in</li>
                    <li>The placeholder text is for your guidance only, it disappears as you begin typing</li>
                </ul>
            </div>

            <!-- Update Form -->
            <form name="updateForm" class="customform" method="post" enctype="multipart/form-data">
                <input type="hidden" name="subject" value="Personal Details Update Form Submission">

                <!-- Part 1: Pupil's Particulars -->
                <h3 class="text-strong  margin-top-20">Student's Personal Information</h3>
                <hr />

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-5 l-5">
                            <label><span class='margin-bottom-10'>Last Name</span></label>
                            <input name="lname" class="required" placeholder="Last Name" title="Student's Last Name" type="text" required />
                        </div>
                        <div class="s-12 m-5 l-7">
                            <label><span class='margin-bottom-10'>First Name</span></label>
                            <input name="fname" class="required" placeholder="First Name" title="Student's First Name" type="text" required />
                        </div>
                    </div>
                </div>

                <!-- Second Row -->
                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Date of Birth</span></label>
                            <input id="datepicker" name="dob" class="required" placeholder="Date of Birth" title="Student's Date of Birth" type="text" required />
                        </div>
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Grade</span></label>
                            <select name="grade" class="required border-radius" required>
                                <option value="">Select Grade</option>

                                <option value="Grade 1">Grade 1</option>
                                <option value="Grade 2">Grade 2</option>
                                <option value="Grade 3">Grade 3</option>
                                <option value="Grade 4">Grade 4</option>
                                <option value="Grade 5">Grade 5</option>
                                <option value="Grade 6">Grade 6</option>
                                <option value="Grade 7">Grade 7</option>
                            </select>
                        </div>
                        <div class="s-12 m-12 l-6">
                            <label><span class='margin-bottom-10'>Religion</span></label>
                            <select name="religion" class="border-radius">
                                <option value="">Select Religion</option>
                                <option value="Christianity">Christianity</option>
                                <option value="Islam">Islam</option>
                                <option value="Hinduism">Hinduism</option>
                                <option value="Buddhism">Buddhism</option>
                                <option value="Judaism">Judaism</option>
                                <option value="Other">Other</option>
                                <option value="None">None</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Third Row -->
                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-12 l-12">
                            <label><span class='margin-bottom-10'>Home Address</span></label>
                            <input name="address" class="required" placeholder="Home Address" title="Home Address" type="text" required />
                        </div>
                    </div>
                </div>

                <!-- Fourth Row -->
                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-12 l-12">
                            <label><span class='margin-bottom-10'>Siblings at School</span></label>
                            <textarea name="siblings" placeholder="If the student has siblings at the school, please provide their names and grades. If none, write 'None'" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Father's Details -->
                <h3 class="text-strong  margin-top-20 ">Father or Male Guardian's Details</h3>
                <hr />

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-6 l-5">
                            <label><span class='margin-bottom-10'>Last Name</span></label>
                            <input name="lname_f" class="required" placeholder="Father's Last Name" title="Father's Last Name" type="text" required />
                        </div>
                        <div class="s-12 m-6 l-7">
                            <label><span class='margin-bottom-10'>First Name</span></label>
                            <input name="fname_f" class="required" placeholder="Father's First Name" title="Father's First Name" type="text" required />
                        </div>
                    </div>
                </div>

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-5 l-3">
                            <label><span class='margin-bottom-10'>Occupation</span></label>
                            <input name="occupation_f" class="required" placeholder="Occupation" title="Father's Occupation" type="text" required />
                        </div>
                        <div class="s-12 m-7 l-9">
                            <label><span class='margin-bottom-10'>Business Address</span></label>
                            <input name="address_business_f" placeholder="Business Address" title="Father's Business Address" type="text" />
                        </div>
                    </div>
                </div>

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Phone Number</span></label>
                            <input name="phone_f" class="required" placeholder="Phone Number" title="Father's Phone Number" type="text" required />
                        </div>
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Work Phone</span></label>
                            <input name="phone_work_f" placeholder="Work Phone" title="Father's Work Phone" type="text" />
                        </div>
                        <div class="s-12 m-12 l-6">
                            <label><span class='margin-bottom-10'>Email Address</span></label>
                            <input name="email_f" class="email" placeholder="Email Address" title="Father's Email Address" type="email" />
                        </div>
                    </div>
                </div>

                <!-- Mother's Details -->
                <h3 class="text-strong  margin-top-20 ">Mother or Female Guardian's Details</h3>
                <hr />

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-6 l-5">
                            <label><span class='margin-bottom-10'>Last Name</span></label>
                            <input name="lname_m" class="required" placeholder="Mother's Last Name" title="Mother's Last Name" type="text" required />
                        </div>
                        <div class="s-12 m-6 l-7">
                            <label><span class='margin-bottom-10'>First Name</span></label>
                            <input name="fname_m" class="required" placeholder="Mother's First Name" title="Mother's First Name" type="text" required />
                        </div>
                    </div>
                </div>

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-4 l-3">
                            <label><span class='margin-bottom-10'>Occupation</span></label>
                            <input name="occupation_m" class="required" placeholder="Occupation" title="Mother's Occupation" type="text" required />
                        </div>
                        <div class="s-12 m-8 l-9">
                            <label><span class='margin-bottom-10'>Business Address</span></label>
                            <input name="address_business_m" placeholder="Business Address" title="Mother's Business Address" type="text" />
                        </div>
                    </div>
                </div>

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Phone Number</span></label>
                            <input name="phone_m" class="required" placeholder="Phone Number" title="Mother's Phone Number" type="text" required />
                        </div>
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Work Phone</span></label>
                            <input name="phone_work_m" placeholder="Work Phone" title="Mother's Work Phone" type="text" />
                        </div>
                        <div class="s-12 m-12 l-6">
                            <label><span class='margin-bottom-10'>Email Address</span></label>
                            <input name="email_m" class="email" placeholder="Email Address" title="Mother's Email Address" type="email" />
                        </div>
                    </div>
                </div>

                <!-- Family Doctor Section -->
                <h3 class="text-strong  margin-top-20 ">Family Doctor or Clinic
                    <span class='tooltip-container'>
                        <span class='tooltip-content tooltip-top'>This can be a named Doctor or a Clinic depending on your Health Scheme</span>
                        <i class='icon-information_black text-green text-red-hover'></i>
                    </span>
                </h3>
                <hr />

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-12 l-12">
                            <label><span class='margin-bottom-10'>Doctor/Clinic Name</span></label>
                            <input name="family_doctor" placeholder="Doctor/Clinic Name" title="Doctor/Clinic Name" type="text" />
                        </div>
                    </div>
                </div>

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Mobile Number</span></label>
                            <input name="phone_doctor" placeholder="Mobile Number" title="Doctor's Mobile Number" type="text" />
                        </div>
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Office Phone</span></label>
                            <input name="tel_doctor" placeholder="Office Phone" title="Doctor's Office Phone" type="text" />
                        </div>
                        <div class="s-12 m-12 l-6">
                            <label><span class='margin-bottom-10'>Email Address</span></label>
                            <input name="email_doctor" class="email" placeholder="Email Address" title="Doctor's Email Address" type="email" />
                        </div>
                    </div>
                </div>

                <!-- Emergency Contact Section -->
                <h3 class="text-strong  margin-top-20 ">Emergency Contact
                    <span class='tooltip-container'>
                        <span class='tooltip-content tooltip-top'>In case of emergency, if parents or guardians are unreachable, we will contact this person</span>
                        <i class='icon-information_black text-green text-red-hover'></i>
                    </span>
                </h3>
                <hr />

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-6 l-5">
                            <label><span class='margin-bottom-10'>Last Name</span></label>
                            <input name="lname_emergency" class="required" placeholder="Last Name" title="Emergency Contact's Last Name" type="text" required />
                        </div>
                        <div class="s-12 m-6 l-7">
                            <label><span class='margin-bottom-10'>First Name</span></label>
                            <input name="fname_emergency" class="required" placeholder="First Name" title="Emergency Contact's First Name" type="text" required />
                        </div>
                    </div>
                </div>

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-12 l-3">
                            <label><span class='margin-bottom-10'>Mobile Number</span></label>
                            <input name="phone_emergency" class="required" placeholder="Mobile Number" title="Emergency Contact's Mobile Number" type="text" required />
                        </div>
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Alternative Phone</span></label>
                            <input name="tel_emergency" placeholder="Alternative Phone" title="Emergency Contact's Alternative Phone" type="text" />
                        </div>
                        <div class="s-12 m-6 l-6">
                            <label><span class='margin-bottom-10'>Email Address</span></label>
                            <input name="email_emergency" class="email" placeholder="Email Address" title="Emergency Contact's Email Address" type="email" />
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <h3 class="text-strong  margin-top-20 ">Additional Information</h3>
                <hr />

                <div class="line">
                    <div class="margin">
                        <div class="s-12 m-12 l-12">
                            <label><span class='margin-bottom-10'>Additional Information</span></label>
                            <textarea name="more_info" placeholder="Please provide any additional information that the school should be aware of" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="margin">
                    <div class="s-12 m-6 l-3 left">
                        <button class="submit-form submit-btn button border-radius text-white" type="submit">Update Information</button>
                    </div>
                    <div class="s-12 m-6 l-3 right">
                        <button class="reset-form cancel-btn button border-radius text-white" type="reset">Reset</button>
                    </div>
                </div>
            </form>
        </div>
    </article>
</main>

<?php require_once('includes/footer.php'); ?>