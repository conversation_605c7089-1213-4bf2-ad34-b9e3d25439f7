<?php 
/**
 * Template Name: Update Personal Details
 *
 * This template is intended for updating student and family information
 * by removing the sidebar. It provides a narrower content area, which enhances 
 * readability, especially for text-heavy content.
 */

// Include the nationalities file for dropdown options
require_once('includes/nationalities.php');
// Include the update details form functionality
require_once('includes/update-details-form.php');

get_header();
?>
<!-- MAIN -->
<main role="main">
    <div class="line">
        <header class="rounded-div section background-blue background-transparent "
            data-image-src="assets/img/parallax-02.jpg">
            <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Update Personal Details
            </h1>
        </header>
    </div>
    <!-- Content -->
    <article class="s-12 m-12 l-9 center">
        <div class="section background-white">
            <!-- put page content below, do not edit above this comment -->

            <!-- School Information -->
            <div class="s-12 m-12 l-12 margin-bottom-20 form-header">
                <h2 class="text-uppercase text-strong">Update Personal Information</h2>

                <p>Please update your personal information below. This ensures we have the most current contact details and other important information.</p>
                <ul>
                    <li>A <span class='any-error'>red</span> left border signifies a required field which must be filled in</li>
                    <li>The placeholder text is for your guidance only, it disappears as you begin typing</li>
                </ul>
            </div>

            <!-- Update Form -->
            <form id="application-form" name="updateForm" class="customform" method="post" enctype="multipart/form-data">
                <input type="hidden" name="subject" value="Personal Details Update Form Submission">
                <input type="hidden" name="nonce" value="<?php echo wp_create_nonce('nkhwazi_personal_details_form_nonce'); ?>">
                
                <!-- Form Messages -->
                <div class="form-message"></div>
                
                <!-- Loading Indicator -->
                <div class="form-loading">
                    <div class="spinner"></div>
                    <p>Submitting your information...</p>
                </div>
                
                <!-- Form Progress Indicator -->
                <div class="form-progress s-12 m-12 l-12">
                    <div class="form-progress-step clickable" data-step="0">
                        <div class="form-progress-step-number">1</div>
                        <div class="form-progress-step-label">Student Details</div>
                    </div>
                    <div class="form-progress-step clickable" data-step="1">
                        <div class="form-progress-step-number">2</div>
                        <div class="form-progress-step-label">Father's Details</div>
                    </div>
                    <div class="form-progress-step clickable" data-step="2">
                        <div class="form-progress-step-number">3</div>
                        <div class="form-progress-step-label">Mother's Details</div>
                    </div>
                    <div class="form-progress-step clickable" data-step="3">
                        <div class="form-progress-step-number">4</div>
                        <div class="form-progress-step-label">Medical & Emergency</div>
                    </div>
                </div>

                <!-- Step 1: Student's Personal Information -->
                <div class="form-step step-1" data-step="0">
                <h3 class="text-strong margin-top-20 form-section-title">Student's Personal Information</h3>

                <div class="grid margin">
                    <div class="s-12 m-5 l-5">
                        <label><span class='margin-bottom-10'>Last Name</span></label>
                        <input name="lname" class="required" placeholder="Last Name" title="Student's Last Name" type="text" required data-min-length="2" data-max-length="30" />
                    </div>
                    <div class="s-12 m-5 l-7">
                        <label><span class='margin-bottom-10'>First Name</span></label>
                        <input name="fname" class="required" placeholder="First Name" title="Student's First Name" type="text" required data-min-length="2" data-max-length="30" />
                    </div>
                </div>

                <div class="grid margin">
                    <div class="s-12 m-6 l-3">
                        <label><span class='margin-bottom-10'>Date of Birth</span></label>
                        <input id="datepicker" name="dob" class="required" placeholder="Date of Birth" title="Student's Date of Birth" type="text" required />
                    </div>
                    <div class="s-12 m-6 l-3">
                        <label><span class='margin-bottom-10'>Grade</span></label>
                        <select name="grade" class="required border-radius" required>
                            <option value="">Select Grade</option>
                            <option value="Reception">Reception</option>
                            <option value="Grade 1">Grade 1</option>
                            <option value="Grade 2">Grade 2</option>
                            <option value="Grade 3">Grade 3</option>
                            <option value="Grade 4">Grade 4</option>
                            <option value="Grade 5">Grade 5</option>
                            <option value="Grade 6">Grade 6</option>
                            <option value="Grade 7">Grade 7</option>
                        </select>
                    </div>
                    <div class="s-12 m-12 l-6">
                        <label><span class='margin-bottom-10'>Religion</span></label>
                        <select name="religion" class="border-radius">
                            <option value="">Select Religion</option>
                            <option value="Christianity">Christianity</option>
                            <option value="Islam">Islam</option>
                            <option value="Hinduism">Hinduism</option>
                            <option value="Buddhism">Buddhism</option>
                            <option value="Judaism">Judaism</option>
                            <option value="Other">Other</option>
                            <option value="None">None</option>
                        </select>
                    </div>
                </div>

                <div class="grid margin">
                    <div class="s-12 m-12 l-12">
                        <label><span class='margin-bottom-10'>Home Address</span></label>
                        <input name="address" class="required" placeholder="Home Address" title="Home Address" type="text" required data-min-length="5" data-max-length="100" />
                    </div>
                </div>

                <div class="grid margin">
                    <div class="s-12 m-12 l-12">
                        <label><span class='margin-bottom-10'>Siblings at School</span></label>
                        <textarea name="siblings" placeholder="If the student has siblings at the school, please provide their names and grades. If none, write 'None'" rows="3" data-min-length="0" data-max-length="200"></textarea>
                    </div>
                </div>
                
                <!-- Step 1 Navigation -->
                <div class="form-navigation">
                    <div></div> <!-- Empty div for flex alignment -->
                    <button type="button" class="next-step button rounded-btn text-white background-blue">Next: Father's Details</button>
                </div>
                </div><!-- End of Step 1 -->

                <!-- Step 2: Father's Details -->
                <div class="form-step step-2" data-step="1">
                <h3 class="text-strong margin-top-20 form-section-title">Father or Male Guardian's Details</h3>

                <div class="grid margin">
                    <div class="s-12 m-6 l-5">
                        <label><span class='margin-bottom-10'>Last Name</span></label>
                        <input name="lname_f" class="required" placeholder="Father's Last Name" title="Father's Last Name" type="text" required data-min-length="2" data-max-length="30" />
                    </div>
                    <div class="s-12 m-6 l-7">
                        <label><span class='margin-bottom-10'>First Name</span></label>
                        <input name="fname_f" class="required" placeholder="Father's First Name" title="Father's First Name" type="text" required data-min-length="2" data-max-length="30" />
                    </div>
                </div>

                <div class="grid margin">
                    <div class="s-12 m-5 l-4">
                        <label><span class='margin-bottom-10'>Occupation</span></label>
                        <input name="occupation_f" class="required" placeholder="Occupation" title="Father's Occupation" type="text" required data-min-length="2" data-max-length="30" />
                    </div>
                    <div class="s-12 m-7 l-8">
                        <label><span class='margin-bottom-10'>Business Address</span></label>
                        <input name="address_business_f" placeholder="Business Address" title="Father's Business Address" type="text" data-min-length="0" data-max-length="100" />
                    </div>
                </div>

                <div class="grid margin">
                    <div class="s-12 m-6 l-3">
                        <label><span class='margin-bottom-10'>Phone Number</span></label>
                        <input name="phone_f" class="required" placeholder="Phone Number" title="Father's Phone Number" type="text" required data-min-length="4" data-max-length="20" />
                    </div>
                    <div class="s-12 m-6 l-3">
                        <label><span class='margin-bottom-10'>Work Phone</span></label>
                        <input name="phone_work_f" placeholder="Work Phone" title="Father's Work Phone" type="text" data-min-length="0" data-max-length="20" />
                    </div>
                    <div class="s-12 m-12 l-6">
                        <label><span class='margin-bottom-10'>Email Address</span></label>
                        <input name="email_f" class="email" placeholder="Email Address" title="Father's Email Address" type="email" data-validate-email="true" />
                    </div>
                </div>
                
                <!-- Step 2 Navigation -->
                <div class="form-navigation">
                    <button type="button" class="prev-step button rounded-btn text-dark background-white border-dark">Previous: Student's Details</button>
                    <button type="button" class="next-step button rounded-btn text-white background-blue">Next: Mother's Details</button>
                </div>
                </div><!-- End of Step 2 -->

                <!-- Step 3: Mother's Details -->
                <div class="form-step step-3" data-step="2">
                <h3 class="text-strong margin-top-20 form-section-title">Mother or Female Guardian's Details</h3>

                <div class="grid margin">
                    <div class="s-12 m-6 l-5">
                        <label><span class='margin-bottom-10'>Last Name</span></label>
                        <input name="lname_m" class="required" placeholder="Mother's Last Name" title="Mother's Last Name" type="text" required data-min-length="2" data-max-length="30" />
                    </div>
                    <div class="s-12 m-6 l-7">
                        <label><span class='margin-bottom-10'>First Name</span></label>
                        <input name="fname_m" class="required" placeholder="Mother's First Name" title="Mother's First Name" type="text" required data-min-length="2" data-max-length="30" />
                    </div>
                </div>

                <div class="grid margin">
                    <div class="s-12 m-4 l-4">
                        <label><span class='margin-bottom-10'>Occupation</span></label>
                        <input name="occupation_m" class="required" placeholder="Occupation" title="Mother's Occupation" type="text" required data-min-length="2" data-max-length="30" />
                    </div>
                    <div class="s-12 m-8 l-8">
                        <label><span class='margin-bottom-10'>Business Address</span></label>
                        <input name="address_business_m" placeholder="Business Address" title="Mother's Business Address" type="text" data-min-length="0" data-max-length="100" />
                    </div>
                </div>

                <div class="grid margin">
                    <div class="s-12 m-6 l-3">
                        <label><span class='margin-bottom-10'>Phone Number</span></label>
                        <input name="phone_m" class="required" placeholder="Phone Number" title="Mother's Phone Number" type="text" required data-min-length="4" data-max-length="20" />
                    </div>
                    <div class="s-12 m-6 l-3">
                        <label><span class='margin-bottom-10'>Work Phone</span></label>
                        <input name="phone_work_m" placeholder="Work Phone" title="Mother's Work Phone" type="text" data-min-length="0" data-max-length="20" />
                    </div>
                    <div class="s-12 m-12 l-6">
                        <label><span class='margin-bottom-10'>Email Address</span></label>
                        <input name="email_m" class="email" placeholder="Email Address" title="Mother's Email Address" type="email" data-validate-email="true" />
                    </div>
                </div>

                <!-- Step 3 Navigation -->
                <div class="form-navigation">
                    <button type="button" class="prev-step button rounded-btn text-dark background-white border-dark">Previous: Father's Details</button>
                    <button type="button" class="next-step button rounded-btn text-white background-blue">Next: Medical & Emergency</button>
                </div>
                </div><!-- End of Step 3 -->
                
                <!-- Step 4: Medical & Emergency Information -->
                <div class="form-step step-4" data-step="3">
                <h3 class="text-strong margin-top-20 form-section-title">Family Doctor or Clinic
                    <span class='tooltip-container'>
                        <span class='tooltip-content tooltip-top'>This can be a named Doctor or a Clinic depending on your Health Scheme</span>
                        <i class='icon-information_black text-green text-red-hover'></i>
                    </span>
                </h3>

                <div class="grid margin">
                    <div class="s-12 m-12 l-12">
                        <label><span class='margin-bottom-10'>Doctor/Clinic Name</span></label>
                        <input name="family_doctor" placeholder="Doctor/Clinic Name" title="Doctor/Clinic Name" type="text" data-min-length="0" data-max-length="50" />
                    </div>
                </div>

                <div class="grid margin">
                    <div class="s-12 m-6 l-3">
                        <label><span class='margin-bottom-10'>Mobile Number</span></label>
                        <input name="phone_doctor" placeholder="Mobile Number" title="Doctor's Mobile Number" type="text" data-min-length="0" data-max-length="20" />
                    </div>
                    <div class="s-12 m-6 l-3">
                        <label><span class='margin-bottom-10'>Office Phone</span></label>
                        <input name="tel_doctor" placeholder="Office Phone" title="Doctor's Office Phone" type="text" data-min-length="0" data-max-length="20" />
                    </div>
                    <div class="s-12 m-12 l-6">
                        <label><span class='margin-bottom-10'>Email Address</span></label>
                        <input name="email_doctor" class="email" placeholder="Email Address" title="Doctor's Email Address" type="email" data-validate-email="true" />
                    </div>
                </div>

                <h3 class="text-strong margin-top-20 form-section-title">Emergency Contact
                    <span class='tooltip-container'>
                        <span class='tooltip-content tooltip-top'>In case of emergency, if parents or guardians are unreachable, we will contact this person</span>
                        <i class='icon-information_black text-green text-red-hover'></i>
                    </span>
                </h3>

                <div class="grid margin">
                    <div class="s-12 m-6 l-5">
                        <label><span class='margin-bottom-10'>Last Name</span></label>
                        <input name="lname_emergency" class="required" placeholder="Last Name" title="Emergency Contact's Last Name" type="text" required data-min-length="2" data-max-length="30" />
                    </div>
                    <div class="s-12 m-6 l-7">
                        <label><span class='margin-bottom-10'>First Name</span></label>
                        <input name="fname_emergency" class="required" placeholder="First Name" title="Emergency Contact's First Name" type="text" required data-min-length="2" data-max-length="30" />
                    </div>
                </div>

                <div class="grid margin">
                    <div class="s-12 m-12 l-3">
                        <label><span class='margin-bottom-10'>Mobile Number</span></label>
                        <input name="phone_emergency" class="required" placeholder="Mobile Number" title="Emergency Contact's Mobile Number" type="text" required data-min-length="4" data-max-length="20" />
                    </div>
                    <div class="s-12 m-6 l-3">
                        <label><span class='margin-bottom-10'>Alternative Phone</span></label>
                        <input name="tel_emergency" placeholder="Alternative Phone" title="Emergency Contact's Alternative Phone" type="text" data-min-length="0" data-max-length="20" />
                    </div>
                    <div class="s-12 m-6 l-6">
                        <label><span class='margin-bottom-10'>Email Address</span></label>
                        <input name="email_emergency" class="email" placeholder="Email Address" title="Emergency Contact's Email Address" type="email" data-validate-email="true" />
                    </div>
                </div>

                <h3 class="text-strong margin-top-20 form-section-title">Additional Information</h3>

                <div class="grid margin">
                    <div class="s-12 m-12 l-12">
                        <label><span class='margin-bottom-10'>Additional Information</span></label>
                        <textarea name="more_info" placeholder="Please provide any additional information that the school should be aware of" rows="3" data-min-length="0" data-max-length="400"></textarea>
                    </div>
                </div>
                
                <!-- Step 4 Navigation -->
                <div class="form-navigation">
                    <button type="button" class="prev-step button rounded-btn text-dark background-white border-dark">Previous: Mother's Details</button>
                    <div></div>
                </div>
                
                <!-- reCAPTCHA -->
                <div class="grid margin">
                    <div class="s-12 m-12 l-12 text-center">
                        <div class="g-recaptcha-container">
                            <div class="g-recaptcha" data-sitekey="6Lf79rcaAAAAALdqWY74mw_n6DtTxR_C5AEL6cfL"></div>
                            <div class="recaptcha-error-message"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Submit Button -->
                <div class="grid margin">
                    <div class="s-12 text-center">
                        <button class="button submit-btn rounded-btn text-white background-blue"
                            type="submit">Update Information</button>
                    </div>
                </div>
                

                </div><!-- End of Step 4 -->

            </form>
        </div>
    </article>
</main>

<?php get_footer(); ?>