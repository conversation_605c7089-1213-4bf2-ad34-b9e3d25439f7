<?php
/**
 * Contact Form Functionality
 *
 * @package YaoZambia
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Register Contact Message Custom Post Type
 */
function yao_register_contact_message_cpt() {
    register_post_type('contact_message', array(
        'labels' => array(
            'name'               => __('Contact Messages', 'yaozambia'),
            'singular_name'      => __('Contact Message', 'yaozambia'),
            'add_new'            => __('Add New', 'yaozambia'),
            'add_new_item'       => __('Add New Message', 'yaozambia'),
            'edit_item'          => __('Edit Message', 'yaozambia'),
            'new_item'           => __('New Message', 'yaozambia'),
            'view_item'          => __('View Message', 'yaozambia'),
            'search_items'       => __('Search Messages', 'yaozambia'),
            'not_found'          => __('No messages found', 'yaozambia'),
            'not_found_in_trash' => __('No messages found in Trash', 'yaozambia'),
            'menu_name'          => __('Contact Messages', 'yaozambia'),
        ),
        'public'              => false,
        'publicly_queryable'  => false,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'show_in_rest'        => false,
        'query_var'           => false,
        'rewrite'             => false,
        'capability_type'     => 'post',
        'has_archive'         => false,
        'hierarchical'        => false,
        'menu_position'       => 30,
        'menu_icon'           => 'dashicons-email-alt',
        'supports'            => array('title'),
    ));
}
add_action('init', 'yao_register_contact_message_cpt');

/**
 * Add meta boxes for contact messages
 */
function yao_contact_message_meta_boxes() {
    add_meta_box(
        'yao_contact_message_details',
        __('Message Details', 'yaozambia'),
        'yao_contact_message_details_callback',
        'contact_message',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'yao_contact_message_meta_boxes');

/**
 * Contact message details meta box callback
 */
function yao_contact_message_details_callback($post) {
    // Get message details
    $full_name = get_post_meta($post->ID, '_contact_full_name', true);
    $email = get_post_meta($post->ID, '_contact_email', true);
    $subject = get_post_meta($post->ID, '_contact_subject', true);
    $phone = get_post_meta($post->ID, '_contact_phone', true);
    $message = get_post_meta($post->ID, '_contact_message', true);
    $date = get_the_date('F j, Y \a\t g:i a', $post->ID);
    $ip = get_post_meta($post->ID, '_contact_ip', true);
    
    // Output message details
    ?>
    <div class="contact-message-details">
        <p><strong><?php _e('From:', 'yaozambia'); ?></strong> <?php echo esc_html($full_name); ?></p>
        <p><strong><?php _e('Email:', 'yaozambia'); ?></strong> <a href="mailto:<?php echo esc_attr($email); ?>"><?php echo esc_html($email); ?></a></p>
        <?php if (!empty($phone)) : ?>
            <p><strong><?php _e('Phone:', 'yaozambia'); ?></strong> <?php echo esc_html($phone); ?></p>
        <?php endif; ?>
        <p><strong><?php _e('Subject:', 'yaozambia'); ?></strong> <?php echo esc_html($subject); ?></p>
        <p><strong><?php _e('Date:', 'yaozambia'); ?></strong> <?php echo esc_html($date); ?></p>
        <p><strong><?php _e('IP Address:', 'yaozambia'); ?></strong> <?php echo esc_html($ip); ?></p>
        <div class="message-content" style="margin-top: 15px; padding: 15px; background: #f9f9f9; border: 1px solid #e5e5e5;">
            <h4 style="margin-top: 0;"><?php _e('Message:', 'yaozambia'); ?></h4>
            <?php echo wpautop(esc_html($message)); ?>
        </div>
    </div>
    <?php
}

/**
 * Add settings page for contact form
 */
function yao_contact_form_settings_menu() {
    add_submenu_page(
        'edit.php?post_type=contact_message',
        __('Contact Form Settings', 'yaozambia'),
        __('Settings', 'yaozambia'),
        'manage_options',
        'contact-form-settings',
        'yao_contact_form_settings_page'
    );
}
add_action('admin_menu', 'yao_contact_form_settings_menu');

/**
 * Contact form settings page callback
 */
function yao_contact_form_settings_page() {
    // Save settings
    if (isset($_POST['yao_contact_settings_nonce']) && wp_verify_nonce($_POST['yao_contact_settings_nonce'], 'yao_contact_settings')) {
        if (isset($_POST['yao_contact_email'])) {
            $email = sanitize_email($_POST['yao_contact_email']);
            if (!empty($email)) {
                update_option('yao_contact_email', $email);
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully.', 'yaozambia') . '</p></div>';
            }
        }
    }
    
    // Get current email
    $current_email = get_option('yao_contact_email', get_option('admin_email'));
    
    ?>
    <div class="wrap">
        <h1><?php _e('Contact Form Settings', 'yaozambia'); ?></h1>
        <form method="post" action="">
            <?php wp_nonce_field('yao_contact_settings', 'yao_contact_settings_nonce'); ?>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="yao_contact_email"><?php _e('Notification Email', 'yaozambia'); ?></label>
                    </th>
                    <td>
                        <input type="email" name="yao_contact_email" id="yao_contact_email" value="<?php echo esc_attr($current_email); ?>" class="regular-text">
                        <p class="description"><?php _e('Email address where contact form submissions will be sent.', 'yaozambia'); ?></p>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}

/**
 * Enqueue contact form script and styles
 */
function yao_enqueue_contact_form_script() {
    if (is_page_template('template-contact.php')) {
        // Enqueue CSS
        wp_enqueue_style(
            'yaozambia-contact-form',
            get_template_directory_uri() . '/assets/css/contact-form.css',
            array(),
            filemtime(get_template_directory() . '/assets/css/contact-form.css')
        );

        // Enqueue JavaScript
        wp_enqueue_script(
            'yaozambia-contact-form',
            get_template_directory_uri() . '/assets/js/contact-form.js',
            array('jquery'),
            filemtime(get_template_directory() . '/assets/js/contact-form.js'),
            true
        );

        // Pass AJAX URL to script
        wp_localize_script(
            'yaozambia-contact-form',
            'yao_contact_form',
            array(
                'ajax_url' => admin_url('admin-ajax.php')
            )
        );
    }
}
add_action('wp_enqueue_scripts', 'yao_enqueue_contact_form_script');

/**
 * Handle contact form submission via AJAX
 */
function yao_submit_contact_form() {
    // Check nonce
    if (!isset($_POST['yao_contact_nonce']) || !wp_verify_nonce($_POST['yao_contact_nonce'], 'yao_contact_form_nonce')) {
        wp_send_json_error(array(
            'message' => __('Security check failed. Please refresh the page and try again.', 'yaozambia')
        ));
    }
    
    // Validate required fields
    $required_fields = array(
        'full_name' => __('Full Name', 'yaozambia'),
        'email' => __('Email Address', 'yaozambia'),
        'subject' => __('Subject', 'yaozambia'),
        'message' => __('Message', 'yaozambia')
    );
    
    foreach ($required_fields as $field => $label) {
        if (empty($_POST[$field])) {
            wp_send_json_error(array(
                'message' => sprintf(__('%s is required.', 'yaozambia'), $label)
            ));
        }
    }
    
    // Validate email
    if (!is_email($_POST['email'])) {
        wp_send_json_error(array(
            'message' => __('Please enter a valid email address.', 'yaozambia')
        ));
    }
    
    // Verify reCAPTCHA if enabled
    if (function_exists('yao_verify_recaptcha') && get_option('yao_recaptcha_site_key') && get_option('yao_recaptcha_secret_key')) {
        // Get reCAPTCHA response from any possible source
        $recaptcha_response = '';
        
        // Check all possible parameter names
        $possible_params = ['g-recaptcha-response', 'recaptcha', 'g_recaptcha_response'];
        
        foreach ($possible_params as $param) {
            if (!empty($_POST[$param])) {
                $recaptcha_response = $_POST[$param];
                break;
            }
        }
        
        // If no response found, check if it's in the form data
        if (empty($recaptcha_response) && !empty($_POST['formData']) && is_array($_POST['formData'])) {
            foreach ($possible_params as $param) {
                if (!empty($_POST['formData'][$param])) {
                    $recaptcha_response = $_POST['formData'][$param];
                    break;
                }
            }
        }
        
        if (empty($recaptcha_response)) {
            wp_send_json_error(array(
                'message' => __('Please complete the reCAPTCHA verification.', 'yaozambia')
            ));
        }
        
        $recaptcha_result = yao_verify_recaptcha($recaptcha_response);
        
        if (is_wp_error($recaptcha_result)) {
            wp_send_json_error(array(
                'message' => $recaptcha_result->get_error_message()
            ));
        }
    }
    
    // Sanitize input
    $full_name = sanitize_text_field($_POST['full_name']);
    $email = sanitize_email($_POST['email']);
    $subject = sanitize_text_field($_POST['subject']);
    $phone = !empty($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    $message = sanitize_textarea_field($_POST['message']);
    
    // Create post title
    $post_title = sprintf(__('Message from %s', 'yaozambia'), $full_name);
    
    // Create new contact message post
    $post_id = wp_insert_post(array(
        'post_title' => $post_title,
        'post_type' => 'contact_message',
        'post_status' => 'publish'
    ));
    
    if (is_wp_error($post_id)) {
        wp_send_json_error(array(
            'message' => __('Failed to save your message. Please try again.', 'yaozambia')
        ));
    }
    
    // Save message details as post meta
    update_post_meta($post_id, '_contact_full_name', $full_name);
    update_post_meta($post_id, '_contact_email', $email);
    update_post_meta($post_id, '_contact_subject', $subject);
    update_post_meta($post_id, '_contact_phone', $phone);
    update_post_meta($post_id, '_contact_message', $message);
    update_post_meta($post_id, '_contact_ip', $_SERVER['REMOTE_ADDR']);
    
    // Send email notification
    $to = get_option('yao_contact_email', get_option('admin_email'));
    $email_subject = sprintf(__('[%s] New Contact Form Message: %s', 'yaozambia'), get_bloginfo('name'), $subject);
    
    $email_body = sprintf(__("You have received a new message from your website contact form.\n\n", 'yaozambia'));
    $email_body .= sprintf(__("Name: %s\n", 'yaozambia'), $full_name);
    $email_body .= sprintf(__("Email: %s\n", 'yaozambia'), $email);
    
    if (!empty($phone)) {
        $email_body .= sprintf(__("Phone: %s\n", 'yaozambia'), $phone);
    }
    
    $email_body .= sprintf(__("Subject: %s\n\n", 'yaozambia'), $subject);
    $email_body .= sprintf(__("Message:\n%s\n\n", 'yaozambia'), $message);
    
    $email_body .= sprintf(__("You can view this message in your WordPress dashboard: %s", 'yaozambia'), admin_url('post.php?post=' . $post_id . '&action=edit'));
    
    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
        'Reply-To: ' . $full_name . ' <' . $email . '>'
    );
    
    $email_sent = wp_mail($to, $email_subject, $email_body, $headers);
    
    // Return success response
    wp_send_json_success(array(
        'message' => __('Thank you! Your message has been sent successfully.', 'yaozambia')
    ));
}
add_action('wp_ajax_yao_submit_contact_form', 'yao_submit_contact_form');
add_action('wp_ajax_nopriv_yao_submit_contact_form', 'yao_submit_contact_form');