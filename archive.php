<?php
/**
 * The template for displaying archive pages
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">
          <?php
          if (is_category()) {
              echo single_cat_title('', false);
          } elseif (is_tag()) {
              echo single_tag_title('', false);
          } elseif (is_author()) {
              echo get_the_author();
          } elseif (is_date()) {
              echo get_the_date('F Y');
          } else {
              echo 'Archives';
          }
          ?>
        </h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <!-- put page content below, do not edit above this comment -->
        <div class="margin">
          <div class="grid margin">
            <?php
            if (have_posts()) :
                while (have_posts()) : the_post();
                    // Get the first category
                    $categories = get_the_category();
                    $category_name = !empty($categories) ? $categories[0]->name : '';
                    $category_link = !empty($categories) ? get_category_link($categories[0]->term_id) : '';
            ?>
            <article class="s-12 m-6 l-4 margin-bottom-20 blog-post">
              <?php 
              $blog_image = get_field('blog_image');
              if ($blog_image) : 
                $blog_image_url = wp_get_attachment_image_url($blog_image['ID'], 'blog-thumbnail'); // Use the blog-thumbnail size
                if (!$blog_image_url) {
                  $blog_image_url = $blog_image['url']; // Fallback to original if size doesn't exist
                }
              ?>
              <img class="rounded-image-top margin-bottom blog-image" src="<?php echo esc_url($blog_image_url); ?>" alt="<?php echo esc_attr($blog_image['alt'] ? $blog_image['alt'] : get_the_title()); ?>">
              <?php endif; ?>
              
              <h3 class="text-strong text-uppercase"><a class="text-dark text-blue-hover" href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
              <div class="grid">
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12"><?php echo get_the_date(); ?></div>
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-author">
                  <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>" class="blog-author">By <?php the_author(); ?></a>
                </div>
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-category">
                  <?php if (!empty($category_name)) : ?>
                  <a href="<?php echo esc_url($category_link); ?>" class="blog-category"><?php echo esc_html($category_name); ?></a>
                  <?php endif; ?>
                </div>
              </div>
              <p><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
              <a class="text-more-info text-primary-hover margin-bottom-30" href="<?php the_permalink(); ?>">Blog Details</a>
            </article>
            <?php
                endwhile;
            else:
            ?>
            <div class="s-12 margin-bottom-20">
              <p>No posts found.</p>
            </div>
            <?php endif; ?>
          </div><!-- Grid -->

          <?php if (have_posts() && $wp_query->max_num_pages > 1): ?>
          <!-- Pagination -->
          <div class="line text-center pagination-container">
            <div class="s-12 margin-bottom">
              <nav class="pagination-nav">
                <?php
                // Debug pagination info for admins
                if (is_user_logged_in() && current_user_can('administrator')) {
                    $paged = get_query_var('paged') ? get_query_var('paged') : 1;
                    echo '<!-- Pagination Debug: Current page: ' . $paged . ' -->';
                    echo '<!-- Pagination Debug: Max pages: ' . $wp_query->max_num_pages . ' -->';
                    echo '<!-- Pagination Debug: Found posts: ' . $wp_query->found_posts . ' -->';
                    echo '<!-- Archive Pagination Debug: Is category: ' . (is_category() ? 'yes' : 'no') . ' -->';
                    if (is_category()) {
                        $cat = get_queried_object();
                        echo '<!-- Archive Pagination Debug: Category: ' . $cat->name . ' (ID: ' . $cat->term_id . ', Count: ' . $cat->count . ') -->';
                    }
                    if (is_author()) {
                        $author = get_queried_object();
                        echo '<!-- Archive Pagination Debug: Author: ' . $author->display_name . ' (ID: ' . $author->ID . ') -->';
                    }
                }
                
                // Get the current URL
                global $wp;
                $current_url = home_url($wp->request);
                
                // Create pagination links
                $pagination_args = array(
                    'base' => str_replace(999999999, '%#%', esc_url(get_pagenum_link(999999999))),
                    'format' => '?paged=%#%',
                    'current' => max(1, get_query_var('paged')),
                    'total' => $wp_query->max_num_pages,
                    'prev_text' => '&laquo; ' . __('Previous', 'nkhwazischool'),
                    'next_text' => __('Next', 'nkhwazischool') . ' &raquo;',
                    'mid_size' => 2,
                    'end_size' => 1,
                    'type' => 'list'
                );
                
                echo paginate_links($pagination_args);
                ?>
              </nav>
            </div>
          </div>
          <?php endif; ?>
        </div>

        <!-- put page content above, do not edit below this comment -->

      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>