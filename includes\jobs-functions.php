<?php
/**
 * Jobs Functions
 * 
 * This file contains functions for managing job postings on the Nkhwazi Primary School website.
 * It includes functions for retrieving, creating, updating, and deleting job postings.
 * 
 * <AUTHOR>
 */

/**
 * Get all active job postings
 * 
 * @return array Array of job postings
 */
function getAllActiveJobs() {
    global $conn;
    
    $sql = "SELECT * FROM jobs WHERE is_active = 1 ORDER BY created_at DESC";
    $result = $conn->query($sql);
    
    $jobs = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $jobs[] = $row;
        }
    }
    
    return $jobs;
}

/**
 * Get a specific job by ID
 * 
 * @param int $id Job ID
 * @return array|null Job data or null if not found
 */
function getJobById($id) {
    global $conn;
    
    $id = (int)$id;
    $sql = "SELECT * FROM jobs WHERE id = ? LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    
    return null;
}

/**
 * Get a specific job by reference code
 * 
 * @param string $reference Job reference code
 * @return array|null Job data or null if not found
 */
function getJobByReference($reference) {
    global $conn;
    
    $sql = "SELECT * FROM jobs WHERE reference = ? LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $reference);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    
    return null;
}

/**
 * Create a new job posting
 * 
 * @param array $jobData Job data including title, reference, excerpt, description, deadline, and how_to_apply
 * @return int|bool The ID of the newly created job or false on failure
 */
function createJob($jobData) {
    global $conn;
    
    // Validate required fields
    $requiredFields = ['title', 'reference', 'excerpt', 'description', 'deadline', 'how_to_apply'];
    foreach ($requiredFields as $field) {
        if (empty($jobData[$field])) {
            return false;
        }
    }
    
    // Validate field lengths
    if (strlen($jobData['title']) < 3 || strlen($jobData['title']) > 30) return false;
    if (strlen($jobData['reference']) < 2 || strlen($jobData['reference']) > 20) return false;
    if (strlen($jobData['excerpt']) < 100 || strlen($jobData['excerpt']) > 160) return false;
    if (strlen($jobData['deadline']) < 6 || strlen($jobData['deadline']) > 20) return false;
    if (strlen($jobData['how_to_apply']) < 5 || strlen($jobData['how_to_apply']) > 200) return false;
    
    $sql = "INSERT INTO jobs (title, reference, excerpt, description, deadline, how_to_apply) 
            VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(
        "ssssss", 
        $jobData['title'], 
        $jobData['reference'], 
        $jobData['excerpt'], 
        $jobData['description'], 
        $jobData['deadline'], 
        $jobData['how_to_apply']
    );
    
    if ($stmt->execute()) {
        return $conn->insert_id;
    }
    
    return false;
}

/**
 * Update an existing job posting
 * 
 * @param int $id Job ID
 * @param array $jobData Job data to update
 * @return bool True on success, false on failure
 */
function updateJob($id, $jobData) {
    global $conn;
    
    $id = (int)$id;
    
    // Validate required fields
    $requiredFields = ['title', 'reference', 'excerpt', 'description', 'deadline', 'how_to_apply'];
    foreach ($requiredFields as $field) {
        if (empty($jobData[$field])) {
            return false;
        }
    }
    
    // Validate field lengths
    if (strlen($jobData['title']) < 3 || strlen($jobData['title']) > 30) return false;
    if (strlen($jobData['reference']) < 2 || strlen($jobData['reference']) > 20) return false;
    if (strlen($jobData['excerpt']) < 100 || strlen($jobData['excerpt']) > 160) return false;
    if (strlen($jobData['deadline']) < 6 || strlen($jobData['deadline']) > 20) return false;
    if (strlen($jobData['how_to_apply']) < 5 || strlen($jobData['how_to_apply']) > 200) return false;
    
    $sql = "UPDATE jobs SET 
            title = ?, 
            reference = ?, 
            excerpt = ?, 
            description = ?, 
            deadline = ?, 
            how_to_apply = ? 
            WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(
        "ssssssi", 
        $jobData['title'], 
        $jobData['reference'], 
        $jobData['excerpt'], 
        $jobData['description'], 
        $jobData['deadline'], 
        $jobData['how_to_apply'],
        $id
    );
    
    return $stmt->execute();
}

/**
 * Delete a job posting
 * 
 * @param int $id Job ID
 * @return bool True on success, false on failure
 */
function deleteJob($id) {
    global $conn;
    
    $id = (int)$id;
    $sql = "DELETE FROM jobs WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    
    return $stmt->execute();
}

/**
 * Archive a job posting (mark as inactive)
 * 
 * @param int $id Job ID
 * @return bool True on success, false on failure
 */
function archiveJob($id) {
    global $conn;
    
    $id = (int)$id;
    $sql = "UPDATE jobs SET is_active = 0 WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    
    return $stmt->execute();
}

/**
 * Validate job data
 * 
 * @param array $jobData Job data to validate
 * @return array Array of error messages, empty if no errors
 */
function validateJobData($jobData) {
    $errors = [];
    
    // Title validation (3-30 characters)
    if (empty($jobData['title'])) {
        $errors['title'] = "Job title is required";
    } elseif (strlen($jobData['title']) < 3 || strlen($jobData['title']) > 30) {
        $errors['title'] = "Job title must be between 3 and 30 characters";
    }
    
    // Reference validation (2-20 characters)
    if (empty($jobData['reference'])) {
        $errors['reference'] = "Job reference is required";
    } elseif (strlen($jobData['reference']) < 2 || strlen($jobData['reference']) > 20) {
        $errors['reference'] = "Job reference must be between 2 and 20 characters";
    }
    
    // Excerpt validation (100-160 characters)
    if (empty($jobData['excerpt'])) {
        $errors['excerpt'] = "Job excerpt is required";
    } elseif (strlen($jobData['excerpt']) < 100 || strlen($jobData['excerpt']) > 160) {
        $errors['excerpt'] = "Job excerpt must be between 100 and 160 characters";
    }
    
    // Deadline validation (6-20 characters)
    if (empty($jobData['deadline'])) {
        $errors['deadline'] = "Application deadline is required";
    } elseif (strlen($jobData['deadline']) < 6 || strlen($jobData['deadline']) > 20) {
        $errors['deadline'] = "Application deadline must be between 6 and 20 characters";
    }
    
    // How to apply validation (5-200 characters)
    if (empty($jobData['how_to_apply'])) {
        $errors['how_to_apply'] = "How to apply instructions are required";
    } elseif (strlen($jobData['how_to_apply']) < 5 || strlen($jobData['how_to_apply']) > 200) {
        $errors['how_to_apply'] = "How to apply instructions must be between 5 and 200 characters";
    }
    
    // Description validation (required)
    if (empty($jobData['description'])) {
        $errors['description'] = "Job description is required";
    }
    
    return $errors;
}