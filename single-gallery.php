<?php
/**
 * Template Name: Single Gallery
 * Description: Template for displaying single photo galleries
 * Author: <PERSON>
 */

require_once('includes/header.php');
?>

<!-- MAIN -->
<main role="main">
    <!-- Content -->
    <article>
        <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
        <div class="line">
            <header class="rounded-div section background-blue background-transparent text-center"
                data-image-src="assets/img/parallax-02.jpg">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">
                    <?php 
                    // Display gallery name if available, otherwise use the post title
                    $gallery_name = get_field('gallery_name');
                    echo esc_html($gallery_name ? $gallery_name : get_the_title()); 
                    ?>
                </h1>
            </header>
        </div>

        <div class="section background-white">
            <div class="line">

                <!-- put page content below, do not edit above this comment -->
                
                <!-- Gallery Description -->
                <div class="s-12 margin-bottom-30">
                    <?php 
                    // Display gallery description
                    $gallery_description = get_field('gallery_description');
                    if ($gallery_description) {
                        echo '<p class="text-dark">' . esc_html($gallery_description) . '</p>';
                    }
                    
                    // Display the content from Gutenberg editor if any
                    the_content();
                    ?>
                </div>
                
                <!-- Gallery Section -->
                <section class="section background-white">
                    <div class="line">
                        <div class="margin">
                            <?php
                            // Get gallery images
                            $gallery_images = get_field('gallery');
                            
                            if ($gallery_images && is_array($gallery_images)) :
                                foreach ($gallery_images as $image) :
                                    // Get image details - use photo-gallery size for both full and thumbnail
                                    $full_image_url = isset($image['sizes']['photo-gallery']) ? $image['sizes']['photo-gallery'] : $image['url'];
                                    $thumb_image_url = isset($image['sizes']['photo-gallery']) ? $image['sizes']['photo-gallery'] : $image['url'];
                                    $alt_text = $image['alt'] ? $image['alt'] : get_the_title();
                                    $title = $image['title'] ? $image['title'] : get_the_title();
                                    
                                    // Check if it's a video
                                    $is_video = false;
                                    $file_type = wp_check_filetype($full_image_url);
                                    if (isset($file_type['ext']) && in_array($file_type['ext'], array('mp4', 'webm', 'ogg'))) {
                                        $is_video = true;
                                    }
                            ?>
                                <div class="s-12 m-6 l-3">
                                    <a class="image-with-hover-overlay image-hover-zoom margin-bottom" data-rel="lightcase:nkhwazi-gallery" 
                                        href="<?php echo esc_url($full_image_url); ?>" data-type="image">
                                        <div class="image-hover-overlay background-primary"> 
                                            <div class="image-hover-overlay-content text-center padding-2x">
                                                <i class="icon-magnifying icon2x text-white"></i>  
                                            </div> 
                                        </div> 
                                        <img src="<?php echo esc_url($thumb_image_url); ?>" 
                                            alt="<?php echo esc_attr($alt_text); ?>" 
                                            title="<?php echo esc_attr($title); ?>" />
                                    </a>
                                </div>
                            <?php
                                endforeach;
                            else :
                            ?>
                                <div class="s-12">
                                    <p>No images found in this gallery.</p>
                                </div>
                            <?php
                            endif;
                            ?>
                        </div>
                    </div>
                    
                    <!-- Back to Galleries Button -->
                    <div class="line text-center">
                        <div class="s-12 margin-bottom">
                            <?php
                            // Find the galleries page
                            $galleries_page = get_page_by_path('galleries');
                            $galleries_url = $galleries_page ? get_permalink($galleries_page->ID) : home_url();
                            ?>
                            <a href="<?php echo esc_url($galleries_url); ?>" class="button button-primary-stroke text-size-12 text-white text-strong margin-bottom">Back to Galleries</a>
                        </div>
                    </div>
                </section>

                <!-- put page content above, do not edit below this comment -->

            </div>
        </div>
        <?php endwhile; endif; ?>
    </article>
</main>

<?php require_once('includes/footer.php'); ?>