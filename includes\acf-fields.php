<?php
/**
 * Register ACF fields for the theme
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Check if ACF is active
if (!function_exists('acf_add_local_field_group')) {
    return;
}

/**
 * Add daily_schedule field to school_categories post type
 */
function nkhwazi_add_daily_schedule_field() {
    acf_add_local_field_group(array(
        'key' => 'group_school_category_schedule',
        'title' => 'School Category Schedule',
        'fields' => array(
            array(
                'key' => 'field_daily_schedule',
                'label' => 'Daily Schedule',
                'name' => 'daily_schedule',
                'type' => 'textarea',
                'instructions' => 'Enter the daily schedule for this school category. (10-300 characters)',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => '',
                'minlength' => 10,
                'maxlength' => 300,
                'rows' => 4,
                'new_lines' => 'wpautop',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'school_categories',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
    ));
}
add_action('acf/init', 'nkhwazi_add_daily_schedule_field');