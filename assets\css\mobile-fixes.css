/* Mobile-specific fixes */

/* Create a mobile-only logo that appears in the navigation */
.mobile-only-logo {
  display: none;
}

@media screen and (max-width: 768px) {
  /* Hide the regular logo section on mobile */
  #logo-section {
    display: none !important;
  }
  
  /* Show the mobile-only logo */
  .mobile-only-logo {
    display: flex !important;
    align-items: center;
    position: absolute;
    left: 60px; /* Space for hamburger menu */
    top: 0;
    height: 70px;
 
    padding-right: 15px;
  }
  
  /* Hide the logo when menu is open */
 .mobile-only-logo .logo {
    margin-right: 10px;
    margin-left: 10px;
    display: block !important;
  }
  
  .mobile-only-logo .logo img {
    height: 50px !important;
    width: auto !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  .mobile-only-logo h1 {
    font-size: 1.2rem;
    margin: 0;
    white-space: nowrap;
    display: block !important;
  }
  

}