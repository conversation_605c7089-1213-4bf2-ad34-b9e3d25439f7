<?php
/**
 * Template Name: Test Form
 */
get_header();
?>

<main role="main">
    <div class="line">
        <header class="rounded-div section background-blue background-transparent text-center"
            data-image-src="assets/img/parallax-02.jpg">
            <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Test Form</h1>
        </header>
    </div>
    
    <article class="s-12 m-12 l-9 center">
        <div class="section background-white">
            <h2>Simple Test Form</h2>
            
            <div id="form-message" style="display: none; padding: 15px; margin-bottom: 20px; border-radius: 4px;"></div>
            
            <form id="test-form" method="post">
                <div class="grid margin">
                    <div class="s-12 m-6 l-6">
                        <label>First Name</label>
                        <input type="text" name="fname" required>
                    </div>
                    <div class="s-12 m-6 l-6">
                        <label>Last Name</label>
                        <input type="text" name="lname" required>
                    </div>
                </div>
                
                <div class="grid margin">
                    <div class="s-12">
                        <button type="submit" class="button background-blue text-white">Submit Test</button>
                    </div>
                </div>
            </form>
        </div>
    </article>
</main>

<script>
jQuery(document).ready(function($) {
    $('#test-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        formData.append('action', 'nkhwazi_submit_application_form');
        formData.append('nonce', '<?php echo wp_create_nonce('nkhwazi_application_form_nonce'); ?>');
        
        console.log('Submitting test form...');
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Success:', response);
                
                var $message = $('#form-message');
                if (response.success) {
                    $message.html(response.data.message).css({
                        'background-color': '#d4edda',
                        'color': '#155724',
                        'border': '1px solid #c3e6cb'
                    }).show();
                    
                    $('#test-form')[0].reset();
                } else {
                    $message.html(response.data.message).css({
                        'background-color': '#f8d7da',
                        'color': '#721c24',
                        'border': '1px solid #f5c6cb'
                    }).show();
                }
            },
            error: function(xhr, status, error) {
                console.log('Error:', status, error);
                console.log('Response:', xhr.responseText);
                
                $('#form-message').html('An error occurred. Please try again later.').css({
                    'background-color': '#f8d7da',
                    'color': '#721c24',
                    'border': '1px solid #f5c6cb'
                }).show();
            }
        });
    });
});
</script>

<?php get_footer(); ?>