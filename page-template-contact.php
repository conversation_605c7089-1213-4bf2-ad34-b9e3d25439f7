<?php
/**
 * Template Name: Contact Form Page
 * Description: A page template that includes the contact form shortcode
 */

// Enqueue contact form validation script
function nkhwazi_enqueue_contact_form_validation() {
    wp_enqueue_script(
        'nkhwazi-contact-form-validation',
        get_template_directory_uri() . '/assets/js/contact-form-validation.js',
        array('jquery'),
        '1.0.0',
        true
    );
}
add_action('wp_enqueue_scripts', 'nkhwazi_enqueue_contact_form_validation');

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-06.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php the_title(); ?></h1>
      </header>
    </div>
    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <!-- Gutenberg content -->
          <div class="s-12 m-12 l-12">
            <?php
            while (have_posts()) :
                the_post();
                the_content();
            endwhile;
            ?>
          </div>
          
          <!-- Company Information -->
          <div class="s-12 m-12 l-5">
            <h2 class="text-strong">School Contact Details</h2>
            <hr class="break break-small background-primary">
            <div class="float-left">
              <i class="icon-placepin background-primary icon-circle-small text-size-20"></i>
            </div>
            <div class="margin-left-80 margin-bottom">
              <h3 class="text-strong margin-bottom-10">School Address</h3>
              <?php
              // Get school address
              $address = nkhwazi_get_school_address();
              
              if (!empty($address)) {
                  // Display address lines
                  echo '<p>';
                  if (!empty($address['line1'])) {
                      echo esc_html($address['line1']) . '<br>';
                  }
                  if (!empty($address['line2'])) {
                      echo esc_html($address['line2']);
                  }
                  echo '</p>';
                  
                  // Display phone numbers
                  if (!empty($address['phone1'])) {
                      echo '<p class="margin-left-10"><i class="icon-sli-call-in text-strong text-secondary text-size-18"></i>&nbsp; <a href="tel:' . esc_attr(preg_replace('/\s+/', '', $address['phone1'])) . '">' . esc_html($address['phone1']) . '</a></p class="margin-left-10">';
                  }
                  if (!empty($address['phone2'])) {
                      echo '<p class="margin-left-10"><i class="icon-sli-call-in text-strong text-secondary text-size-18"></i>&nbsp; <a href="tel:' . esc_attr(preg_replace('/\s+/', '', $address['phone2'])) . '">' . esc_html($address['phone2']) . '</a></p>';
                  }
                  
                  // Display email if allowed
                  if (!empty($address['email']) && !empty($address['display_email'])) {
                      echo '<p class="margin-left-10"><i class="icon-sli-envelope text-strong text-secondary text-size-18"></i>&nbsp; <a href="mailto:' . esc_attr($address['email']) . '">' . esc_html($address['email']) . '</a></p>';
                  }
              } else {
                  echo '<p class="any-error error">Address information not available.</p>';
              }
              ?>
            </div>
            <div class="float-left">
              <i class="icon-smartphone background-primary icon-circle-small text-size-20"></i>
            </div>
            <!-- Admin Contact Information -->
            <div class="margin-left-80 margin-bottom">
              <h3 class="text-strong margin-bottom-10">Contact Information</h3>
              <?php
              // Get admin contacts
              $admin_contacts = nkhwazi_get_admin_contacts();

              if (!empty($admin_contacts)) {
                  foreach ($admin_contacts as $contact) {
                      echo '<p class="margin-bottom-20">';
                      // Job Title
                      echo '<strong>' . esc_html($contact['job_title']) . '</strong><br>';
                      
                      // Phone Number - make it dialable
                      if (!empty($contact['phone'])) {
                          echo '<span class="margin-left-10"><i class="icon-sli-call-in text-strong text-secondary text-size-18"></i>&nbsp; <a href="tel:' . esc_attr(preg_replace('/\s+/', '', $contact['phone'])) . '">' . esc_html($contact['phone']) . '</a></span><br>';
                      }
                      
                      // Email Address (only if display_email is true)
                      if (!empty($contact['email']) && !empty($contact['display_email'])) {
                          echo '<span class="margin-left-10"><i class="icon-sli-envelope text-strong text-secondary text-size-18"></i>  &nbsp; <a href="mailto:' . esc_attr($contact['email']) . '">' . esc_html($contact['email']) . '</a></span>';
                      }
                      
                      echo '</p>';
                  }
              } else {
                  echo '<p class="margin-bottom-10">No contact information available.</p>';
              }
              ?>
            </div>
          </div>

          <!-- Contact Form -->
          <div class="s-12 m-12 l-7">
            <h2 class="text-strong margin-m-top-50">Contact Us</h2>
            <hr class="break break-small background-primary">
            
            <div class="margin-bottom-20">
              <p>Please fill out the form below to get in touch with us:</p>
              <ul>
                <li>Fields with a <span class='any-error'>red</span> left border are required</li>
                <li>A <span style="color: #2ecc71;">green</span> border indicates a valid entry</li>
                <li>The form will show you how many characters you have remaining</li>
                <li>Please use only letters, numbers, and basic punctuation</li>
              </ul>
            </div>
            
            <?php echo do_shortcode('[nkhwazi_contact_form]'); ?>
          </div>
        </div>
      </div>
    </div>

    <div class="line">
      <h2 class="text-dark text-strong margin-m-top-50">Where we are located</h2>

      <div class='margin-top-bottom-20'>
        <span class='hide-l hide-xl hide-xxl'><iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3846.2284300191445!2d28.294016364850272!3d-15.41821758928599!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1940f353839a7ea7%3A0x8a82f68499432955!2sNkhwazi+Primary+School%2C+Ituna+Rd%2C+Lusaka%2C+Zambia!5e0!3m2!1sen!2suk!4v1523659030624"
            width="350" height="300" frameborder="0" style="border:0" allowfullscreen>
          </iframe></span>
        <span class='hide-s hide-m'><iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3846.2284300191445!2d28.294016364850272!3d-15.41821758928599!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1940f353839a7ea7%3A0x8a82f68499432955!2sNkhwazi+Primary+School%2C+Ituna+Rd%2C+Lusaka%2C+Zambia!5e0!3m2!1sen!2suk!4v1523659030624"
            width="1280" height="450" frameborder="0" style="border:0" allowfullscreen>
          </iframe></span>
      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>