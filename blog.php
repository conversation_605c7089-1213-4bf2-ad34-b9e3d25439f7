<?php
/**
 * Template Name: Blog Listing Page
 * Template Post Type: page
 * Description: A template for displaying blog posts in a grid layout.
 * Author: <PERSON>
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-blue background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">School Blog</h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <!-- put page content below, do not edit above this comment -->
        <div class="margin">
   
          <div class="category-filter">Filter By Category</div>
          <hr class="break-alt">
            <!-- Categories navigation -->
            <div class="grid margin blog-category-nav">
              <?php
              // Get current page URL
              $current_url = get_permalink();
              
              // Add "All" option
              echo '<div class="s-6 m-4 l-2 margin-bottom-20">';
              echo '<a href="' . esc_url(add_query_arg('category', 'all', $current_url)) . '" class="blog-category-link ' . ($selected_category === 'all' ? 'active' : '') . '">All</a>';
              echo '</div>';
              
              // Define the categories we want to show
              $category_slugs = array(
                'events' => 'School Events',
                'academics' => 'Academics',
                'facilities' => 'Facilities',
                'community' => 'Community Service'
              );
              
              // Display each category
              foreach ($category_slugs as $slug => $name) {
                echo '<div class="s-6 m-4 l-2 margin-bottom-20">';
                echo '<a href="' . esc_url(add_query_arg('category', $slug, $current_url)) . '" class="blog-category-link ' . ($selected_category === $slug ? 'active' : '') . '">' . esc_html($name) . '</a>';
                echo '</div>';
                
                // Debug output for admins
                if (is_user_logged_in() && current_user_can('administrator')) {
                  // Check if this category exists
                  $cat_obj = get_term_by('slug', $slug, 'category');
                  if ($cat_obj) {
                    echo '<!-- Category exists: ' . $slug . ' (ID: ' . $cat_obj->term_id . ') -->';
                  } else {
                    echo '<!-- Category does not exist: ' . $slug . ' -->';
                  }
                }
              }
              ?>
            </div>
         
          <div class="grid margin">
            <?php
            // Get category filter from URL
            $selected_category = isset($_GET['category']) ? sanitize_text_field($_GET['category']) : 'all';
            
            // Set up query args
            $args = array(
                'post_type' => 'post',
                'posts_per_page' => 6,
                'paged' => get_query_var('paged') ? get_query_var('paged') : 1,
                'orderby' => 'date',
                'order' => 'DESC'
            );
            
            // Add category filter if not 'all'
            if ($selected_category !== 'all') {
                // Get the category by slug
                $category_obj = get_term_by('slug', $selected_category, 'category');
                
                if ($category_obj) {
                    // Use the category ID for more reliable filtering
                    $args['cat'] = $category_obj->term_id;
                    
                    // Debug output for admins
                    if (is_user_logged_in() && current_user_can('administrator')) {
                        echo '<!-- Filtering by category: ' . $selected_category . ' (ID: ' . $category_obj->term_id . ') -->';
                    }
                }
            }
            
            // Run the query
            $blog_query = new WP_Query($args);
            
            // For debugging - only visible to admins
            if (is_user_logged_in() && current_user_can('administrator')) {
                echo '<!-- Query Debug: Found posts: ' . esc_html($blog_query->found_posts) . ' -->';
                echo '<!-- Query Debug: Query vars: ' . esc_html(json_encode($blog_query->query_vars)) . ' -->';
            }
            
            if ($blog_query->have_posts()) :
                while ($blog_query->have_posts()) : $blog_query->the_post();
                    // Get post categories for filtering
                    $post_categories = get_the_category();
                    $category_slugs = array();
                    $category_names = array();
                    
                    foreach ($post_categories as $cat) {
                        $category_slugs[] = $cat->slug;
                        $category_names[$cat->slug] = $cat->name;
                    }
                    
                    // Get the first category for display
                    $category_name = !empty($post_categories) ? $post_categories[0]->name : '';
                    $category_link = !empty($post_categories) ? get_category_link($post_categories[0]->term_id) : '';
            ?>
            <?php 
            // Debug output for admins
            if (is_user_logged_in() && current_user_can('administrator')) {
                echo '<!-- Post: ' . get_the_title() . ' - Categories: ' . implode(', ', $category_slugs) . ' -->';
            }
            ?>
            <article class="s-12 m-6 l-4 margin-bottom-20 blog-post" 
                     data-categories="<?php echo esc_attr(implode(' ', $category_slugs)); ?>"
                     data-post-id="<?php echo get_the_ID(); ?>">
              <?php 
              $blog_image = get_field('blog_image');
              if ($blog_image) : 
                $blog_image_url = wp_get_attachment_image_url($blog_image['ID'], 'blog-thumbnail'); // Use the blog-thumbnail size
                if (!$blog_image_url) {
                  $blog_image_url = $blog_image['url']; // Fallback to original if size doesn't exist
                }
              ?>
              <img class="rounded-image margin-bottom blog-image" src="<?php echo esc_url($blog_image_url); ?>" alt="<?php echo esc_attr($blog_image['alt'] ? $blog_image['alt'] : get_the_title()); ?>">
              <?php endif; ?>
              
              <h3 class="text-strong text-uppercase"><a class="text-dark text-blue-hover" href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
              <div class="grid">
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12"><?php echo get_the_date(); ?></div>
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-author">
                  <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>" class="blog-author">By <?php the_author(); ?></a>
                </div>
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-category">
                  <?php if (!empty($category_name)) : ?>
                  <a href="<?php echo esc_url($category_link); ?>" class="blog-category"><?php echo esc_html($category_name); ?></a>
                  <?php endif; ?>
                </div>
              </div>
              <p><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
              <a class="text-more-info text-primary-hover margin-bottom-30" href="<?php the_permalink(); ?>">Blog Details</a>
            </article>
            <?php
                endwhile;
            else:
            ?>
            <div class="s-12 margin-bottom-20 no-posts-message">
              <?php if ($selected_category !== 'all'): ?>
                <p>No posts found in the "<?php echo esc_html(isset($category_slugs[$selected_category]) ? $category_slugs[$selected_category] : $selected_category); ?>" category.</p>
                <p><a href="<?php echo esc_url(add_query_arg('category', 'all', $current_url)); ?>" class="text-primary-hover">View all posts</a></p>
              <?php else: ?>
                <p>No blog posts found.</p>
              <?php endif; ?>
            </div>
            <?php endif; ?>
          </div><!-- Grid -->

          <!-- Pagination -->
          <div class="line text-center pagination-container">
            <div class="s-12 margin-bottom">
              <ul class="pagination">
                <?php
                $big = 999999999; // need an unlikely integer
                $current_url = get_permalink();
                
                // Create pagination base URL with category parameter if set
                if ($selected_category !== 'all') {
                    $pagination_base = add_query_arg(array(
                        'category' => $selected_category,
                        'paged' => $big
                    ), $current_url);
                } else {
                    $pagination_base = add_query_arg('paged', $big, $current_url);
                }
                
                echo paginate_links(array(
                    'base' => str_replace($big, '%#%', esc_url($pagination_base)),
                    'format' => '',
                    'current' => max(1, get_query_var('paged')),
                    'total' => $blog_query->max_num_pages,
                    'prev_text' => __('Prev', 'nkhwazischool'),
                    'next_text' => __('Next', 'nkhwazischool'),
                    'type' => 'list'
                ));
                wp_reset_postdata();
                ?>
              </ul>
            </div>
          </div>
        </div>

        <!-- put page content above, do not edit below this comment -->

      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>