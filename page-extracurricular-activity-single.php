<?php
/**
 * Template Name: Single Extracurricular Activity
 * Description: A template for displaying detailed information about a specific extracurricular activity.
 *              Features a two-column layout with activity details and gallery.
 * Author: <PERSON>
 */

require_once('includes/header.php');

// In a WordPress theme, this would be replaced with actual data from the database
// For now, we'll use static placeholder data for design purposes

// Get the activity slug from URL parameter
$activity_slug = isset($_GET['slug']) ? $_GET['slug'] : 'annual-football-tournament'; // Default to football tournament

// Static placeholder data - this would be dynamic in WordPress
$activity_name = 'Annual Football Tournament';
$header_image = 'assets/img/page-images/football-tournament-header.jpg';
$card_image = 'assets/img/activities/football-tournament-1.jpg';
$activity_scope = 'External';
$activity_excerpt = 'Our annual inter-school football tournament brings together teams from across the region to compete in a friendly but competitive environment.';
$extracurricular_type = 'Sports';
$activity_description = 'The Annual Football Tournament is one of the most anticipated events in our school calendar. It brings together teams from various schools in the region to compete in a friendly yet competitive environment. The tournament aims to promote sportsmanship, teamwork, and physical fitness among students while fostering inter-school relationships.';
$activity_location = 'School football grounds and nearby community sports fields';
$activity_date = 'July 15-20, 2025';
$activity_gallery = [
    'assets/img/gallery/running.jpg',
    'assets/img/gallery/swimming.jpg',
    'assets/img/gallery/indoor-sport.jpg',
    'assets/img/gallery/running3.jpg'
];

// Related activities data for the sidebar
$related_activities = [
    [
        'slug' => 'Technology-club',
        'name' => 'Computer Programming',
        'card_image' => 'assets/img/card-images/coding-hackathon.jpg',
        'activity_scope' => 'Internal',
        'extracurricular_type' => 'Academic Clubs'
    ],
    [
        'slug' => 'choir-competition',
        'name' => 'Choir Competition',
        'card_image' => 'assets/img/card-images/choir-competition.jpg',
        'activity_scope' => 'External',
        'extracurricular_type' => 'Music & Arts'
    ]
];

// Convert extracurricular type to slug for linking
// In WordPress, this would be handled by the CMS
$extracurricular_type_slug = strtolower(str_replace(' & ', '-and-', str_replace(' ', '-', $extracurricular_type)));
?>

<!-- MAIN -->
<main role="main">
  <!-- Header Section -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center position-relative"
        data-image-src="assets/img/parallax-02.jpg" style="padding-bottom: 40px;">
        <h1 class="text-uppercase text-white margin-bottom-20 text-thin text-line-height-1"><?php echo htmlspecialchars($activity_name); ?></h1>
        
        <!-- Breadcrumbs - Will be replaced with WordPress shortcode -->
        <div class="breadcrumbs-container">
          <div class="breadcrumbs rounded-div background-white-transparent box-shadow padding-1x">
            <span class="breadcrumb-item"><a href="index.php">Home</a></span>
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-item"><a href="page-extracurricular-activities.php">Activities</a></span>
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-item"><?php echo htmlspecialchars($activity_name); ?></span>
          </div>
        </div>
      </header>
    </div>
    
    <!-- Full-width Featured Image (only shown if image exists) -->
    <?php if (!empty($header_image)): ?>
    <div class="line margin-top-30 margin-bottom-30">
      <div class="s-12">
        <img src="<?php echo htmlspecialchars($header_image); ?>" alt="<?php echo htmlspecialchars($activity_name); ?>" class="full-width-img rounded-image">
      </div>
    </div>
    <?php endif; ?>

    <!-- Main Content Section -->
    <div class="section background-white">
      <div class="line">
        <!-- Two-column layout -->
        <div class="grid margin">
          <!-- Left Column: Activity Details -->
          <div class="s-12 m-6 l-7">
            <!-- Activity Overview -->
            <div class="margin-bottom-30">
              <h2 class="text-strong text-uppercase margin-bottom-20"><?php echo htmlspecialchars($activity_name); ?></h2>
                
              <!-- Activity Name and Key Details in White Box with Shadow -->
              <div class="background-white padding-2x rounded-div margin-bottom-20 box-shadow">
                
                <div class="grid margin">
        
                  <!-- Venue (formerly Location) -->
                  <div class="s-12 margin-bottom-10">
                    <div class="grid">
                      <div class="s-4 m-3 l-3">
                        <span class="text-size-12 text-dark">VENUE:</span>
                      </div>
                      <div class="s-8 m-9 l-9">
                        <span>
                          <i class="icon-map margin-right-5"></i> <?php echo htmlspecialchars($activity_location); ?>
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Event Date (formerly Date) -->
                  <div class="s-12 margin-bottom-10">
                    <div class="grid">
                      <div class="s-4 m-3 l-3">
                        <span class="text-size-12 text-dark">EVENT DATE:</span>
                      </div>
                      <div class="s-8 m-9 l-9">
                        <span>
                          <?php echo htmlspecialchars($activity_date); ?>
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Category -->
                  <div class="s-12">
                    <div class="grid">
                      <div class="s-4 m-3 l-3">
                        <span class="text-size-12 text-dark">CATEGORY:</span>
                      </div>
                      <div class="s-8 m-9 l-9">
                        <span>
                          
                          <a href="page-single-extracurricular.php?slug=<?php echo urlencode($extracurricular_type_slug); ?>">
                            <?php echo htmlspecialchars($extracurricular_type); ?>
                          </a>
                        </span>
                      </div>
                    </div>
                  </div>
                            <!-- Activity Scope -->
                  <div class="s-12 margin-bottom-10">
                    <div class="grid">
                      <div class="s-4 m-3 l-3">
                        <span class="text-size-12 text-dark">ACTIVITY TYPE:</span>
                      </div>
                      <div class="s-8 m-9 l-9">
                        <span>
                          <!--vale of scope should be either 'external' = Outside School or 'internal' = inside school-->
                           Outside School
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Main Content Area (will be replaced by the_content in WordPress) -->
              <div class="activity-content margin-top-30">
                <!-- This div will be replaced by the Gutenberg editor content in WordPress -->
                <p><?php echo htmlspecialchars($activity_description); ?></p>
                <p class="margin-top-15"><?php echo htmlspecialchars($activity_excerpt); ?></p>
              </div>
              
              <!-- Action Buttons -->
         <div class="s-12 m-12 l-12 margin-top-30">
                  <a class="s-12 m-6 l-4 center button rounded-btn s-12 margin-bottom text-white background-blue" href="#">Back to All Activities</a>
                </div>
            </div>
          </div>
          
          <!-- Right Column: Gallery -->
          <div class="s-12 m-6 l-5 text-center">
            <!-- Gallery Section -->
            <div class="margin-bottom-30">
              <h2 class="text-padding-small rounded-div background-primary text-white text-strong text-uppercase margin-bottom-20">
                Activity Gallery
              </h2>
              
              <?php if (!empty($activity_gallery)): ?>
              <div class="grid margin">
                <?php foreach ($activity_gallery as $index => $image): ?>
                <div class="s-12 m-6 l-6 margin-m-bottom">
                  <a href="<?php echo htmlspecialchars($image); ?>" class="lightbox">
                    <img src="<?php echo htmlspecialchars($image); ?>" alt="<?php echo htmlspecialchars($activity_name); ?> Image <?php echo $index + 1; ?>" class="rounded-image full-img">
                  </a>
                </div>
                <?php endforeach; ?>
              </div>
              <?php else: ?>
              <p class="text-center">No gallery images available for this activity.</p>
              <?php endif; ?>
            </div>
            
            <!-- Related Activities -->
            <div>
              <h3 class="margin-bottom-30 margin-top-20 text-strong text-center">Related Activities</h3>
              
              <div class="activities-list">
                <?php foreach ($related_activities as $related_activity): ?>
                <div class="activity-item margin-bottom-15">
                  <!-- Activity Card (Clickable) -->
                  <a href="page-extracurricular-activity-single.php?slug=<?php echo urlencode($related_activity['slug']); ?>" class="activity-link">
                    <div class="activity-header background-grey-hover cursor-pointer">
                      <div class="grid margin">
                        <div class="s-5 m-5 l-5">
                          <img src="<?php echo htmlspecialchars($related_activity['card_image']); ?>" alt="<?php echo htmlspecialchars($related_activity['name']); ?>" class="activity-thumbnail">
                        </div>
                        <div class="s-7 m-7 l-7">
                          <h4 class="text-strong"><?php echo htmlspecialchars($related_activity['name']); ?></h4>
                          <span class="text-size-12 m-12 l-12 text-grey">Click to view details</span>
                        </div>
                      </div>
                    </div>
                  </a>
                </div>
                <?php endforeach; ?>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </article>
</main>

<!-- JavaScript for activity links -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Make entire activity card clickable
  const activityItems = document.querySelectorAll('.activity-item');
  
  activityItems.forEach(item => {
    item.addEventListener('click', function(e) {
      // Find the link inside this activity item
      const link = this.querySelector('.activity-link');
      if (link) {
        // Navigate to the link's href
        window.location.href = link.getAttribute('href');
      }
    });
  });
});
</script>

<?php require_once('includes/footer.php'); ?>