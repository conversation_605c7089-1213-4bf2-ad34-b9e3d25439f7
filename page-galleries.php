<?php
/**
 * Template Name: Galleries
 * Description: A full-width layout template with no sidebar. Ideal for content that requires the entire horizontal space, such as image galleries or staff listings.
 * Author: <PERSON>
 */

require_once('includes/header.php');
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-blue background-transparent text-center"
        data-image-src="assets/img/parallax-02.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">
          <?php the_title(); ?>
        </h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">

        <!-- put page content below, do not edit above this comment -->

        <?php 
        // Display the Gutenberg content at the top of the page
        while (have_posts()) :
            the_post();
            the_content();
        endwhile;
        ?>
        
        <div class="grid margin">
          <?php
          // Query for photo galleries
          $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
          $args = array(
              'post_type' => 'photo_gallery',
              'posts_per_page' => 9,
              'paged' => $paged,
          );
          
          $gallery_query = new WP_Query($args);
          
          if ($gallery_query->have_posts()) :
              while ($gallery_query->have_posts()) : $gallery_query->the_post();
                  // Get the gallery card image
                  $gallery_card = get_field('gallery_card');
                  $thumbnail_url = '';
                  $alt_text = get_the_title();
                  
                  if ($gallery_card && isset($gallery_card['sizes']['gallery-card'])) {
                      $thumbnail_url = $gallery_card['sizes']['gallery-card'];
                      $alt_text = $gallery_card['alt'] ? $gallery_card['alt'] : $alt_text;
                  } else {
                      // Fallback: Get the first image from the gallery to use as the thumbnail
                      $gallery_images = get_field('gallery');
                      
                      if ($gallery_images && is_array($gallery_images)) {
                          $first_image = reset($gallery_images);
                          if (isset($first_image['sizes']['gallery-card'])) {
                              $thumbnail_url = $first_image['sizes']['gallery-card'];
                              $alt_text = $first_image['alt'] ? $first_image['alt'] : $alt_text;
                          } elseif (isset($first_image['sizes']['medium'])) {
                              $thumbnail_url = $first_image['sizes']['medium'];
                              $alt_text = $first_image['alt'] ? $first_image['alt'] : $alt_text;
                          } elseif (isset($first_image['url'])) {
                              $thumbnail_url = $first_image['url'];
                          }
                      }
                  }
                  
                  // If no gallery images, use a placeholder
                  if (empty($thumbnail_url)) {
                      $thumbnail_url = get_template_directory_uri() . '/assets/img/placeholder.jpg';
                  }
                  
                  // Get gallery description
                  $gallery_description = get_field('gallery_description');
                  if (empty($gallery_description)) {
                      $gallery_description = wp_trim_words(get_the_content(), 20, '...');
                  }
                  
                  // Get gallery name
                  $gallery_name = get_field('gallery_name');
                  if (empty($gallery_name)) {
                      $gallery_name = get_the_title();
                  }
          ?>
              <div class="s-12 m-4 l-4 margin-m-bottom blog-post gallery-card">
                <span class="gallery-label">Photo Gallery</span>
                <a href="<?php the_permalink(); ?>">
                  <img class="rounded-image blog-image" src="<?php echo esc_url($thumbnail_url); ?>" alt="<?php echo esc_attr($alt_text); ?>">
                </a>
                <div class="gallery-content">
                  <h3 class="text-strong">
                    <a class="text-dark text-orange-hover" href="<?php the_permalink(); ?>"><?php echo esc_html($gallery_name); ?></a>
                  </h3>
                  <div class="s-12 m-12 l-12">
                    <p class="margin-bottom text-dark"><?php echo esc_html($gallery_description); ?></p>
                    <a class="text-more-info text-primary-hover margin-bottom-30" href="<?php the_permalink(); ?>">View Gallery</a>
                  </div>
                </div>
              </div>
          <?php
              endwhile;
          else :
          ?>
              <div class="s-12 margin-m-bottom">
                <p>No galleries found. Please check back later.</p>
              </div>
          <?php
          endif;
          ?>
        </div>

        <?php if ($gallery_query->max_num_pages > 1) : ?>
        <!-- Pagination -->
        <div class="line text-center pagination-container">
          <div class="s-12 margin-bottom">
            <ul class="pagination">
              <?php
              // Previous page
              if ($paged > 1) :
                  echo '<li><a href="' . esc_url(get_pagenum_link($paged - 1)) . '" class="previous-page">Prev</a></li>';
              endif;
              
              // Page numbers
              for ($i = 1; $i <= $gallery_query->max_num_pages; $i++) :
                  $active_class = ($i == $paged) ? 'active-page' : '';
                  echo '<li><a href="' . esc_url(get_pagenum_link($i)) . '" class="' . $active_class . '">' . $i . '</a></li>';
              endfor;
              
              // Next page
              if ($paged < $gallery_query->max_num_pages) :
                  echo '<li><a href="' . esc_url(get_pagenum_link($paged + 1)) . '" class="next-page">Next</a></li>';
              endif;
              ?>
            </ul>
          </div>
        </div>
        <?php 
        endif;
        wp_reset_postdata();
        ?>

        <!-- put page content above, do not edit below this comment -->

      </div>
    </div>
  </article>
</main>

<?php require_once('includes/footer.php'); ?>
