/*
 * Custom styles for Nkhwazi Primary School website
 */

/* Form progress steps */
.form-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
    
    /* Remove any secondary color line */
    &::after {
        display: none !important;
    }
}

/* Additional rule to target any potential secondary color line */
.form-progress::after {
    display: none !important;
}

.form-progress-step {
    position: relative;
    z-index: 2;
    text-align: center;
    transition: all 0.3s ease;
    
    &.clickable {
        cursor: pointer;
        
        &:hover .form-progress-step-number {
            background-color: #3649e2;
            color: white;
            transform: scale(1.1);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        &:hover .form-progress-step-label {
            color: #3649e2;
            font-weight: bold;
        }
    }
    
    &.active .form-progress-step-number {
        background-color: #3649e2;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    &.active .form-progress-step-label {
        color: #3649e2;
        font-weight: bold;
    }
    
    &.completed .form-progress-step-number {
        background-color: #2ecc71;
        color: white;
    }
}

/* The form-progress-step-number styles are defined in application-form.css */

.form-progress-step-label {
    font-size: 14px;
    color: #666;
}

/* Form steps */
.form-step {
    display: none !important;
    opacity: 0;
    transition: opacity 0.3s ease;
    
    &.active {
        display: block !important;
        opacity: 1;
    }
   
}

/*
//Ubuntu styles
.ubuntu-light {
  font-family: "Ubuntu", sans-serif;
  font-weight: 300;
  font-style: normal;
}

.ubuntu-regular {
  font-family: "Ubuntu", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.ubuntu-medium {
  font-family: "Ubuntu", sans-serif;
  font-weight: 500;
  font-style: normal;
}

.ubuntu-bold {
  font-family: "Ubuntu", sans-serif;
  font-weight: 700;
  font-style: normal;
}

.ubuntu-light-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 300;
  font-style: italic;
}

.ubuntu-regular-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 400;
  font-style: italic;
}

.ubuntu-medium-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 500;
  font-style: italic;
}

.ubuntu-bold-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 700;
  font-style: italic;
}

*/

/* Noto serif styles */
/**
 *
.noto-serif-georgian-<uniquifier> {
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: <weight>;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}
 */

/**
  * HEX Code: #3649e2
  *RGB Code: rgb(54,73,226)
   complementary color: #E2CF36 (rgb(226, 207, 54))
 */

$primary-color: rgb(54, 73, 226);
$secondary-color: rgb(226, 207, 54);
$secondary-color-darker: rgb(203, 186, 48);
$body-text: #222;
$link-color: #0000ff;
$visited-link-color: #800080;
$hover-link-color: #ff0000;
$active-link-color: #ff00ff;
$white-color: #ffffff;
$grey-color: #cccccc;
$light-grey-color: #eeeeee;
$button-hover: #e2cf36;

.text-yellow-darker {
  color: $secondary-color-darker !important;
}

.text-primary {
  color: $primary-color !important;
}

/*General Styles*/
body,
p {
  font-family: "ubuntu", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 1.0rem;
  text-align: left;
}
/*** Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: $secondary-color;
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-weight: normal;
  line-height: 1.3;
  font-variation-settings: "wdth" 100;
  margin: 0.5rem 0;
  text-align: left;
}
h1 {
  font-size: 2.2rem;
  text-align: center;
}
h2 {
  font-size: 1.6rem;
}
h3 {
  font-size: 1.3rem;
}
h4 {
  font-size: 1.1rem;
}

/*** Lists */
/**targeting ONLY content within the main part of the page */
main li {
  font-size: 1.0rem;
  list-style-type: disc;
}
main ul, main ol {
  margin-top: 1.0rem;
  margin-bottom: 1.0rem;
  margin-left: 1.0rem;
}

.material-symbols-outlined {
  font-family: "Material Symbols Outlined", sans-serif;
  font-weight: 300 !important;
  font-style: normal;
  font-size: 24px !important; /* Adjust size as needed */
  display: inline-block;
  vertical-align: middle !important;
  line-height: 1;
  color: $secondary-color !important;
}

.material-symbols-outlined:hover {
  color: $button-hover !important;
}

.material-symbols-outlined-alt {
  font-family: "Material Symbols Outlined", sans-serif;
  font-weight: 400 !important;
  font-style: normal;
  font-size: 48px !important; /* Adjust size as needed */
  display: inline-block;
  vertical-align: middle !important;
  line-height: 1;
  color: $secondary-color-darker !important;
}

/**Header styles */
.website-name {
  color: $primary-color !important;
}

.rounded-div {
  border-radius: 10px;
  overflow: hidden;
  /* Add word breaking properties to prevent text overflow */
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

/* Management sidebar specific styles */
.management-sidebar a {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  display: block;
  width: 100%;
}

/* Logo and school name container */
.nav-text {
  background: none repeat scroll 0 0 $primary-color;
}
.logo-container {
  display: flex;
  align-items: left;
  justify-content: center;
  flex-wrap: wrap;
}

.logo-container .logo {
  margin-right: 15px;
}

.logo-container h1 {
  margin: 0;
  color: $primary-color;
  font-size: 2.2rem;
  text-align: left;
  margin-top: 30px;
  font-weight: 600;
}

/* Centered menu styles */
.centered-menu {
  display: flex;
  justify-content: center; /* Changed from left to center */
  flex-wrap: wrap;
  float: none !important;
}

.centered-menu > li {
  float: none !important;
  display: inline-block;
}

.social-bar-footer {
  border-bottom: 2px solid rgba(54, 73, 226, 0.7);
}

/** Event styles */
.event-date {
  margin-top: 10px;
  background-color: $secondary-color;
  color: $white-color;
  padding: 6px 10px;
}

.event-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: all 0.3s ease;
  height: 100%;

  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-5px);
  }
}

.category-padding {
  padding: 15px 10px !important;
}

/* Reusable image styles */

.rounded-image-top {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
}
.rounded-image {
  border-radius: 10px;
  overflow: hidden;
  
  img {
    border-radius: 10px;
    overflow: hidden;
  }
}
.full-img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.full-width-img {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  margin: 0 auto;
  max-width: 1720px;
}



/* Extracurricular page styles */
.extracurricular-summary {
  background-color: #f8f8f8;
  border-left: 4px solid $primary-color;
  padding: 20px;
  margin-bottom: 30px;
  border-radius: 0 10px 10px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  
  p {
    margin-bottom: 10px;
    font-size: 1.1rem;
    line-height: 1.5;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    strong {
      color: $primary-color;
      font-weight: 600;
    }
  }
}

/* Single Extracurricular Activity Styles */
.background-primary-hightlight {
  background-color: lighten($primary-color, 45%);
  padding: 20px;
  border-radius: 10px;
  
  p {
    font-size: 1.1rem;
    line-height: 1.5;
    font-weight: 500;
    color: $body-text;
  }
  
  .text-dark {
    font-weight: 600;
    color: darken($body-text, 15%);
  }
  
  a {
    color: $primary-color;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

/* Extracurricular Item Page Image */
.extracurricular-page-image {
  width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: cover;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Activity items on extracurricular pages */
.activities-list {
  .activity-item {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-3px);
    }
    
    .activity-header {
      border-radius: 8px;
      overflow: hidden;
      padding: 10px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }
    
    .activity-thumbnail {
      width: 100%;
      height: auto;
      border-radius: 5px;
      object-fit: cover;
      aspect-ratio: 16/9;
    }
    
    h4 {
      margin-bottom: 5px;
      font-size: 1.1rem;
      line-height: 1.3;
    }
    
    p {
      margin: 0 0 5px 0;
    }
  }
}

/* Back button for extracurricular pages */
.button.background-primary {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 30px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid $primary-color;
  
  &:hover {
    background-color: transparent !important;
    color: $primary-color !important;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}


/* Blog author and category links */
.blog-author,
.blog-category {
  color: $link-color !important;

  &:hover {
    text-decoration: underline;
  }
}

/* About Nkhwazi Primary School section styles */
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:first-child {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  text-align: left;

  h2,
  p,
  a {
    text-align: left;
  }
}

/* Apply rounded corners to the About Nkhwazi Primary School image */
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img {
  @extend .rounded-image;
}

.nkhwazi-stats {
  background-color: #eaeaea;
  padding: 0.8rem;
  border-radius: 10px;
}

.number-stat {
  border: 1px solid $white-color;
  background-color: #f2f2f2;
  border-radius: 10px;
}

/** Links */

/* Font colors */
.background-white,
.background-white p,
a.background-white,
.background-white a,
.background-white a:active {
  color: #444;
}
.background-white a:link {
  color: $link-color;
}

.background-white a:visited {
  color: $visited-link-color;
}

.background-white a:hover {
  color: $hover-link-color;
}
.category-filter {
  color: $body-text;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

/* Teacher filter section */
.teacher-filter-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 20px;

  .button {
    margin: 5px;
    transition: all 0.3s ease;
  }
}

.teacher-filter-btn {
  background-color: white !important;
  color: black !important;
  border: 1px solid black !important;
  border-radius: 5px !important;
  padding: 8px 15px !important;
}

.teacher-filter-buttons .button {
  &.active {
    background-color: $primary-color !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  &:hover {
    background-color: $primary-color !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

.background-grey {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.box-shadow {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }
}

/* Breadcrumbs Styling */
.position-relative {
  position: relative;
}

.breadcrumbs-container {
  position: absolute;
  bottom: 5px;  /* Changed from -15px to keep it inside the header */
  left: 10px;
  z-index: 10;
}

.background-white-transparent {
  background-color: rgba(255, 255, 255, 0.85); /* Semi-transparent white */
}

.breadcrumbs {
  font-size: 0.8rem;
  text-align: left;
  
  .breadcrumb-item {
    display: inline-block;
    
    a {
      color: $link-color;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
        color: $hover-link-color;
      }
    }
  }
  
  .breadcrumb-separator {
    display: inline-block;
    margin: 0 5px;
    color: $grey-color;
  }
}

.padding-1x {
  padding: 10px 15px;
}

.padding-2x {
  padding: 20px 25px;
}

/* Gallery thumbnails in single-gallery.php */
.image-with-hover-overlay img {
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-with-hover-overlay {
  border-radius: 10px;
  overflow: hidden;
}

/* Responsive adjustments for breadcrumbs */
@media screen and (min-width: 992px) {
  /* Adjustments for large screens */
  .breadcrumbs-container {
    bottom: 15px;
  }
}

@media screen and (max-width: 768px) {
  /* Adjustments for small screens */
  .breadcrumbs-container {
    position: relative;
    bottom: auto;
  }
}

/* Lightcase Gallery Fixes */
/* Fix for iframe scrollbars in lightcase galleries */
#lightcase-content {
  overflow: hidden !important;
}

#lightcase-content .lightcase-contentInner {
  overflow: hidden !important;
}

/* Application Form Field with Counter Styles */
.field-with-counter {
  position: relative;
  margin-bottom: 20px; /* Add space between fields */
  width: 100%;
  
  input, textarea, select {
    width: 100%;
    margin-bottom: 0 !important; /* Remove default bottom margin */
  }
}

/* Application Form Character Counter Styles */
.form-header {
 background-color: $white-color;
 clear: both;
}
.character-counter {
  font-size: 0.8rem;
  text-align: right;
  margin-top: 5px;
  color: #666;
  transition: all 0.3s ease;
  padding: 2px 5px;
  border-radius: 3px;
  display: block;
  
  &.warning {
    color: #e2a736;
    font-weight: bold;
    background-color: rgba(226, 167, 54, 0.1);
  }
  
  &.error {
    color: #e74c3c;
    font-weight: bold;
    background-color: rgba(231, 76, 60, 0.1);
  }
}

.field-error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 5px;
  display: block;
  
  /* When inside a field-with-counter */
  .field-with-counter & {
    margin-bottom: 5px;
  }
}

/* Form field validation styles */
input.error, 
textarea.error, 
select.error {
  border-color: #e74c3c !important;
  border-left-width: 3px !important;
}

input.valid, 
textarea.valid, 
select.valid {
  border-color: #2ecc71 !important;
  border-left-width: 3px !important;
}

/* Ensure proper sizing for lightcase content */
#lightcase-case {
  max-width: 100vw !important;
  max-height: 100vh !important;
}

@media screen and (max-width: 768px) {
  /* Adjustments for small screens */
  .breadcrumbs-container {
    left: auto;
    margin-top: 10px;
    margin-bottom: -25px;
  }
  
  .breadcrumbs {
    font-size: 0.7rem;
    width: 100%;
  }
}

.background-white a:active {
  color: $active-link-color;
}

/* Blog Styles */

/* Blog post metadata container */
.blog-meta-container {
  background-color: $white-color;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  
  .meta-item {
    font-size: 0.95rem;
    
    &.date {
      color: $body-text;
    }
    
    &.author, &.category {
      a {
        color: $link-color;
        
        &:hover {
          color: $hover-link-color;
          text-decoration: underline;
        }
        
        .text-dark {
          color: $body-text;
          font-weight: normal;
        }
      }
    }
  }
  
  /* Responsive adjustments */
  @media screen and (max-width: 768px) {
    .meta-item {
      font-size: 0.9rem;
    }
  }
  
  @media screen and (max-width: 480px) {
    flex-direction: column;
    
    .meta-item {
      width: 100%;
      margin-bottom: 5px;
      text-align: center !important;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}



/* Blog Category Navigation - Horizontal */
.blog-categories-horizontal {
  margin-bottom: 30px;
  
  .blog-categories-menu {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%; /* Ensure full width */
    box-sizing: border-box; /* Include padding in width calculation */
    
    li {
      margin: 0 10px 10px 0;
      list-style-type: none;
      box-sizing: border-box; /* Include padding in width calculation */
    }
    
    .blog-category-link {
      display: inline-block;
      padding: 8px 12px;
      background-color: #f5f5f5;
      border-radius: 5px;
      text-align: center;
      transition: all 0.3s ease;
      color: $body-text;
      font-weight: 500;
      cursor: pointer;
      width: 100%; /* Ensure full width */
      box-sizing: border-box; /* Include padding in width calculation */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis; /* Add ellipsis for long category names */
      
      &:hover {
        background-color: #e0e0e0;
        color: $link-color;
        text-decoration: none;
      }
      
      &.active {
        background-color: $primary-color;
        color: white;
        
        &:hover {
          background-color: darken($primary-color, 10%);
          text-decoration: none;
        }
      }
    }
  }
  
  /* Responsive adjustments */
  /* Medium screens - 2 per line */
  @media screen and (max-width: 768px) and (min-width: 481px) {
    .blog-categories-menu {
      display: flex;
      flex-wrap: wrap;
      
      li {
        width: calc(50% - 10px); /* 2 per line with some margin */
        margin-right: 10px;
        margin-bottom: 10px;
        
        &:nth-child(2n) {
          margin-right: 0; /* Remove right margin for every second item */
        }
      }
      
      .blog-category-link {
        display: block;
        width: 100%;
      }
    }
  }
  
  /* Small screens - stacked */
  @media screen and (max-width: 480px) {
    .blog-categories-menu {
      flex-direction: column;
      
      li {
        margin-right: 0;
        width: 100%;
      }
      
      .blog-category-link {
        display: block;
        width: 100%;
      }
    }
  }
}

.category-filter {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: $body-text;
}

.no-posts-message {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 5px;
  text-align: center;
  margin-top: 20px;
  
  p {
    margin: 0;
    color: $body-text;
  }
}

/* Blog Images with Rounded Corners */
.single-post article img,
.blog article img,
.blog-sidebar img,
.s-12.m-4.l-4 img,
.s-12.m-12.l-12 img,
.wp-block-image img {
  border-radius: 10px !important;
  overflow: hidden !important;
}

/* Blog Home Button */
.rounded-btn {
  border-radius: 30px;
  padding: 10px 20px;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;
  
  &:hover {
    background-color: darken($primary-color, 10%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

/* Blog Home Link in content area */
.blog-home-link-container {
  border-top: 1px solid $light-grey-color;
  padding-top: 20px;
  
  a {
    font-size: 1.1rem;
    font-weight: 500;
    display: inline-block;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateX(-5px);
      text-decoration: underline;
    }
    
    i {
      transition: all 0.3s ease;
    }
    
    &:hover i {
      transform: translateX(-3px);
    }
  }
}

/* Blog Category Navigation */
.blog-category-nav {
  margin-bottom: 20px !important;

  .category-link, .blog-category-link {
    color: $link-color;
    text-decoration: none;
    padding: 10px 5px !important;
    transition: all 0.3s ease;
    display: inline-block;

    &:hover {
      color: $hover-link-color;
    }

    &.active {
      font-weight: bold;
      border-bottom: 2px solid $link-color;
    }
  }
}

/* School Categories Styles */
.school-category {
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 0;
  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }
  
  img {
    width: 100%;
    height: auto;
    object-fit: cover;
    margin-bottom: 0 !important;
    border-radius: 10px 10px 0 0;
  }
  
  h3 {
    margin-top: 15px;
    text-align: center;
    padding: 0 15px;
  }
  
  p {
    flex-grow: 1;
    padding: 0 15px;
  }
  
  .text-more-info {
    display: inline-block;
    margin: 15px auto;
    text-align: center;
    width: auto;
    background-color: $primary-color;
    color: white !important;
    padding: 8px 20px;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: darken($primary-color, 10%);
      color: white !important;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

/* Category Info Box Styles */
.category-info-box {
  border: 1px solid #eaeaea;
  margin-bottom: 20px;
  
  h3 {
    color: $primary-color;
    font-size: 1.2rem;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 5px;
  }
  
  p {
    margin-bottom: 10px;
  }
}

/* Category Sidebar Styles */
.category-sidebar-item {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* Single Extracurricular Page Styles */
.activity-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  }
}

.activity-link {
  display: block;
  text-decoration: none;
  color: inherit;
  
  &:hover {
    text-decoration: none;
    color: inherit;
  }
}

/* Activity Single Page Styles */
.activity-content {
  line-height: 1.6;
  
  p {
    margin-bottom: 1rem;
  }
  
  h3, h4 {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }
  
  ul, ol {
    margin-left: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  img {
    max-width: 100%;
  }
}

/* 404 Error Page Styles */
.error-404 {
  text-align: center;
  
  .error-icon {
    font-size: 120px;
    color: $secondary-color-darker;
    margin-bottom: 20px;
  }
  
  .error-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: $primary-color;
  }
  
  .error-description {
    font-size: 1.2rem;
    margin-bottom: 30px;
  }
  
  .error-actions {
    margin-top: 30px;
    
    .text-more-info {
      margin: 0 10px;
    }
  }
  
  .search-form {
    max-width: 500px;
    margin: 0 auto;
    
    input[type="search"] {
      border: 1px solid $grey-color;
      border-radius: 5px;
      padding: 10px 15px;
      width: 70%;
    }
    
    button {
      background-color: $primary-color;
      color: white;
      border: none;
      border-radius: 5px;
      padding: 10px 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: darken($primary-color, 10%);
      }
    }
  }
}

/* Job Listings and Job Detail Page Styles */
/* Compatible with WordPress custom post types */
.job-card, 
.type-job, /* WordPress post_class() for job custom post type */
.hentry.job /* WordPress post_class() alternative */ {
  transition: all 0.3s ease;
  
  .job-card-inner,
  .entry-content-wrap /* WordPress common content wrapper class */ {
    border-radius: 10px;
    padding: 20px;
    background-color: #ffffff; /* Ensure white background */
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    
    &:hover {
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
      transform: translateY(-3px);
    }
  }
  
  h3, 
  .entry-title /* WordPress standard title class */ {
    margin-top: 0;
    
    a {
      color: inherit;
      text-decoration: none;
      
      &:hover {
        text-decoration: none;
      }
    }
  }
}

.job-meta {
  background-color: rgba(54, 73, 226, 0.05);
  padding: 10px;
  border-radius: 5px;
}

.job-excerpt {
  font-style: italic;
  border-left: 3px solid $primary-color;
  padding-left: 15px;
}

.job-description,
.entry-content /* WordPress standard content class */ {
  h3 {
    margin-top: 25px;
    margin-bottom: 10px;
    color: $primary-color;
  }
  
  ul, ol {
    margin-left: 20px;
  }
  
  /* WordPress image alignment classes */
  img.alignright {
    float: right;
    margin: 0 0 1em 1em;
  }
  
  img.alignleft {
    float: left;
    margin: 0 1em 1em 0;
  }
  
  img.aligncenter {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
  
  .alignright {
    float: right;
    margin: 0 0 1em 1em;
  }
  
  .alignleft {
    float: left;
    margin: 0 1em 1em 0;
  }
  
  .aligncenter {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
}

.job-application {
  background-color: $light-grey-color;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid $secondary-color;
}

.job-actions {
  margin-top: 20px;
  
  .button {
    margin-right: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

.activity-header {
  padding: 10px;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #f0f0f0;
  }
}

.activity-thumbnail {
  border-radius: 5px;
}

.activity-details {
  border-top: 1px solid #e0e0e0;
  border-radius: 0 0 8px 8px;
}

.cursor-pointer {
  cursor: pointer;
}

.background-light-grey {
  background-color: $light-grey-color;
  border-radius: 8px;
}

.category-sidebar-image {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 8px 8px 0 0;
}

.category-sidebar-title {
  padding: 10px;
  text-align: center;
  font-weight: 500;
  color: $body-text;
  background-color: #f8f8f8;
}

.category-sidebar-link {
  text-decoration: none;
  color: inherit;
  
  &:hover {
    text-decoration: none;
  }
}

/* Blog Post Styling */
.blog-post {
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 15px;
  border-radius: 10px;
  position: relative;
}
.blog-image {
  margin-top: 8px;
}
.blog-post:hover {
  transform: translateY(-5px);
}

/* Gallery card specific styling */
.gallery-card {
  padding: 0 0 0 0 !important; /* Remove all padding for gallery cards */
  background-color: #ffffff !important; /* Apply white background to entire card */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important; /* Add shadow to entire card */
  overflow: hidden; /* Ensure content doesn't overflow */
  transition: all 0.3s ease;
  margin-bottom: 15px !important; /* Space between the info button and the bottom edge of the card*/
  border-radius: 10px !important; /* Round all corners of the card */
}

.gallery-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15) !important; /* Enhanced shadow on hover */
}

/* Team Card Styling */
.card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 0; /* Changed from 5px to remove extra space */
  padding: 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  /* Ensure no internal spacing that might create gaps */
  box-sizing: border-box;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }

  .team-photo {
    width: 100%;
    display: block;
    transition: all 0.3s ease;
    margin: 0;
    padding: 0;
  }

  h3 {
    margin-top: 15px;
    font-size: 1.3rem;
  }

  /* Content wrapper for all text and buttons */
  h3, .title, .read-all {
    padding-left: 15px;
    padding-right: 15px;
    margin-top: 15px; /* Add space between image and text */
  }

  .team-link {
    color: $primary-color;
    text-decoration: none;

    transition: color 0.3s ease;

    &:hover {
      color: darken($primary-color, 10%);
    }
  }

  .title {
    color: #222;
    font-size: 1.1rem !important;
    font-weight: 300;
    margin: 5px 0 5px 0;
    padding: 0 10px;
  }

  /* Remove spacer that was pushing content apart */
  &:after {
    display: none; /* Hide the spacer completely */
  }
  .read-all {
    display: inline-block;
    margin: 5px 0 0; /* Removed bottom margin completely */
    padding: 8px 20px;
    background-color: $primary-color;
    color: white;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background-color: darken($primary-color, 10%);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

/* Fix for modal content */
.modal {
  border-radius: 12px;
  max-width: 800px;
  margin: 0 auto;

  h2, h3 {
    color: $primary-color;
  }

  .margin-bottom.padding {
    padding: 20px 30px;
  }

  .modal-close-button {
    background-color: $primary-color;
    color: white;
    border-radius: 30px;
    padding: 8px 20px;
    display: inline-block;
    margin: 0 auto 20px;
    transition: all 0.3s ease;

    &:hover {
      background-color: darken($primary-color, 10%);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

/* Gallery label styling */
.gallery-label {
  position: absolute;
  top: 25px;
  right: 15px;
  background-color: $primary-color;
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  z-index: 2;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Extracurricular label styling */
.extracurricular-label {
  background-color: $secondary-color;
  color: #333;
  font-weight: 600;
}

/* Teacher category header styling */
.teacher-category-header {
  background-color: $primary-color;
  color: white;
  padding: 10px 0;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  width: 100%;
  border-radius: 10px 10px 0 0;
  margin: 0;
}

/* Teaching staff card specific styling */
.card.gallery-card[data-category] {
  .gallery-content {
    padding: 15px;
    padding-bottom: 0; /* Removed bottom padding */
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    h3 {
      margin-top: 10px;
      margin-bottom: 5px;
    }

    .title {
      margin-bottom: 8px; /* Reduced bottom margin */
    }

    .read-all {
      display: inline-block;
      margin-bottom: 0; /* Removed bottom margin */
      margin-top: auto; /* Push the button to the bottom of the flex container */
    }
  }

  /* Team photo with sharp corners */
  .team-photo {
    width: 100%;
    display: block;
    margin: 0;
    padding: 0;
    border-radius: 0 !important; /* Sharp corners */
  }
}

/* Management page specific styles */
.page-management {
  .gallery-card {
    padding-bottom: 0 !important;

    .gallery-content {
      padding-bottom: 0 !important;

      div {
        margin-bottom: 0 !important;
      }

      .read-all {
        margin-bottom: 0 !important;
      }
    }
  }
}

/* Gallery image hover effect */
.gallery-card a img.rounded-image,
.gallery-card a .team-photo.rounded-image {
  transition: all 0.3s ease;
  border-radius: 10px 10px 0 0 !important; /* Rounded top corners only for gallery images */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add shadow to the image */
  width: 100%; /* Ensure image takes full width */
  display: block; /* Remove any extra space */
}

.gallery-card a:hover img.rounded-image,
.gallery-card a:hover .team-photo.rounded-image {
  opacity: 0.85;
  transform: scale(1.02);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Gallery card text content */
.gallery-content {
  padding: 15px 15px 0px; /* Removed bottom padding to fix extra space */
  margin-top: 0;
  margin-bottom: 0; /* Added to remove bottom margin */

  h3 {
    margin-top: 0;
    margin-bottom: 8px; /* Reduced bottom margin */
  }

  p {
    margin-bottom: 8px; /* Reduced bottom margin */
  }

  .text-more-info {
    display: inline-block;
    margin-bottom: 0; /* Removed bottom margin */
  }

  /* Fix for the container of the View Bio button */
  div {
    margin-bottom: 0 !important; /* Remove bottom margin from button container */
  }
}

.blog-post img.rounded-image {
  width: 100%;
  border-radius: 5px;
}

.blog-post h3 {
  margin-top: 15px;
  font-size: 1.3rem;
  line-height: 1.3;
}

.blog-post p {
  margin: 10px 0;
  line-height: 1.5;
}

.blog-author a,
.blog-category a {
  color: #555;
  text-decoration: none;
  transition: color 0.3s ease;
}

.blog-author a:hover,
.blog-category a:hover {
  color: #0074d9;
}

/* Nationality Dropdown Styling */
select[name="nationality"],
select[name="nationality_f"],
select[name="nationality_m"] {
  max-height: 300px;
  overflow-y: auto !important;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;

  option {
    padding: 8px 12px;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

/* Nationality Dropdown Styling */
select[name="nationality"],
select[name="nationality_f"],
select[name="nationality_m"] {
  max-height: 300px;
  overflow-y: auto !important;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;

  option {
    padding: 8px 12px;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

/* Pagination Styling - Removed duplicate styles */

/* Blog Details Link Styling */
.blog-categories {
  background-color: $secondary-color-darker;
  padding: 0.8rem;
  margin-bottom: 20px;
  color: $white-color;
}
.blog-post .text-more-info {
  display: inline-block;
  margin-top: 10px;
  font-weight: 500;
  position: relative;
  padding-right: 20px;
}

.blog-post .text-more-info:hover:after {
  transform: translateX(5px);
}

/* Blog Post Category Tags */
.blog-category a {
  background-color: #f1f1f1;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 0.85em;
  transition: background-color 0.3s ease;
}

.blog-category a:hover {
  background-color: #e0e0e0;
}

/* Footer styles */
footer {
  .section.background-blue {
    border-radius: 10px 10px 0 0;
    overflow: hidden;
  }
  .bottom-footer {
    margin-bottom: 0.5rem;
  }
  .padding.background-yellow {
    border-radius: 0 0 10px 10px;
    overflow: hidden;
  }
}

.footer-links {
  border-top: 1px solid $secondary-color;
  padding-top: 0.625rem;

  p {
    margin-bottom: 15px; /* Adjust this value to increase/decrease the gap */
  }
}
.admin-contact-info {
  padding-top: 1rem;
  margin-top: 2rem;
}
.admin-info-item {
  background: rgba(54, 73, 226, 0.6);
  padding: 0.8rem !important;
  padding-top: 0.8rem !important;
  border-radius: 10px;
  overflow: hidden;
}
.footer-links {
  border-top: 1px solid $secondary-color;
  padding-top: 1rem;

  p {
    margin-bottom: 10px; /* Adjust this value to increase/decrease the gap */
  }
}

footer h4 {
  color: $secondary-color !important;
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}
.admin-name,
.admin-phone {
  word-wrap: break-word;
}

/* Slider styles */
.carousel-default .item img {
  @extend .rounded-image;
}

/*** Navigation bar styles */
.top-nav li a,
.background-white .top-nav li a {
  color: #002633;
  font-size: 1rem;
  padding: 0.7em 1.25em;
}
nav {
  border-bottom: 4px solid rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.5rem 0;
  position: relative;
  z-index: 15;
}
.top-nav ul ul {
  background: $secondary-color none repeat scroll 0 0;
}
.top-nav li ul li {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}
.top-nav li ul li:last-child {
  border-bottom: 0;
}
.top-nav li ul li a,
.background-white .top-nav li ul li a,
.top-nav .active-item li a {
  background: $light-grey-color none repeat scroll 0 0;
  color: $primary-color;
}

@media screen and (min-width: 769px) {
  footer h4 {
    text-align: center !important;
  }
  .developer-credit {
    text-align: right;
  }
}

/* Application Form Styles */
.application-form {
  color: $primary-color;
  font-weight: 600;
}

.any-error {
  color: #C81010;
  font-weight: bold;
}

.any-success {
  color: #06a10b;
  font-weight: bold;
}

/* Style for required fields */
form.customform input.required,
form.customform select.required,
form.customform textarea.required {
  border-left: 4px solid #C81010;
}

/* Improve form field spacing */
form.customform input,
form.customform select,
form.customform textarea {
  margin-bottom: 1rem;
}

/* Style for form section headings */
h3.application-form {
  margin-top: 2rem;
  font-size: 1.4rem;
}

h4.text-left {
  margin-top: 1.5rem;
  color: $primary-color;
  font-weight: 600;
}

/* Style for submit and reset buttons */
.submit-btn {
  background-color: $primary-color !important;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background-color: darken($primary-color, 10%) !important;
}

.cancel-btn {
  background-color: #C81010 !important;
  transition: background-color 0.3s ease;
}

.cancel-btn:hover {
  background-color: darken(#C81010, 10%) !important;
}

/* Datepicker styling */
.ui-datepicker {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 0 8px rgba(0,0,0,0.1);
  padding: 10px;
}

.ui-datepicker-header {
  background-color: $primary-color;
  color: white;
  border-radius: 3px;
  padding: 5px;
}

.ui-datepicker-calendar th {
  padding: 5px;
  text-align: center;
}


.ui-datepicker-calendar td {
  padding: 2px;
  text-align: center;
}

.ui-datepicker-calendar a {
  display: block;
  padding: 5px;
  text-decoration: none;
  border-radius: 3px;
}

.ui-datepicker-calendar a:hover {
  background-color: $secondary-color;
  color: white;
}

/* Mobile styles */
@media screen and (max-width: 768px) {
  /* Ensure the hamburger menu is visible */
  .nav-text {
    display: block !important;
  }
  .top-nav li a,
  .background-white .top-nav li a {
    background: $light-grey-color none repeat scroll 0 0;
    color: #000;
    font-size: 1.2em;
    padding: 1em;
    text-align: center;
  }

  .top-nav li ul li ul li a {
    background: none repeat scroll 0 0 #456274;
  }
  .top-nav {
    background: none repeat scroll 0 0 $light-grey-color;
  }

  .top-nav li ul li a,
  .background-white .top-nav li ul li a,
  .top-nav .active-item li a {
    background: $grey-color none repeat scroll 0 0;
    color: #000;
  }
  h1 {
    font-size: 1.7rem;
    text-align: center;
  }
  h2 {
    font-size: 1.4rem;
  }
  h3 {
    font-size: 1.1rem;
  }
  h4 {
    font-size: 1rem;
  }

  .blog-post h3 {
    font-size: 1.1rem;
  }

  .homepage-headers,
  .page-headers {
    font-size: 1.4rem;
  }

  .number-stat {
    margin-bottom: 15px;
  }
  /* More info button */
  a.text-more-info {
    margin-bottom: 2rem;
  }

  hr.break-alt {
    margin: 5px 0 !important;
  }

  /* Mobile centered menu styles */
  .centered-menu {
    display: block;
  }

  .centered-menu > li {
    display: block;
  }
  .admin-info-item {
    margin-bottom: 1rem;
    text-align: center;
  }
  footer h4 {
    text-align: left;
  }
}

@media screen and (max-width: 480px) {
  .developer-credit {
    margin-top: 1rem !important;
  }
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .blog-post {
    margin-bottom: 30px;
  }
}

@media screen and (max-width: 480px) {
  .pagination li a {
    padding: 6px 12px;
  }
  .blog-author, .blog-category, .blog-date {
   font-size: 0.8rem;
  }
}

/* Fix for teacher cards grid to prevent cards from bumping into each other */
.grid.margin {
  grid-row-gap: 20px; /* Add vertical spacing between rows for all grids */
}

/* Specific styling for teacher cards grid */
.teacher-cards-grid {
  grid-row-gap: 50px !important; /* Add more vertical spacing between rows for teacher cards */
  display: grid !important; /* Ensure grid display is enforced */
  margin-bottom: 30px !important; /* Add bottom margin to the entire grid */
}

/* Utility class for smaller bottom margins */
.margin-bottom-5 {
  margin-bottom: 5px !important;
}

/* Specific styling for management page cards */
.page-management .gallery-card .gallery-content {
  padding: 10px 10px 5px; /* Further reduced padding for management page */
}

/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 0;
  margin: 30px 0;
  flex-wrap: wrap;
}

.pagination li {
  margin: 0 5px;
  display: inline-block;
}

.pagination a {
  display: block;
  padding: 8px 15px;
  text-decoration: none;
  color: $body-text;
  background-color: $white-color;
  border: 1px solid $grey-color;
  border-radius: 5px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: lighten($primary-color, 40%);
    color: $primary-color;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
  
  &.active-page {
    background-color: $primary-color;
    color: $white-color;
    border-color: $primary-color;
    font-weight: bold;
  }
  
  &.previous-page,
  &.next-page {
    background-color: $light-grey-color;
    
    &:hover {
      background-color: $grey-color;
      color: $body-text;
    }
  }
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
}

/* WordPress Default Pagination Styles */
.pagination-nav {
  margin: 0; /* Remove margin since the container has padding */
  background-color: transparent !important; /* Override Responsee background */
  
  .page-numbers {
    display: flex !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    background-color: transparent !important; /* Ensure no background from Responsee */
    
    li {
      margin: 0 5px 5px 0 !important;
      display: inline-block !important;
      list-style-type: none !important;
      background-color: transparent !important; /* Ensure no background from Responsee */
      padding: 0 !important; /* Reset padding */
    }
    
    a, span {
      display: inline-block !important;
      padding: 8px 15px !important;
      background-color: $white-color !important;
      border: 1px solid $grey-color !important;
      border-radius: 5px !important;
      color: $body-text !important;
      text-decoration: none !important;
      transition: all 0.3s ease !important;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
      
      &:hover {
        background-color: lighten($primary-color, 40%) !important;
        color: $primary-color !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
      }
    }
  
    .current {
      background-color: $primary-color !important;
      color: $white-color !important;
      border-color: $primary-color !important;
      font-weight: bold !important;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
    }
    
    .dots {
      background: none !important;
      border: none !important;
      padding: 8px 5px !important;
    }
    
    /* Style for previous and next buttons */
    .prev, .next {
      background-color: $secondary-color !important;
      color: $white-color !important;
      border-color: $secondary-color !important;
      font-weight: bold !important;
      
      &:hover {
        background-color: darken($secondary-color, 10%) !important;
        border-color: darken($secondary-color, 10%) !important;
        color: $white-color !important;
      }
    }
  }
}

/* Add specific spacing control for pagination container */
.pagination-container {
  margin-top: 30px; /* Keep space above pagination */
  margin-bottom: 15px; /* Reduce space below pagination by half */
  background-color: #f5f5f5 !important; /* Light grey background - with !important to override Responsee */
  border-radius: 10px !important; /* Rounded corners */
  padding: 15px !important; /* Add some padding */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important; /* Subtle shadow */
}

/* Additional spacing control for specific sections */
.section .line:last-child .pagination-container {
  margin-bottom: 15px; /* Ensure consistent spacing before footer */
}

/* Ensure proper spacing after content */
.grid.margin + .pagination-container {
  margin-top: 30px; /* Maintain space after content */
}

/* Responsive adjustments for pagination */
@media screen and (max-width: 768px) {
  .pagination-nav .page-numbers li {
    margin-bottom: 10px;
  }
}

/* Contact Form Styles */
.contact-form {
  position: relative;
  
  .form-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 100;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
  }
  
  .spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .form-message {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    display: none;
    text-align: center;
    font-weight: bold;
    
    &.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    &.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  }
  
  .submit-btn {
    position: relative;
    transition: all 0.3s ease;
    
    &.submitting {
      background-color: #cccccc !important;
      cursor: not-allowed;
    }
    
    &:hover {
      background-color: darken($primary-color, 10%) !important;
    }
  }
  
  .spinner-text {
    display: inline-block;
    position: relative;
    
    &:after {
      content: '...';
      position: absolute;
      right: -12px;
      animation: ellipsis 1.5s infinite;
    }
  }
  
  @keyframes ellipsis {
    0% { content: '.'; }
    33% { content: '..'; }
    66% { content: '...'; }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}
