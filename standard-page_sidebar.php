<?php
/**
 * Template Name: Standard Page with Sidebar
 * Description: A balanced layout with a main content area (9 columns) and a sidebar (3 columns). 
 *              Ideal for most pages, allowing readers to consume main content while staying updated 
 *              via sidebar elements like recent blog posts, events, or announcements.
 * Author: <PERSON>
 */

require_once('includes/header.php');

/**
 * Page Template: Standard Page with Sidebar
 *
 * This is the default template for most pages. It provides a content area using 9 columns 
 * and a 3-column sidebar. The layout supports a good balance between focused page content 
 * and access to supporting or dynamic content such as recent posts, event listings, or widgets.
 *
 * Usage: Recommended for general pages where both primary content and supplemental sidebar 
 * information are needed.
 */
?>

<!-- MAIN -->
<main role="main">
  <!-- third section -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="assets/img/parallax-02.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Title of Sample Page</h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <div class="s-12 m-8 l-9">
            <!-- put page content below, do not edit above this comment -->

            <?php 
            while (have_posts()) :
                the_post();
                the_content();
            endwhile;
            ?>

            <!-- put page content above, do not edit below this comment -->
          </div><!-- Ends content -->

          <!-- Sidebar -->
          <?php require_once('includes/main_sidebar.php'); ?>

        </div>
      </div>
    </div>
  </article>
</main>

<?php require_once('includes/footer.php'); ?>
