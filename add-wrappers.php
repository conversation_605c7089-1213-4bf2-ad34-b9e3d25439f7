<?php
// <PERSON><PERSON><PERSON> to add wrapper property to all ACF fields

// Read the file
$file_path = __DIR__ . '/inc/scf-fields.php';
$content = file_get_contents($file_path);

// Define the pattern to match field definitions
$pattern = "/'key' => '([^']+)',\s*'label' => '([^']+)',\s*'name' => '([^']+)',.*?(?='wrapper'|,\s*\),)/s";

// Define the replacement with wrapper property
$replacement = "$0\n                'wrapper' => array(\n                    'width' => '50',\n                ),";

// Check if the pattern already has a wrapper
$content = preg_replace_callback($pattern, function($matches) {
    // If it doesn't already have a wrapper property
    if (strpos($matches[0], "'wrapper'") === false) {
        return $matches[0] . "\n                'wrapper' => array(\n                    'width' => '50',\n                ),";
    }
    return $matches[0];
}, $content);

// Write the modified content back to the file
file_put_contents($file_path, $content);

echo "Wrapper properties added to all fields.\n";