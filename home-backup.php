<?php
/**
 * The template for displaying the blog posts index
 *
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-blue background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">School Blog</h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <!-- put page content below, do not edit above this comment -->
        <div class="margin">
   
          <div class="category-filter">Filter By Category</div>
          <hr class="break-alt">
            <!-- Categories navigation - Horizontal Menu -->
            <div class="blog-categories-horizontal">
              <ul class="blog-categories-menu">
              <?php
              // Get current URL and path information
              global $wp;
              $current_url = home_url($wp->request);
              $url_path = parse_url($current_url, PHP_URL_PATH);
              $last_segment = basename($url_path);
              
              // Get the blog page URL
              $blog_url = get_permalink(get_option('page_for_posts'));
              if (!$blog_url) {
                  $blog_url = home_url('/');
              }
              
              // Get total post count for all published posts
              $total_posts = wp_count_posts()->publish;
              
              // Check if we're on the main blog page
              $is_main_blog = is_home() && !is_paged();
              
              // Add "All" option with total post count
              echo '<li>';
              echo '<a href="' . esc_url($blog_url) . '" class="blog-category-link ' . ($is_main_blog ? 'active' : '') . '" data-category="all">All (' . $total_posts . ')</a>';
              echo '</li>';

              // Get all categories that have posts
              $categories = get_categories(array(
                'orderby' => 'name',
                'order' => 'ASC',
                'hide_empty' => true, // Only get categories that have posts
              ));
              
              // Debug: Show found categories for admins
              if (is_user_logged_in() && current_user_can('administrator')) {
                echo '<!-- Found ' . count($categories) . ' categories with posts -->';
                foreach ($categories as $category) {
                  echo '<!-- Category: ' . $category->name . ' (Slug: ' . $category->slug . ', ID: ' . $category->term_id . ', Count: ' . $category->count . ') -->';
                }
              }

              // Display each category
              foreach ($categories as $category) {
                // Check if we're on this category page
                $is_active = is_category($category->term_id);
                
                echo '<li>';
                echo '<a href="' . esc_url(get_category_link($category->term_id)) . '" class="blog-category-link ' . ($is_active ? 'active' : '') . '" data-category="' . esc_attr($category->slug) . '">' 
                     . esc_html($category->name) . ' (' . $category->count . ')</a>';
                echo '</li>';
              }
              ?>
              </ul>
            </div>
         
          <div class="grid margin">
            <?php
            // Create a custom query for blog posts
            $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
            $args = array(
                'post_type' => 'post',
                'posts_per_page' => 10,
                'paged' => $paged
            );
            
            // If we're on a category page, get the current category
            if (is_category()) {
                $args['cat'] = get_queried_object_id();
            }
            
            $blog_query = new WP_Query($args);
            
            // For debugging - only visible to admins
            if (is_user_logged_in() && current_user_can('administrator')) {
                echo '<!-- Query Debug: Found posts: ' . esc_html($blog_query->found_posts) . ' -->';
                echo '<!-- Query Debug: Current page: ' . $paged . ' -->';
                echo '<!-- Query Debug: Max pages: ' . $blog_query->max_num_pages . ' -->';
                echo '<!-- Query Debug: Post type: post -->';
                echo '<!-- Query Debug: Args: ' . esc_html(json_encode($args)) . ' -->';
            }
            
            if ($blog_query->have_posts()) :
                while ($blog_query->have_posts()) : $blog_query->the_post();
                    // Get post categories for filtering
                    $post_categories = get_the_category();
                    $category_slugs = array();
                    $category_names = array();
                    
                    // Debug output for admins
                    if (is_user_logged_in() && current_user_can('administrator')) {
                        echo '<!-- Post ID: ' . get_the_ID() . ' has ' . count($post_categories) . ' categories -->';
                    }
                    
                    foreach ($post_categories as $cat) {
                        $category_slugs[] = $cat->slug;
                        $category_names[$cat->slug] = $cat->name;
                        
                        // Debug output for admins
                        if (is_user_logged_in() && current_user_can('administrator')) {
                            echo '<!-- Category: ' . $cat->name . ' (Slug: ' . $cat->slug . ', ID: ' . $cat->term_id . ') -->';
                        }
                    }
                    
                    // Get the first category for display
                    $category_name = !empty($post_categories) ? $post_categories[0]->name : '';
                    $category_link = !empty($post_categories) ? get_category_link($post_categories[0]->term_id) : '';
            ?>
            <?php
            // Debug output for admins
            if (is_user_logged_in() && current_user_can('administrator')) {
                echo '<!-- Post: ' . get_the_title() . ' - Categories: ' . implode(', ', $category_slugs) . ' -->';
            }
            ?>
            <article class="s-12 m-6 l-4 margin-bottom-20 blog-post"
                     data-categories="<?php echo esc_attr(implode(' ', $category_slugs)); ?>"
                     data-post-id="<?php echo get_the_ID(); ?>">
              <?php 
              $blog_image = get_field('blog_image');
              if ($blog_image) : 
                $blog_image_url = wp_get_attachment_image_url($blog_image['ID'], 'blog-thumbnail'); // Use the blog-thumbnail size
                if (!$blog_image_url) {
                  $blog_image_url = $blog_image['url']; // Fallback to original if size doesn't exist
                }
              ?>
              <img class="rounded-image margin-bottom blog-image" src="<?php echo esc_url($blog_image_url); ?>" alt="<?php echo esc_attr($blog_image['alt'] ? $blog_image['alt'] : get_the_title()); ?>">
              <?php endif; ?>
              
              <h3 class="text-strong text-uppercase"><a class="text-dark text-blue-hover" href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
              <div class="grid">
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12"><?php echo get_the_date(); ?></div>
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-author">
                  <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>" class="blog-author">By <?php the_author(); ?></a>
                </div>
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-category">
                  <?php if (!empty($category_name)) : ?>
                  <a href="<?php echo esc_url($category_link); ?>" class="blog-category"><?php echo esc_html($category_name); ?></a>
                  <?php endif; ?>
                </div>
              </div>
              <p><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
              <a class="text-more-info text-primary-hover margin-bottom-30" href="<?php the_permalink(); ?>">Blog Details</a>
            </article>
            <?php
                endwhile;
            else:
            ?>
            <div class="s-12 margin-bottom-20 no-posts-message">
              <?php if (is_category()): ?>
                <p>No posts found in the "<?php echo esc_html(single_cat_title('', false)); ?>" category.</p>
                <p><a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>" class="text-primary-hover">View all posts</a></p>
              <?php else: ?>
                <p>No blog posts found.</p>
              <?php endif; ?>
            </div>
            <?php 
            endif;
            // Reset post data
            wp_reset_postdata();
            ?>
          </div><!-- Grid -->

          <?php if ($blog_query->max_num_pages > 1): ?>
          <!-- Pagination -->
          <div class="line text-center pagination-container">
            <div class="s-12 margin-bottom">
              <nav class="pagination-nav">
                <?php
                // Debug pagination info for admins
                if (is_user_logged_in() && current_user_can('administrator')) {
                    echo '<!-- Pagination Debug: Current page: ' . $paged . ' -->';
                    echo '<!-- Pagination Debug: Max pages: ' . $blog_query->max_num_pages . ' -->';
                    echo '<!-- Pagination Debug: Found posts: ' . $blog_query->found_posts . ' -->';
                }
                
                // Get the current URL
                $current_url = home_url($wp->request);
                
                // Create pagination links
                echo paginate_links(array(
                    'base' => str_replace(999999999, '%#%', esc_url(get_pagenum_link(999999999))),
                    'format' => '?paged=%#%',
                    'current' => max(1, $paged),
                    'total' => $blog_query->max_num_pages,
                    'prev_text' => '&laquo; ' . __('Previous', 'nkhwazischool'),
                    'next_text' => __('Next', 'nkhwazischool') . ' &raquo;',
                    'mid_size' => 2,
                    'end_size' => 1,
                    'type' => 'list'
                ));
                
                // Reset post data
                wp_reset_postdata();
                ?>
              </nav>
            </div>
          </div>
          <?php endif; ?>
        </div>

        <!-- put page content above, do not edit below this comment -->

      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>