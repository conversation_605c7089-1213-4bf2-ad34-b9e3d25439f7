/**
 * Personal Details Form JavaScript
 * 
 * Handles form validation, AJAX submission, and UI interactions
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize form
        initPersonalDetailsForm();
    });
    
    // Fallback for jQuery ready event
    $(window).on('load', function() {
        if ($('#application-form').length && !$('#application-form').data('initialized')) {
            initPersonalDetailsForm();
        }
    });

    /**
     * Initialize the personal details form
     */
    function initPersonalDetailsForm() {
        const $form = $('#application-form');
        
        if (!$form.length) {
            return;
        }
        
        // Check if already initialized
        if ($form.data('initialized')) {
            return;
        }
        
        // Mark as initialized
        $form.data('initialized', true);
        
        // Initialize datepicker
        $('#datepicker').datepicker({ 
            dateFormat: 'dd-mm-yy',
            changeMonth: true,
            changeYear: true,
            yearRange: "-100:+0" // Allow selection of any date in the past
        });
        
        // Initialize form steps
        initFormSteps($form);
        
        // Initialize form validation
        initFormValidation($form);
        
        // Handle form submission
        $form.on('submit', function(e) {
            e.preventDefault();
            
            // Validate form
            if (!validateForm($form)) {
                return;
            }
            
            // Validate reCAPTCHA
            if (!validateRecaptcha($form)) {
                showFormMessage($form, 'Please complete the reCAPTCHA verification.', 'error');
                return;
            }
            
            // Submit form via AJAX
            submitFormAjax($form);
        });
    }
    
    /**
     * Initialize form validation
     */
    function initFormValidation($form) {
        // Initialize character counters
        initCharacterCounters($form);
        
        // Validate on input change
        $form.find('input, select, textarea').on('input change', function() {
            validateField($(this));
        });
        
        // Validate on blur
        $form.find('input, select, textarea').on('blur', function() {
            validateField($(this));
        });
        
        // Live validation for all fields with min/max length
        $form.find('[data-min-length], [data-max-length]').on('input', function() {
            updateCharacterCount($(this));
            // For optional fields, only validate if they have content
            if ($(this).hasClass('required') || $(this).val().trim() !== '') {
                validateField($(this));
            } else {
                // Clear validation for empty optional fields
                $(this).removeClass('error valid');
            }
        });
        
        // Initialize character counters on page load
        $form.find('[data-min-length], [data-max-length]').each(function() {
            updateCharacterCount($(this));
        });
    }
    
    /**
     * Initialize character counters for fields with min/max length
     */
    function initCharacterCounters($form) {
        $form.find('[data-min-length], [data-max-length]').each(function() {
            const $field = $(this);
            
            // Create a container for the field and counter if it doesn't exist
            if (!$field.parent().hasClass('field-with-counter')) {
                // Get the field's current container
                const $fieldContainer = $field.parent();
                
                // Create a wrapper for the field
                $field.wrap('<div class="field-with-counter"></div>');
                
                // Add character counter after the field inside the wrapper
                $field.after('<div class="character-counter"></div>');
                updateCharacterCount($field);
            }
        });
    }
    
    /**
     * Update character count display
     */
    function updateCharacterCount($field) {
        const minLength = $field.data('min-length');
        const maxLength = $field.data('max-length');
        
        if (!minLength && !maxLength) {
            return;
        }
        
        const currentLength = $field.val().length;
        
        // Find the counter within the field's wrapper
        let $counter;
        if ($field.parent().hasClass('field-with-counter')) {
            $counter = $field.parent().find('.character-counter');
        } else {
            // For backward compatibility
            $counter = $field.next('.character-counter');
        }
        
        // Create counter if it doesn't exist
        if (!$counter.length) {
            if ($field.parent().hasClass('field-with-counter')) {
                $field.after('<div class="character-counter"></div>');
                $counter = $field.parent().find('.character-counter');
            } else {
                $field.after('<div class="character-counter"></div>');
                $counter = $field.next('.character-counter');
            }
        }
        
        let counterText = '';
        let counterClass = '';
        
        // Different messages based on validation state
        if (currentLength === 0 && !$field.hasClass('required')) {
            // Optional field that's empty
            counterText = 'Optional';
        } else if (minLength > 0 && currentLength < minLength && (currentLength > 0 || $field.hasClass('required'))) {
            // Below minimum length (only show warning if field has content or is required)
            // Only apply if minLength is greater than 0
            counterText = currentLength + '/' + minLength + ' min';
            counterClass = 'warning';
        } else if (maxLength && currentLength > maxLength) {
            // Exceeds maximum length
            counterText = currentLength + '/' + maxLength + ' (too long)';
            counterClass = 'error';
        } else if (maxLength && currentLength >= maxLength * 0.9) {
            // Approaching maximum length
            const remaining = maxLength - currentLength;
            counterText = remaining + ' character' + (remaining !== 1 ? 's' : '') + ' left';
            counterClass = 'warning';
        } else if (maxLength) {
            // Within limits with maximum
            counterText = currentLength + '/' + maxLength;
        } else if (minLength > 0 && currentLength >= minLength) {
            // Above minimum length with no maximum
            // Only apply if minLength is greater than 0
            counterText = currentLength + ' characters';
        }
        
        $counter.text(counterText).removeClass('warning error').addClass(counterClass);
    }
    
    /**
     * Initialize form steps
     */
    function initFormSteps($form) {
        const $steps = $form.find('.form-step');
        const $progressSteps = $form.find('.form-progress-step');
        
        if (!$steps.length) {
            return;
        }
        
        // Store the current active step in session storage if it exists
        const storedActiveStep = sessionStorage.getItem('activeFormStep');
        
        if (storedActiveStep && parseInt(storedActiveStep) < $steps.length) {
            // Hide all steps
            $steps.hide().removeClass('active');
            
            // Show the stored active step
            const activeStepIndex = parseInt(storedActiveStep);
            $steps.eq(activeStepIndex).addClass('active').show();
            
            // Update progress indicators
            $progressSteps.removeClass('active completed');
            for (let i = 0; i < activeStepIndex; i++) {
                $progressSteps.eq(i).addClass('completed');
            }
            $progressSteps.eq(activeStepIndex).addClass('active');
            

        } else {
            // Hide all steps except the first one
            $steps.not(':first').hide();
            
            // Add active class to first step
            $steps.first().addClass('active');
            $progressSteps.first().addClass('active');
            
            // Initialize session storage
            sessionStorage.setItem('activeFormStep', '0');
        }
        
        // Handle next button click
        $form.find('.next-step').on('click', function() {
            const $currentStep = $form.find('.form-step.active');
            const $nextStep = $currentStep.next('.form-step');
            
            // Validate current step
            if (!validateStep($currentStep)) {
                return;
            }
            
            // Move to next step
            $currentStep.removeClass('active').hide();
            $nextStep.addClass('active').show();
            
            // Update progress indicator
            const currentIndex = $steps.index($currentStep);
            const nextIndex = currentIndex + 1;
            
            $progressSteps.eq(currentIndex).removeClass('active').addClass('completed');
            $progressSteps.eq(nextIndex).addClass('active');
            
            // Store the current step in session storage
            sessionStorage.setItem('activeFormStep', nextIndex.toString());

            
            // Scroll to top of form
            $('html, body').animate({
                scrollTop: $form.offset().top - 50
            }, 300);
        });
        
        // Handle previous button click
        $form.find('.prev-step').on('click', function() {
            const $currentStep = $form.find('.form-step.active');
            const $prevStep = $currentStep.prev('.form-step');
            
            // Move to previous step
            $currentStep.removeClass('active').hide();
            $prevStep.addClass('active').show();
            
            // Update progress indicator
            const currentIndex = $steps.index($currentStep);
            const prevIndex = currentIndex - 1;
            
            $progressSteps.eq(currentIndex).removeClass('active');
            $progressSteps.eq(prevIndex).removeClass('completed').addClass('active');
            
            // Store the current step in session storage
            sessionStorage.setItem('activeFormStep', prevIndex.toString());

            
            // Scroll to top of form
            $('html, body').animate({
                scrollTop: $form.offset().top - 50
            }, 300);
        });
        
        // Handle direct navigation by clicking on progress steps
        $form.find('.form-progress-step.clickable').on('click', function() {
            const clickedIndex = parseInt($(this).data('step'));
            const currentIndex = $progressSteps.index($progressSteps.filter('.active'));
            
            // Don't allow skipping ahead without validation
            if (clickedIndex > currentIndex) {
                // Validate all steps up to the clicked one
                let allValid = true;
                for (let i = 0; i <= currentIndex; i++) {
                    if (!validateStep($steps.eq(i))) {
                        allValid = false;
                        break;
                    }
                }
                
                if (!allValid) {
                    alert('Please complete the current step before proceeding.');
                    return;
                }
            }
            
            // Update active step
            $steps.removeClass('active').hide();
            $steps.eq(clickedIndex).addClass('active').show();
            
            // Update progress indicators
            $progressSteps.removeClass('active');
            $progressSteps.eq(clickedIndex).addClass('active');
            
            // Update completed steps
            $progressSteps.removeClass('completed');
            for (let i = 0; i < clickedIndex; i++) {
                $progressSteps.eq(i).addClass('completed');
            }
            
            // Store the current step in session storage
            sessionStorage.setItem('activeFormStep', clickedIndex.toString());

            
            // Scroll to top of form
            $('html, body').animate({
                scrollTop: $form.offset().top - 50
            }, 300);
        });
    }
    
    /**
     * Validate a form step
     */
    function validateStep($step) {
        let isValid = true;
        
        // Validate all required fields in the step
        $step.find('input.required, select.required, textarea.required').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });
        
        // Validate email fields
        $step.find('input.email').each(function() {
            if ($(this).val() !== '' && !validateField($(this))) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    /**
     * Validate the entire form
     */
    function validateForm($form) {
        let isValid = true;
        
        // Validate all required fields
        $form.find('input.required, select.required, textarea.required').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });
        
        // Validate email fields
        $form.find('input.email').each(function() {
            if ($(this).val() !== '' && !validateField($(this))) {
                isValid = false;
            }
        });
        
        // If not valid, show error message
        if (!isValid) {
            showFormMessage($form, 'Please fill in all required fields correctly.', 'error');
        }
        
        return isValid;
    }
    
    /**
     * Validate a single field
     */
    function validateField($field) {
        // Skip validation for non-required empty fields
        if (!$field.hasClass('required') && $field.val() === '') {
            removeFieldError($field);
            // Update character count for empty optional fields
            if ($field.data('min-length') || $field.data('max-length')) {
                updateCharacterCount($field);
            }
            return true;
        }
        
        // Required field validation
        if ($field.hasClass('required') && $field.val() === '') {
            showFieldError($field, 'This field is required');
            return false;
        }
        
        // Email validation
        if ($field.hasClass('email') && $field.val() !== '') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test($field.val())) {
                showFieldError($field, 'Please enter a valid email address');
                return false;
            }
        }
        
        // Min length validation
        const minLength = $field.data('min-length');
        if (minLength && $field.val().length < minLength && $field.val() !== '') {
            showFieldError($field, `Minimum length is ${minLength} characters`);
            // Update character count for fields with min length issues
            if ($field.data('min-length') || $field.data('max-length')) {
                updateCharacterCount($field);
            }
            return false;
        }
        
        // Max length validation
        const maxLength = $field.data('max-length');
        if (maxLength && $field.val().length > maxLength) {
            showFieldError($field, `Maximum length is ${maxLength} characters`);
            // Update character count for fields with max length issues
            if ($field.data('min-length') || $field.data('max-length')) {
                updateCharacterCount($field);
            }
            return false;
        }
        
        // Field is valid
        removeFieldError($field);
        // Update character count for valid fields
        if ($field.data('min-length') || $field.data('max-length')) {
            updateCharacterCount($field);
        }
        return true;
    }
    
    /**
     * Show field error
     */
    function showFieldError($field, message) {
        // Remove existing error
        removeFieldError($field);
        
        // Add error class
        $field.removeClass('valid').addClass('error');
        
        // Add error message
        const $errorMessage = $('<div class="field-error-message">' + message + '</div>');
        $field.after($errorMessage);
    }
    
    /**
     * Remove field error
     */
    function removeFieldError($field) {
        $field.removeClass('error').addClass('valid');
        $field.next('.field-error-message').remove();
    }
    
    /**
     * Submit form via AJAX
     */
    function submitFormAjax($form) {
        // Show loading indicator
        $form.find('.form-loading').show();
        $form.find('.form-message').hide();
        $form.find('button[type="submit"]').prop('disabled', true);
        
        // Collect form data
        const formData = new FormData($form[0]);
        
        // Add action and nonce
        formData.append('action', 'nkhwazi_submit_personal_details_form');
        
        // Use the nonce from the hidden field if available, otherwise use the one from the localized script
        const hiddenNonce = $form.find('input[name="nonce"]').val();
        if (hiddenNonce) {
            formData.append('nonce', hiddenNonce);
        } else {
            formData.append('nonce', nkhwazi_personal_details_form.nonce);
        }
        
        // Make sure all form fields are included
        const $allFields = $form.find('input, select, textarea');
        $allFields.each(function() {
            const field = $(this);
            const name = field.attr('name');
            
            // Skip fields without a name
            if (!name) return;
            
            // Add the field to the FormData if it's not already included
            if (field.is(':checkbox') || field.is(':radio')) {
                if (field.is(':checked') && !formData.has(name)) {
                    formData.append(name, field.val());
                }
            } else if (!formData.has(name)) {
                formData.append(name, field.val());
            }
        });
        
        $.ajax({
            url: nkhwazi_personal_details_form.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {

                
                // Hide loading indicator
                $form.find('.form-loading').hide();
                
                if (response.success) {
                    // Show success message
                    showFormMessage($form, response.data.message, 'success');
                    
                    // Reset form
                    $form[0].reset();
                    $form.find('.valid, .error').removeClass('valid error');
                    $form.find('.field-error-message').remove();
                    
                    // Reset form steps if using steps
                    if ($form.find('.form-step').length) {
                        $form.find('.form-step').removeClass('active').hide();
                        $form.find('.form-step').first().addClass('active').show();
                        $form.find('.form-progress-step').removeClass('active completed');
                        $form.find('.form-progress-step').first().addClass('active');
                        
                        // Reset session storage
                        sessionStorage.setItem('activeFormStep', '0');

                    }
                    
                    // Scroll to top of form
                    $('html, body').animate({
                        scrollTop: $form.offset().top - 50
                    }, 300);
                } else {
                    // Show error message
                    showFormMessage($form, response.data.message, 'error');
                    
                    // Enable submit button
                    $form.find('button[type="submit"]').prop('disabled', false);
                }
            },
            error: function(xhr, status, error) {
                
                // Hide loading indicator
                $form.find('.form-loading').hide();
                
                // Show error message
                showFormMessage($form, 'An error occurred. Please try again later.', 'error');
                
                // Enable submit button
                $form.find('button[type="submit"]').prop('disabled', false);
            }
        });
    }
    
    /**
     * Show form message
     */
    function showFormMessage($form, message, type) {
        const $message = $form.find('.form-message');
        
        $message.removeClass('success error').addClass(type);
        $message.html(message).show();
        
        // Scroll to message
        $('html, body').animate({
            scrollTop: $message.offset().top - 100
        }, 300);
    }
    
    /**
     * Validate reCAPTCHA
     */
    function validateRecaptcha($form) {
        const recaptchaResponse = grecaptcha.getResponse();
        
        if (recaptchaResponse.length === 0) {
            // Show error message
            $form.find('.recaptcha-error-message').text('Please complete the reCAPTCHA verification.');
            return false;
        }
        
        // Clear error message
        $form.find('.recaptcha-error-message').text('');
        return true;
    }
    
})(jQuery);