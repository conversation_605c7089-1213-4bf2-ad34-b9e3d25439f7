<?php
/**
 * Template Name: Notifications Page
 * Description: A template for displaying school notifications
 *
 * @package Nkhwazi_Primary_School
 */

get_header();

// Query notifications
$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
$args = array(
    'post_type' => 'notification',
    'posts_per_page' => 8, // 3 per row, 2 rows
    'orderby' => 'date',
    'order' => 'DESC',
    'paged' => $paged
);

$notifications_query = new WP_Query($args);
?>

<!-- MAIN -->
<main role="main">
    <!-- Content -->
    <article>
        <div class="line">
            <header class="rounded-div section background-primary background-transparent text-center"
                data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-02.jpg">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">School Notifications</h1>
                <!-- Page ID: 426 -->
            </header>
        </div>

        <div class="section background-white">
            <div class="line">
                <div class="margin">
                    <!-- Page content if any -->
                    <div class="s-12 m-12 l-12 margin-bottom-30">
                        <?php while (have_posts()) : the_post(); ?>
                            <?php the_content(); ?>
                        <?php endwhile; ?>
                    </div>
                    
                    <?php if ($notifications_query->have_posts()) : ?>
                        <!-- Notifications List -->
                        <div class="s-12 m-12 l-12">
                            <div class="grid margin">
                                <?php while ($notifications_query->have_posts()) : $notifications_query->the_post(); 
                                    // Get ACF fields
                                    $heading = get_field('heading');
                                    $description = get_field('description');
                                    $post_date = get_the_date('F j, Y');
                                ?>
                                    <div class="s-12 m-6 l-4 margin-bottom-30">
                                        <div class="notification-card background-white padding-2x rounded-div box-shadow">
                                            <h3 class="text-strong text-size-20 margin-bottom-5"><?php echo esc_html($heading); ?></h3>
                                            <small class="text-grey margin-bottom-10 display-block">Posted: <?php echo esc_html($post_date); ?></small>
                                            <div class="margin-bottom-0"><?php echo wp_kses_post($description); ?></div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($notifications_query->max_num_pages > 1) : ?>
                        <div class="s-12 text-center margin-top-30">
                            <?php
                            $big = 999999999; // need an unlikely integer
                            echo paginate_links(array(
                                'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                                'format' => '?paged=%#%',
                                'current' => max(1, get_query_var('paged')),
                                'total' => $notifications_query->max_num_pages,
                                'prev_text' => __('Prev', 'nkhwazischool'),
                                'next_text' => __('Next', 'nkhwazischool'),
                            ));
                            ?>
                        </div>
                        <?php endif; ?>
                        
                    <?php else : ?>
                        <div class="s-12 m-12 l-12">
                            <p class="text-center">No notifications found.</p>
                        </div>
                    <?php endif; ?>
                    
                    <?php wp_reset_postdata(); ?>
                </div>
            </div>
        </div>
    </article>
</main>

<?php get_footer(); ?>