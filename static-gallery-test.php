<?php
/**
 * Template Name: Static Gallery Test
 * Template Post Type: page
 * Description: Test template for displaying static photo galleries without WordPress gallery functions
 * Author: <PERSON>
 */

require_once('includes/header.php');
?>

<!-- MAIN -->
<main role="main">
    <!-- Content -->
    <article>
        <div class="line">
            <header class="rounded-div section background-blue background-transparent text-center"
                data-image-src="assets/img/parallax-02.jpg">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">
                    Static Gallery Test
                </h1>
            </header>
        </div>

        <div class="section background-white">
            <div class="line">

                <!-- Gallery Description -->
                <div class="s-12 margin-bottom-30">
                    <p class="text-dark">This is a test gallery using static images to debug the lightcase iframe issue.</p>
                </div>
                
                <!-- Gallery Section -->
                <section class="section background-white">
                    <div class="line">
                        <div class="margin">
                            <?php
                            // Static image array
                            $static_images = array(
                                array(
                                    'url' => get_template_directory_uri() . '/assets/img/gallery/DSCN0659.JPG',
                                    'title' => 'DSCN0659',
                                    'alt' => 'School Image 1'
                                ),
                                array(
                                    'url' => get_template_directory_uri() . '/assets/img/gallery/DSCN0734_large.JPG',
                                    'title' => 'DSCN0734',
                                    'alt' => 'School Image 2'
                                ),
                                array(
                                    'url' => get_template_directory_uri() . '/assets/img/gallery/DSCN0751_large.JPG',
                                    'title' => 'DSCN0751',
                                    'alt' => 'School Image 3'
                                ),
                                array(
                                    'url' => get_template_directory_uri() . '/assets/img/gallery/DSCNWS0438_large.jpg',
                                    'title' => 'DSCNWS0438',
                                    'alt' => 'School Image 4'
                                ),
                                array(
                                    'url' => get_template_directory_uri() . '/assets/img/gallery/indoor-sport.jpg',
                                    'title' => 'Indoor Sport',
                                    'alt' => 'Indoor Sport'
                                )
                            );
                            
                            // Display static images
                            foreach ($static_images as $image) :
                            ?>
                                <div class="s-12 m-6 l-3">
                                    <a class="image-with-hover-overlay image-hover-zoom margin-bottom" data-rel="lightcase:staticgallery" 
                                        href="<?php echo esc_url($image['url']); ?>">
                                        <div class="image-hover-overlay background-primary"> 
                                            <div class="image-hover-overlay-content text-center padding-2x">
                                                <i class="icon-magnifying icon2x text-white"></i>  
                                            </div> 
                                        </div> 
                                        <img src="<?php echo esc_url($image['url']); ?>" 
                                            alt="<?php echo esc_attr($image['alt']); ?>" 
                                            title="<?php echo esc_attr($image['title']); ?>" />
                                    </a>
                                </div>
                            <?php
                            endforeach;
                            ?>
                        </div>
                    </div>
                    
                    <!-- Back to Galleries Button -->
                    <div class="line text-center">
                        <div class="s-12 margin-bottom">
                            <a href="<?php echo esc_url(home_url()); ?>" class="button button-primary-stroke text-size-12 text-white text-strong margin-bottom">Back to Home</a>
                        </div>
                    </div>
                </section>

            </div>
        </div>
    </article>
</main>

<?php require_once('includes/footer.php'); ?>