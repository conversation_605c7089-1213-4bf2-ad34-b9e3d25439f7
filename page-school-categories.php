<?php 
/**
 * Template Name: School Categories
 * Description: A template for displaying school categories in a grid layout.
 * Author: <PERSON>
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <div class="line">
    <header class="rounded-div section background-blue background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.jpg">
      <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">School Categories</h1>
    </header>
  </div>
  <!-- Content -->
  <article class="s-12 m-12 l-12">
    <div class="section background-white">
      <div class="line">
        <!-- put page content below, do not edit above this comment -->
        
        <div class="margin">
          <!-- Section Title and Description -->
          <div class="line">
            <h2 class="text-uppercase text-padding-small background-primary text-white text-strong  margin-bottom-30">
              <i class="icon-spread margin-right-10"></i>School Categories
            </h2>
          </div>
          
          <div class="s-12 margin-bottom-30">
            <p>At Nkhwazi Primary School, we organize our educational structure into distinct categories to ensure age-appropriate learning experiences. Each school category is designed to meet the specific developmental needs of children at different stages of their academic journey.</p>
          </div>
          
          <!-- School Categories Grid -->
          <div class="grid margin">
            <?php
            // Get all school categories
            $args = array(
                'post_type' => 'school_categories',
                'posts_per_page' => -1,
                'orderby' => 'menu_order',
                'order' => 'ASC',
            );
            $categories_query = new WP_Query($args);
            
            if ($categories_query->have_posts()) :
                while ($categories_query->have_posts()) : $categories_query->the_post();
                    // Get ACF fields
                    $school_category = get_field('school_category');
                    $category_excerpt = get_field('category_excerpt');
                    $grade_range = get_field('grade_range');
                    $age_range = get_field('age_range');
                    $card_img = get_field('card_img');
                    
                    // Get post ID and slug for URL
                    $post_id = get_the_ID();
                    $post_slug = get_post_field('post_name', $post_id);
            ?>
            <div class="s-12 m-6 l-4 margin-m-bottom">
              <div class="school-category">
                <?php if ($card_img) : ?>
                <img class="margin-bottom rounded-image-top" src="<?php echo esc_url(wp_get_attachment_image_url($card_img['ID'], 'school-category-card')); ?>" alt="<?php echo esc_attr(get_the_title()); ?>">
                <?php endif; ?>
                <h3 class="text-strong"><a class="text-dark text-primary-hover" href="<?php echo esc_url(home_url('/school-categories/' . $post_slug . '/')); ?>"><?php echo esc_html(get_the_title()); ?></a></h3>
                <p><?php echo esc_html($category_excerpt); ?></p>
                <p><?php echo esc_html($grade_range); ?>/ <?php echo esc_html($age_range); ?></p>
                <div class="margin-top-bottom-20 center">
                  <a class="text-more-info" href="<?php echo esc_url(home_url('/school-categories/' . $post_slug . '/')); ?>">Category Details</a>
                </div>
              </div>
            </div>
            <?php
                endwhile;
                wp_reset_postdata();
            else:
            ?>
            <div class="s-12 margin-m-bottom">
              <p>No school categories found.</p>
            </div>
            <?php endif; ?>
          </div>
        </div>
        
        <!-- put page content above, do not edit below this comment -->
      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>