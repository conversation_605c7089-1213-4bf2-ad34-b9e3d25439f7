<?php
/**
 * Contact Form Functionality
 *
 * @package NkhwaziSchool
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Enqueue scripts and styles for the contact form
 */
function nkhwazi_enqueue_contact_form_script() {
    // Only enqueue on the contact page
    if (is_page('contact')) {
        // Enqueue jQuery UI for validation
        wp_enqueue_script('jquery-ui-core');
        
        // Enqueue the contact form script
        wp_enqueue_script(
            'nkhwazischool-contact-form',
            get_template_directory_uri() . '/assets/js/contact-form.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        // Pass AJAX URL to script
        wp_localize_script(
            'nkhwazischool-contact-form',
            'nkhwazi_contact_form',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('nkhwazi_contact_form_nonce'),
                'recaptcha_site_key' => '6Lf79rcaAAAAALdqWY74mw_n6DtTxR_C5AEL6cfL'
            )
        );
    }
}
add_action('wp_enqueue_scripts', 'nkhwazi_enqueue_contact_form_script');

/**
 * Get admin contacts from the database
 * 
 * @return array Array of admin contacts with job title and email
 */
function nkhwazi_get_admin_contacts() {
    $contacts = array();
    
    $args = array(
        'post_type' => 'admin_contact_info',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC',
    );
    
    $query = new WP_Query($args);
    
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $job_title = get_field('job_title');
            $email = get_field('email');
            
            if (!empty($job_title) && !empty($email)) {
                $contacts[] = array(
                    'job_title' => $job_title,
                    'email' => $email
                );
            }
        }
    }
    
    wp_reset_postdata();
    
    return $contacts;
}

/**
 * Handle contact form submission via AJAX
 */
function nkhwazi_submit_contact_form() {
    // For debugging
    error_log('Contact form submission received');
    
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'nkhwazi_contact_form_nonce')) {
        error_log('Nonce verification failed');
        wp_send_json_error(array(
            'message' => __('Security check failed. Please refresh the page and try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Verify reCAPTCHA
    if (!isset($_POST['g-recaptcha-response']) || empty($_POST['g-recaptcha-response'])) {
        error_log('reCAPTCHA response missing');
        wp_send_json_error(array(
            'message' => __('Please complete the reCAPTCHA verification.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Verify with Google reCAPTCHA API
    $recaptcha_secret = '6Lf79rcaAAAAACnBRBWJW8M7XLGq4o-tPhvn7r-J';
    $recaptcha_response = $_POST['g-recaptcha-response'];
    
    $verify_response = wp_remote_post('https://www.google.com/recaptcha/api/siteverify', array(
        'body' => array(
            'secret' => $recaptcha_secret,
            'response' => $recaptcha_response,
            'remoteip' => $_SERVER['REMOTE_ADDR']
        )
    ));
    
    if (is_wp_error($verify_response)) {
        error_log('reCAPTCHA verification error: ' . $verify_response->get_error_message());
        wp_send_json_error(array(
            'message' => __('reCAPTCHA verification failed. Please try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    $response_body = wp_remote_retrieve_body($verify_response);
    $response_data = json_decode($response_body, true);
    
    if (!isset($response_data['success']) || !$response_data['success']) {
        error_log('reCAPTCHA verification failed: ' . print_r($response_data, true));
        wp_send_json_error(array(
            'message' => __('reCAPTCHA verification failed. Please try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Validate required fields
    $required_fields = array(
        'name' => __('Your Name', 'nkhwazischool'),
        'email' => __('Your Email', 'nkhwazischool'),
        'subject' => __('Subject', 'nkhwazischool'),
        'message' => __('Message', 'nkhwazischool')
    );
    
    $missing_fields = array();
    foreach ($required_fields as $field => $label) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $label;
        }
    }
    
    if (!empty($missing_fields)) {
        error_log('Missing required fields: ' . implode(', ', $missing_fields));
        wp_send_json_error(array(
            'message' => __('Please fill in all required fields.', 'nkhwazischool'),
            'fields' => $missing_fields
        ));
        wp_die();
    }
    
    // Validate field lengths
    $length_validations = array(
        'name' => array('min' => 2, 'max' => 30, 'label' => __('Your Name', 'nkhwazischool')),
        'email' => array('min' => 5, 'max' => 100, 'label' => __('Your Email', 'nkhwazischool')),
        'phone' => array('min' => 6, 'max' => 20, 'label' => __('Phone Number', 'nkhwazischool'), 'required' => false),
        'subject' => array('min' => 4, 'max' => 100, 'label' => __('Subject', 'nkhwazischool')),
        'message' => array('min' => 20, 'max' => 2000, 'label' => __('Message', 'nkhwazischool'))
    );
    
    $length_errors = array();
    foreach ($length_validations as $field => $rules) {
        // Skip if field is not required and empty
        if (isset($rules['required']) && $rules['required'] === false && empty($_POST[$field])) {
            continue;
        }
        
        $length = isset($_POST[$field]) ? strlen($_POST[$field]) : 0;
        
        if ($length < $rules['min']) {
            $length_errors[] = sprintf(
                __('%s must be at least %d characters.', 'nkhwazischool'),
                $rules['label'],
                $rules['min']
            );
        } elseif ($length > $rules['max']) {
            $length_errors[] = sprintf(
                __('%s must be no more than %d characters.', 'nkhwazischool'),
                $rules['label'],
                $rules['max']
            );
        }
    }
    
    if (!empty($length_errors)) {
        error_log('Length validation errors: ' . implode(', ', $length_errors));
        wp_send_json_error(array(
            'message' => __('Please correct the following errors:', 'nkhwazischool') . '<br>' . implode('<br>', $length_errors)
        ));
        wp_die();
    }
    
    // Validate email
    if (!is_email($_POST['email'])) {
        error_log('Invalid email: ' . $_POST['email']);
        wp_send_json_error(array(
            'message' => __('Please enter a valid email address.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Sanitize input
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    $subject = sanitize_text_field($_POST['subject']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // Get recipient email
    $recipient_email = '';
    if (isset($_POST['recipient']) && !empty($_POST['recipient'])) {
        $recipient_email = sanitize_email($_POST['recipient']);
    } else {
        // If no recipient selected, get the first admin contact
        $contacts = nkhwazi_get_admin_contacts();
        if (!empty($contacts)) {
            $recipient_email = $contacts[0]['email'];
        } else {
            // Fallback to admin email
            $recipient_email = get_option('admin_email');
        }
    }
    
    error_log('Sending email to: ' . $recipient_email);
    
    // Create email headers
    $headers = array(
        'Content-Type: text/html; charset=UTF-8',
        'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
        'Reply-To: ' . $name . ' <' . $email . '>'
    );
    
    // Create email body
    $email_body = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Contact Form Submission</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; }
        .header { background-color: #0073aa; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .field { margin-bottom: 15px; }
        .field-label { font-weight: bold; }
        .footer { background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>New Contact Form Submission</h1>
    </div>
    <div class="content">
        <p>You have received a new message from your website contact form.</p>
        
        <div class="field">
            <div class="field-label">Name:</div>
            <div>' . esc_html($name) . '</div>
        </div>
        
        <div class="field">
            <div class="field-label">Email:</div>
            <div>' . esc_html($email) . '</div>
        </div>';
        
    if (!empty($phone)) {
        $email_body .= '
        <div class="field">
            <div class="field-label">Phone:</div>
            <div>' . esc_html($phone) . '</div>
        </div>';
    }
    
    $email_body .= '
        <div class="field">
            <div class="field-label">Subject:</div>
            <div>' . esc_html($subject) . '</div>
        </div>
        
        <div class="field">
            <div class="field-label">Message:</div>
            <div>' . nl2br(esc_html($message)) . '</div>
        </div>
    </div>
    <div class="footer">
        <p>This email was sent from the contact form on ' . get_bloginfo('name') . ' (' . site_url() . ')</p>
        <p>IP Address: ' . $_SERVER['REMOTE_ADDR'] . '</p>
    </div>
</body>
</html>';
    
    // Send email
    $mail_sent = wp_mail($recipient_email, 'Contact Form: ' . $subject, $email_body, $headers);
    
    if ($mail_sent) {
        error_log('Email sent successfully');
        wp_send_json_success(array(
            'message' => __('Thank you for your message. We will get back to you as soon as possible.', 'nkhwazischool')
        ));
    } else {
        error_log('Failed to send email');
        wp_send_json_error(array(
            'message' => __('There was an error sending your message. Please try again later.', 'nkhwazischool')
        ));
    }
    
    wp_die();
}
add_action('wp_ajax_nkhwazi_submit_contact_form', 'nkhwazi_submit_contact_form');
add_action('wp_ajax_nopriv_nkhwazi_submit_contact_form', 'nkhwazi_submit_contact_form');