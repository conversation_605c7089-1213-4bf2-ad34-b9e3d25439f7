<?php
/**
 * The template for displaying single extracurricular posts
 *
 * @package Nkhwazi_Primary_School
 */

get_header();

// Get the current post
global $post;
?>

<!-- MAIN -->
<main role="main">
  <!-- Header Section -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php the_title(); ?></h1>
      </header>
    </div>
    
    <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
    
    <!-- Featured Image -->
    <?php if (has_post_thumbnail()) : ?>
    <div class="line margin-top-30 margin-bottom-30">
      <div class="s-12">
        <?php the_post_thumbnail('full', array('class' => 'full-width-img rounded-image', 'alt' => get_the_title())); ?>
      </div>
    </div>
    <?php endif; ?>

    <!-- Main Content Section -->
    <div class="section background-white">
      <div class="line">
        <div class="grid margin">
          <!-- Left Column: Extracurricular Details -->
          <div class="s-12 m-6 l-7">
            <?php 
            // Display additional fields if they exist
            $type = get_field('type');
            $excerpt = get_field('excerpt');
            
            if ($type || $excerpt) :
            ?>
            <div class="extracurricular-summary">
              <?php if ($type) : ?>
              <p><strong>Type:</strong> <?php echo esc_html($type); ?></p>
              <?php endif; ?>
              
              <?php if ($excerpt) : ?>
              <p><strong>Summary:</strong> <?php echo esc_html($excerpt); ?></p>
              <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <h2 class="text-primary margin-bottom-20">About This Extracurricular</h2>
            <?php 
            // Display the main content
            the_content(); 
            ?>
          </div>
          
          <!-- Right Column: Associated Activities -->
          <div class="s-12 m-6 l-5">
            <?php
            // Query for extracurricular_item posts that are related to this extracurricular
            $args = array(
                'post_type' => 'extracurricular_item',
                'posts_per_page' => 4,
                'meta_query' => array(
                    array(
                        'key' => 'extracurricular_type', // Field that links to the parent extracurricular
                        'value' => '"' . get_the_ID() . '"', // ACF relationship fields store IDs as serialized strings
                        'compare' => 'LIKE'
                    )
                )
            );
            $related_query = new WP_Query($args);
            
            // Debug output - can be removed after testing
            if (current_user_can('administrator')) {
                echo '<!-- Debug: Found ' . $related_query->found_posts . ' related activities -->';
            }
            
            // Check if we have related activities
            if ($related_query->have_posts()) : 
                $related_activities = $related_query->posts;
            ?>
            <h2 class="text-primary margin-bottom-20 text-center text-strong">Associated Activities</h2>
            
            <!-- Activities List with Links to Single Activity Pages -->
            <div class="activities-list">
              <?php while ($related_query->have_posts()) : $related_query->the_post(); 
                // Get the activity data
                $activity_id = get_the_ID();
                $activity_title = get_the_title();
                $activity_name = get_field('name', $activity_id);
                $activity_excerpt = get_field('excerpt', $activity_id);
                $activity_date = get_field('date', $activity_id);
                $card_image = get_field('card_image', $activity_id);
              ?>
              <div class="activity-item margin-bottom-15">
                <!-- Activity Card (Clickable) -->
                <a href="<?php echo esc_url(get_permalink($activity_id)); ?>" class="activity-link">
                  <div class="activity-header background-grey-hover cursor-pointer">
                    <div class="grid margin">
                      <div class="s-5 m-5 l-5">
                        <?php if ($card_image) : ?>
                          <img src="<?php echo esc_url($card_image['url']); ?>" alt="<?php echo esc_attr($activity_name ? $activity_name : $activity_title); ?>" class="activity-thumbnail">
                        <?php else : ?>
                          <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/placeholder.jpg" alt="<?php echo esc_attr($activity_name ? $activity_name : $activity_title); ?>" class="activity-thumbnail">
                        <?php endif; ?>
                      </div>
                      <div class="s-7 m-7 l-7">
                        <h4 class="text-strong"><?php echo esc_html($activity_name ? $activity_name : $activity_title); ?></h4>
                        <?php if ($activity_date) : ?>
                          <p class="text-size-12 text-grey margin-bottom-5"><?php echo esc_html($activity_date); ?></p>
                        <?php endif; ?>
                        <?php if ($activity_excerpt) : ?>
                          <p class="text-size-12 text-grey margin-bottom-5"><?php echo wp_trim_words($activity_excerpt, 10); ?></p>
                        <?php endif; ?>
                        <span class="text-size-12 m-12 l-12 text-primary">Click to view details</span>
                      </div>
                    </div>
                  </div>
                </a>
              </div>
              <?php endwhile; 
              wp_reset_postdata(); // Reset the post data to the main query
              ?>
              
              <?php 
              // If there are more activities than we're showing, display a "View All" link
              if ($related_query->found_posts > 4) : 
                // Try to find a page with the extracurricular-activities template
                $pages = get_pages(array(
                  'meta_key' => '_wp_page_template',
                  'meta_value' => 'page-extracurricular-activities.php'
                ));
                
                $activities_url = '';
                if (!empty($pages)) {
                  $activities_url = get_permalink($pages[0]->ID);
                } else {
                  // Fallback to the home page
                  $activities_url = home_url();
                }
              ?>
              <div class="text-center margin-top-30">
                <a href="<?php echo esc_url($activities_url); ?>" class="button background-primary text-white text-strong">View All Activities</a>
              </div>
              <?php endif; ?>
            </div>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
    
    <?php endwhile; endif; ?>
    
    <!-- Back to Extracurricular Categories Button -->
    <div class="section background-white">
      <div class="line text-center margin-bottom-10">
        <?php
        // Get the URL of the extracurricular category page using its ID
        $extracurricular_page_id = 335; // Specific ID for the extracurricular page
        $back_url = get_permalink($extracurricular_page_id);
        ?>
        <a href="<?php echo esc_url($back_url); ?>" class="button background-primary text-white text-strong">
          Back to Extracurricular Categories
        </a>
      </div>
    </div>
    
  </article>
</main>

<!-- JavaScript for activity links -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Make entire activity card clickable
  const activityItems = document.querySelectorAll('.activity-item');
  
  activityItems.forEach(item => {
    item.addEventListener('click', function(e) {
      // Find the link inside this activity item
      const link = this.querySelector('.activity-link');
      if (link) {
        // Navigate to the link's href
        window.location.href = link.getAttribute('href');
      }
    });
  });
});
</script>

<?php get_footer(); ?>