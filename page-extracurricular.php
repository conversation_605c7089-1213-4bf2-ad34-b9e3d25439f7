<?php
/**
 * Template Name: Extracurricular Category
 * Description: A full-width layout template showcasing the school's extracurricular Categories.
 * Author: <PERSON>
 */

get_header();

// Query extracurricular posts
$args = array(
    'post_type' => 'extracurricular',
    'posts_per_page' => -1, // Get all posts
    'orderby' => 'title',
    'order' => 'ASC',
    // Remove meta_query for now to see if that's causing the issue
);

$extracurricular_query = new WP_Query($args);

// Debug information (only visible to admins)
if (is_user_logged_in() && current_user_can('administrator')) {
    echo '<!-- Debug: Extracurricular Query -->';
    echo '<!-- Found posts: ' . $extracurricular_query->found_posts . ' -->';
    echo '<!-- Query SQL: ' . $extracurricular_query->request . ' -->';
}
?>

<!-- MAIN -->
<main role="main">
    <!-- Content -->
    <article>
        <div class="line">
            <header class="rounded-div section background-blue background-transparent text-center"
                data-image-src="assets/img/parallax-02.jpg">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Extracurricular
                    Categories</h1>
            </header>
        </div>

        <div class="section background-white">
            <div class="line">

                <!-- put page content below, do not edit above this comment -->

                <div class="s-12 m-12 l-12 margin-bottom-20">
                    <?php the_content(); ?>
                </div>

                <!-- Extracurricular Activities Cards -->
                <div class="grid margin">
                    <?php
                    if ($extracurricular_query->have_posts()) :
                        while ($extracurricular_query->have_posts()) : $extracurricular_query->the_post();
                            // Get ACF fields
                            $type = get_field('type');
                            $excerpt = get_field('excerpt');
                            $image = get_field('image');

                            // Get the post ID for more reliable URLs
                            $post_id = get_the_ID();
                    ?>
                    <div class="s-12 m-4 l-4 margin-m-bottom blog-post gallery-card">
                        <span class="gallery-label extracurricular-label">Extracurricular</span>
                        <a href="<?php echo esc_url(get_permalink($post_id)); ?>">
                            <?php if ($image) : ?>
                                <img class="rounded-image blog-image" src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr(get_the_title()); ?>">
                            <?php else : ?>
                                <img class="rounded-image blog-image" src="<?php echo esc_url(get_template_directory_uri() . '/assets/img/placeholder.jpg'); ?>" alt="<?php echo esc_attr(get_the_title()); ?>">
                            <?php endif; ?>
                        </a>
                        <div class="gallery-content">
                            <h3 class="text-strong">
                                <a class="text-dark text-orange-hover" href="<?php echo esc_url(get_permalink($post_id)); ?>">
                                    <?php the_title(); ?>
                                </a>
                            </h3>
                            <div class="s-12 m-12 l-12">
                                <p class="margin-bottom text-dark"><?php echo esc_html($excerpt); ?></p>
                                <a class="text-more-info text-primary-hover margin-bottom-20" href="<?php echo esc_url(get_permalink($post_id)); ?>">Learn More</a>
                            </div>
                        </div>
                    </div>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    else :
                    ?>
                    <div class="s-12 m-12 l-12">
                        <p class="text-center">No extracurricular activities found.</p>
                    </div>
                    <?php endif; ?>
                </div>

                <?php if ($extracurricular_query->max_num_pages > 1) : ?>
                <!-- Pagination -->
                <div class="line text-center pagination-container">
                    <div class="s-12 margin-bottom">
                        <ul class="pagination">
                            <?php
                            $big = 999999999; // need an unlikely integer
                            echo paginate_links(array(
                                'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                                'format' => '?paged=%#%',
                                'current' => max(1, get_query_var('paged')),
                                'total' => $extracurricular_query->max_num_pages,
                                'prev_text' => __('Prev', 'nkhwazischool'),
                                'next_text' => __('Next', 'nkhwazischool'),
                                'type' => 'list'
                            ));
                            ?>
                        </ul>
                    </div>
                </div>
                <?php endif; ?>

                <!-- put page content above, do not edit below this comment -->

            </div>
        </div>
    </article>
</main>

<?php get_footer(); ?>