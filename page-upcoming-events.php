<?php
/**
 * Template Name: Upcoming Events Page
 * Description: A page that lists all upcoming events in a responsive grid layout.
 * Author: <PERSON>
 * 
 * Page ID: 434 (Upcoming Events)
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-blue background-transparent text-center"
        data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-02.jpg">
        <h1 class="text-white margin-bottom-0 text-thin text-line-height-1 text-uppercase">Upcoming Events</h1>
        <!-- Page ID: 434 -->
      </header>
    </div>

    <div class="section background-white">
      <div class="grid margin">
        <div class="s-12 m-12 l-12 margin-bottom-10">
          <?php while (have_posts()) : the_post(); ?>
          <?php the_content(); ?>
          <?php endwhile; ?>
        </div>

        <?php
        // Get current page for pagination
        $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
        
        // Events per page
        $events_per_page = 8;
        
        // Get upcoming events
        $args = array(
            'post_type' => 'upcoming_event',
            'posts_per_page' => $events_per_page,
            'paged' => $paged,
            'meta_key' => 'due_date',
            'orderby' => 'meta_value',
            'order' => 'ASC',
        );
        
        $events_query = new WP_Query($args);
        
        if ($events_query->have_posts()) :
            while ($events_query->have_posts()) : $events_query->the_post();
                // Get ACF fields
                $due_date = get_field('due_date');
                $event_name = get_field('event_name');
                $event_excerpt = get_field('event_excerpt');
                
                // Get permalink for the event
                $event_link = get_permalink();
        ?>
        <!-- Event Card -->
        <div class="s-12 m-6 l-4 margin-m-bottom margin-bottom-20">
          <article class="margin-bottom-10 event-card">
            <div class="grid text-left">
              <div class="s-12 m-12 l-1 text-left">
                <i class="icon-sli-clock text-strong text-secondary text-size-20"></i>
              </div>
              <div class="s-11 m-11 l-11 margin-bottom-10">
                <div class="admin-name margin-left-10 text-size-16">Due: <span
                    class="text-size-14 rounded-div text-primary-dark event-date"><?php echo esc_html($due_date); ?></span>
                </div>
              </div>
            </div>
            <h3 class="text-strong"><a class="text-dark text-blue-hover"
                href="<?php echo esc_url($event_link); ?>"><?php echo esc_html($event_name); ?></a></h3>
            <p><?php echo esc_html($event_excerpt); ?></p>
            <a class="text-more-info text-primary-hover" href="<?php echo esc_url($event_link); ?>">Event Details</a>
          </article>
        </div>
        <?php
            endwhile;
        else :
        ?>
        <div class="s-12 text-center">
          <p>No upcoming events found. Please check back later.</p>
        </div>
        <?php
        endif;
        ?>

      </div><!-- grid margin -->

      <!-- Pagination -->
      <?php if ($events_query->max_num_pages > 1) : ?>
      <div class="line text-center pagination-container">
        <div class="s-12 margin-bottom">
          <ul class="pagination">
            <?php
            // Previous page
            if ($paged > 1) :
            ?>
            <li><a href="<?php echo add_query_arg('paged', $paged - 1, get_permalink(434)); ?>"
                class="previous-page">Prev</a></li>
            <?php endif; ?>

            <?php
            // Page numbers
            for ($i = 1; $i <= $events_query->max_num_pages; $i++) :
                $active_class = ($i == $paged) ? 'active-page' : '';
            ?>
            <li><a href="<?php echo add_query_arg('paged', $i, get_permalink(434)); ?>"
                class="<?php echo $active_class; ?>"><?php echo $i; ?></a></li>
            <?php endfor; ?>

            <?php
            // Next page
            if ($paged < $events_query->max_num_pages) :
            ?>
            <li><a href="<?php echo add_query_arg('paged', $paged + 1, get_permalink(434)); ?>"
                class="next-page">Next</a></li>
            <?php endif; ?>
          </ul>
        </div>
      </div>
      <?php endif; ?>

      <?php wp_reset_postdata(); ?>

    </div>
  </article>
</main>

<?php get_footer(); ?>