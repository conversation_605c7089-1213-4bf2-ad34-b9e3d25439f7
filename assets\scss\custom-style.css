/* Custom styles for Nkhwazi School */
/* Contact Form Styles */
.contact-form {
  position: relative;
  /* Form validation styles */
  /* Character counter styles */
  /* Fix for field error messages */
  /* Form message styles */
  /* Loading indicator */
  /* reCAPTCHA container */
  /* Center reCAPTCHA */
}
.contact-form input.error,
.contact-form textarea.error,
.contact-form select.error {
  border-left: 4px solid #e74c3c !important;
}
.contact-form input.valid,
.contact-form textarea.valid,
.contact-form select.valid {
  border-left: 4px solid #2ecc71 !important;
}
.contact-form .field-error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}
.contact-form .field-with-counter {
  position: relative;
  width: 100%;
  margin-bottom: 10px;
}
.contact-form .field-with-counter .character-counter {
  position: absolute;
  right: 10px;
  top: -20px;
  font-size: 12px;
  color: #777;
}
.contact-form .field-with-counter .character-counter.warning {
  color: #f39c12;
}
.contact-form .field-with-counter .character-counter.error {
  color: #e74c3c;
}
.contact-form .field-with-counter input, .contact-form .field-with-counter textarea {
  width: 100%;
}
.contact-form .field-error-message {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
}
.contact-form .form-message {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: none;
}
.contact-form .form-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}
.contact-form .form-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
.contact-form .form-loading {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 100;
  text-align: center;
  padding-top: 100px;
}
.contact-form .form-loading .spinner {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
.contact-form .form-loading p {
  margin-top: 15px;
  font-weight: bold;
}
.contact-form .g-recaptcha-container {
  margin-bottom: 20px;
}
.contact-form .g-recaptcha-container .recaptcha-error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
}
.contact-form .g-recaptcha {
  display: inline-block;
}

/* Animation for spinner */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Make sure the submit button is a button, not a link */
.submit-btn {
  cursor: pointer;
}
.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .contact-form .g-recaptcha {
    transform: scale(0.85);
    transform-origin: 0 0;
  }
  .contact-form .field-with-counter .character-counter {
    top: -18px;
    font-size: 10px;
  }
}/*# sourceMappingURL=custom-style.css.map */