/* Custom styles for Nkhwazi School */
/* Animation for spinner */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Make sure the submit button is a button, not a link */
.submit-btn {
  cursor: pointer;
}
.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Contact Form Character Counter Styles - Positioned below fields */
.character-counter {
  font-size: 0.8rem;
  text-align: right;
  margin-top: 5px;
  color: #666;
  transition: all 0.3s ease;
  padding: 2px 5px;
  border-radius: 3px;
  display: block;
  position: relative;
  top: auto;
  right: auto;
}

.character-counter.warning {
  color: #e2a736;
  font-weight: bold;
  background-color: rgba(226, 167, 54, 0.1);
}

.character-counter.error {
  color: #e74c3c;
  font-weight: bold;
  background-color: rgba(231, 76, 60, 0.1);
}

/* Field error message positioning */
.field-error-message {
  display: block;
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
}

/* Form field validation styles */
.field-with-counter {
  position: relative;
  margin-bottom: 15px;
}

/* Required field indicator - red border for required fields */
input.required,
textarea.required,
select.required {
  border-left: 3px solid #e74c3c !important;
}

/* Required fields note */
.required-fields-note {
  color: #777;
  font-style: italic;
  text-align: right;
  margin-bottom: 15px;
}

.required-fields-note small {
  font-size: 0.85rem;
}

/* Error state overrides required state with a darker red */
input.error,
textarea.error,
select.error {
  border-color: #e74c3c !important;
  border-left-width: 3px !important;
  border-left-color: #c0392b !important;
}

/* Valid state overrides required state with green */
input.valid,
textarea.valid,
select.valid {
  border-color: #2ecc71 !important;
  border-left-width: 3px !important;
  border-left-color: #2ecc71 !important;
}

/* Unified card styling for blog posts and category cards */
/* This ensures both card types have the same styling with top rounded corners only */
.rounded-image-top {
  border-radius: 10px 10px 0 0 !important;
  overflow: hidden;
}

/* Fix for category card images to ensure consistent layout */
.home-category-card img.rounded-image-top,
.school-category img.rounded-image-top {
  border-radius: 10px 10px 0 0 !important;
  overflow: hidden;
  margin-top: -15px;
  margin-left: -15px;
  margin-right: -15px;
  width: calc(100% + 30px);
  max-width: none;
}

/* Restore original blog post image styling */
.blog-post img.rounded-image-top {
  border-radius: 10px 10px 0 0 !important;
  overflow: hidden;
  width: 100%;
}

/* Blog image styling */
.blog-image {
  margin-top: 8px;
}

/* Make home category cards look like blog cards - Updated approach */
.home-category-card {
  transition: all 0.3s ease;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 0 !important;
  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}
.home-category-card img.rounded-image-top {
  margin: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
  border-radius: 10px 10px 0 0 !important;
  display: block !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
  height: auto !important;
}
.home-category-card h3, .home-category-card p, .home-category-card .margin-top-bottom-20 {
  padding-left: 15px !important;
  padding-right: 15px !important;
}
.home-category-card h3 {
  padding-top: 15px !important;
}
.home-category-card .margin-top-bottom-20 {
  padding-bottom: 15px !important;
}

.home-category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}

/* Ensure school-category has the same padding as blog-post */
.school-category.blog-post {
  padding: 15px;
}/*# sourceMappingURL=custom-style.css.map */