/**
 * Blog Category Filter Script
 * Handles client-side filtering of blog posts by category
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Blog filter script loaded');
        initBlogFilter();
    });

    function initBlogFilter() {
        // Get all blog posts
        const blogPosts = $('.blog-post');
        console.log('Found ' + blogPosts.length + ' blog posts');
        
        // Debug: Log all posts and their categories
        blogPosts.each(function() {
            const categories = $(this).attr('data-categories') || '';
            console.log('Post: ' + $(this).find('h3').text() + ' - Categories: ' + categories);
        });
        
        // Get category filter links
        const categoryLinks = $('.blog-category-link');
        console.log('Found ' + categoryLinks.length + ' category links');
        
        // Debug: Log all category links
        categoryLinks.each(function() {
            const category = $(this).data('category');
            console.log('Category link: ' + $(this).text() + ' - Data category: ' + category);
        });
        
        // Set initial state based on URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const initialCategory = urlParams.get('category') || 'all';
        console.log('Initial category: ' + initialCategory);
        
        // Filter posts on page load
        filterPosts(initialCategory);
        
        // Add click event to category links
        categoryLinks.on('click', function(e) {
            e.preventDefault();
            
            // Get selected category
            const category = $(this).data('category');
            console.log('Category clicked: ' + category);
            
            // Update URL without reloading the page
            const url = new URL(window.location);
            url.searchParams.set('category', category);
            window.history.pushState({}, '', url);
            
            // Update active class
            categoryLinks.removeClass('active');
            $(this).addClass('active');
            
            // Filter posts
            filterPosts(category);
        });
        
        // Function to filter posts by category
        function filterPosts(category) {
            console.log('Filtering posts by category: ' + category);
            
            // If 'all' is selected, show all posts
            if (category === 'all') {
                console.log('Showing all posts');
                blogPosts.show();
                $('.no-posts-message').hide();
                return;
            }
            
            // Hide all posts first
            blogPosts.hide();
            
            // Show posts that match the selected category
            const matchingPosts = blogPosts.filter(function() {
                const postCategoriesAttr = $(this).attr('data-categories') || '';
                console.log('Post categories attribute: ' + postCategoriesAttr);
                
                if (!postCategoriesAttr) {
                    return false;
                }
                
                const postCategories = postCategoriesAttr.split(' ');
                console.log('Post categories array: ' + JSON.stringify(postCategories));
                
                const matches = postCategories.includes(category);
                console.log('Matches category ' + category + ': ' + matches);
                
                return matches;
            });
            
            console.log('Found ' + matchingPosts.length + ' matching posts');
            matchingPosts.show();
            
            // If no posts are visible, show a message
            if (matchingPosts.length === 0) {
                console.log('No posts found in category: ' + category);
                if ($('.no-posts-message').length === 0) {
                    $('.grid.margin').append('<div class="s-12 no-posts-message"><p>No posts found in this category.</p></div>');
                } else {
                    $('.no-posts-message').show();
                }
            } else {
                $('.no-posts-message').hide();
            }
        }
    }

})(jQuery);