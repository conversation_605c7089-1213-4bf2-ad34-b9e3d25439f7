<section class="background-white">
        <div class="line"> 
          <div class="carousel-default owl-carousel carousel-main carousel-wide-arrows">
            <?php
            // Get slides from frontpage_slideshow post type
            $args = array(
                'post_type' => 'frontpage_slideshow',
                'posts_per_page' => -1,
                'orderby' => 'menu_order',
                'order' => 'ASC',
            );
            $slides_query = new WP_Query($args);
            
            if ($slides_query->have_posts()) :
                while ($slides_query->have_posts()) : $slides_query->the_post();
                    // Get ACF fields
                    $slider_image = get_field('slider_image');
                    $slider_heading = get_field('slider_heading');
                    $slider_description = get_field('slider_description');
                    $slider_url = get_field('slider_url');
                    
                    // Use default URL if not set
                    if (empty($slider_url)) {
                        $slider_url = home_url('/');
                    }
                    
                    // No need to get the title as we're using slider_heading for alt text
            ?>
            <a href="<?php echo esc_url($slider_url); ?>" class="item">
              <img src="<?php echo esc_url(wp_get_attachment_image_url($slider_image['ID'], 'home-slide')); ?>" alt="<?php echo esc_attr($slider_heading); ?>">
              <div class="carousel-content">
                <div class="content-bottom">
                  <p class="text-padding background-yellow text-white text-s-size-20 text-m-size-30 text-size-40 text-thin text-line-height-1"><?php echo esc_html($slider_heading); ?></p>
                  <p class="text-padding background-primary text-white margin-bottom-40"><?php echo esc_html($slider_description); ?></p>                   
                </div>
              </div>
            </a>
            <?php
                endwhile;
                wp_reset_postdata();
            endif; ?>
          </div>  
        </div>
      </section>