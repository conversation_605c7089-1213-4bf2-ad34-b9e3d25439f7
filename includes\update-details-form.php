<?php
/**
 * Update Personal Details Form Functionality
 *
 * @package NkhwaziSchool
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Register Personal Details Update Custom Post Type
 */
function nkhwazi_register_personal_details_cpt() {
    register_post_type('personal_details', array(
        'labels' => array(
            'name'               => __('Personal Details Updates', 'nkhwazischool'),
            'singular_name'      => __('Personal Details Update', 'nkhwazischool'),
            'add_new'            => __('Add New', 'nkhwazischool'),
            'add_new_item'       => __('Add New Update', 'nkhwazischool'),
            'edit_item'          => __('Edit Update', 'nkhwazischool'),
            'new_item'           => __('New Update', 'nkhwazischool'),
            'view_item'          => __('View Update', 'nkhwazischool'),
            'search_items'       => __('Search Updates', 'nkhwazischool'),
            'not_found'          => __('No updates found', 'nkhwazischool'),
            'not_found_in_trash' => __('No updates found in Trash', 'nkhwazischool'),
            'menu_name'          => __('Personal Details', 'nkhwazischool'),
        ),
        'public'              => false,
        'publicly_queryable'  => false,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'show_in_rest'        => false,
        'query_var'           => false,
        'rewrite'             => false,
        'capability_type'     => 'post',
        'has_archive'         => false,
        'hierarchical'        => false,
        'menu_position'       => 31,
        'menu_icon'           => 'dashicons-id-alt',
        'supports'            => array('title'),
    ));
}
add_action('init', 'nkhwazi_register_personal_details_cpt');

/**
 * Add meta boxes for personal details updates
 */
function nkhwazi_personal_details_meta_boxes() {
    add_meta_box(
        'nkhwazi_personal_details',
        __('Personal Details Update', 'nkhwazischool'),
        'nkhwazi_personal_details_callback',
        'personal_details',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'nkhwazi_personal_details_meta_boxes');

/**
 * Personal details meta box callback
 */
function nkhwazi_personal_details_callback($post) {
    // Get student details
    $lname = get_post_meta($post->ID, '_pupil_lname', true);
    $fname = get_post_meta($post->ID, '_pupil_fname', true);
    $dob = get_post_meta($post->ID, '_pupil_dob', true);
    $grade = get_post_meta($post->ID, '_pupil_grade', true);
    $religion = get_post_meta($post->ID, '_pupil_religion', true);
    $address = get_post_meta($post->ID, '_pupil_address', true);
    $siblings = get_post_meta($post->ID, '_pupil_siblings', true);
    
    // Father's details
    $lname_f = get_post_meta($post->ID, '_father_lname', true);
    $fname_f = get_post_meta($post->ID, '_father_fname', true);
    $occupation_f = get_post_meta($post->ID, '_father_occupation', true);
    $address_business_f = get_post_meta($post->ID, '_father_address_business', true);
    $phone_f = get_post_meta($post->ID, '_father_phone', true);
    $phone_work_f = get_post_meta($post->ID, '_father_phone_work', true);
    $email_f = get_post_meta($post->ID, '_father_email', true);
    
    // Mother's details
    $lname_m = get_post_meta($post->ID, '_mother_lname', true);
    $fname_m = get_post_meta($post->ID, '_mother_fname', true);
    $occupation_m = get_post_meta($post->ID, '_mother_occupation', true);
    $address_business_m = get_post_meta($post->ID, '_mother_address_business', true);
    $phone_m = get_post_meta($post->ID, '_mother_phone', true);
    $phone_work_m = get_post_meta($post->ID, '_mother_phone_work', true);
    $email_m = get_post_meta($post->ID, '_mother_email', true);
    
    // Doctor's details
    $family_doctor = get_post_meta($post->ID, '_doctor_name', true);
    $phone_doctor = get_post_meta($post->ID, '_doctor_phone', true);
    $tel_doctor = get_post_meta($post->ID, '_doctor_tel', true);
    $email_doctor = get_post_meta($post->ID, '_doctor_email', true);
    
    // Emergency contact details
    $lname_emergency = get_post_meta($post->ID, '_emergency_lname', true);
    $fname_emergency = get_post_meta($post->ID, '_emergency_fname', true);
    $phone_emergency = get_post_meta($post->ID, '_emergency_phone', true);
    $tel_emergency = get_post_meta($post->ID, '_emergency_tel', true);
    $email_emergency = get_post_meta($post->ID, '_emergency_email', true);
    
    // Additional information
    $more_info = get_post_meta($post->ID, '_more_info', true);
    
    // Submission details
    $date = get_the_date('F j, Y \a\t g:i a', $post->ID);
    $ip = get_post_meta($post->ID, '_update_ip', true);
    
    // Output details in accordion
    ?>
    <div class="notice notice-info" style="margin: 10px 0;">
        <p><strong>Information:</strong> Click on each section header below to view the details.</p>
    </div>
    
    <style>
        /* Accordion styles instead of tabs */
        .details-section {
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .details-section-header {
            background-color: #f1f1f1;
            padding: 12px 15px;
            cursor: pointer;
            font-weight: bold;
            border-bottom: 1px solid transparent;
            transition: 0.3s;
            position: relative;
        }
        
        .details-section-header:after {
            content: '+';
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            font-weight: normal;
        }
        
        .details-section-header.active:after {
            content: '−';
        }
        
        .details-section-header:hover {
            background-color: #e0e0e0;
        }
        
        .details-section-header.active {
            background-color: #0073aa;
            color: white;
            border-bottom-color: #ccc;
        }
        
        .details-section-content {
            padding: 15px;
            display: none;
            background-color: #fff;
            border-top: 1px solid #ddd;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .details-section-content.active {
            display: block;
        }
        
        /* Additional styles */
        .details-field {
            margin-bottom: 15px;
        }
        
        .details-field label {
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }
        
        .details-field-value {
            padding: 8px;
            background: #f9f9f9;
            border: 1px solid #e5e5e5;
        }
        
        .details-meta {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
            font-size: 12px;
            color: #666;
        }
        .details-field {
            margin-bottom: 15px;
        }
        .details-field label {
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }
        .details-field-value {
            padding: 8px;
            background: #f9f9f9;
            border: 1px solid #e5e5e5;
        }
        .details-meta {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
            font-size: 12px;
            color: #666;
        }
    </style>
    
    <!-- Student's Details Section -->
    <div class="details-section">
        <div class="details-section-header active" data-section="student-details">
            Student's Details
        </div>
        <div id="student-details" class="details-section-content active" style="display: block;">
        
        <div class="details-field">
            <label>Full Name:</label>
            <div class="details-field-value"><?php echo esc_html($fname . ' ' . $lname); ?></div>
        </div>
        
        <div class="details-field">
            <label>Date of Birth:</label>
            <div class="details-field-value"><?php echo esc_html($dob); ?></div>
        </div>
        
        <div class="details-field">
            <label>Grade:</label>
            <div class="details-field-value"><?php echo esc_html($grade); ?></div>
        </div>
        
        <div class="details-field">
            <label>Religion:</label>
            <div class="details-field-value"><?php echo esc_html($religion); ?></div>
        </div>
        
        <div class="details-field">
            <label>Home Address:</label>
            <div class="details-field-value"><?php echo esc_html($address); ?></div>
        </div>
        
        <div class="details-field">
            <label>Siblings at School:</label>
            <div class="details-field-value"><?php echo !empty($siblings) ? esc_html($siblings) : 'None'; ?></div>
        </div>
    </div>
    </div>
    
    <!-- Father's Details Section -->
    <div class="details-section">
        <div class="details-section-header" data-section="father-details">
            Father's Details
        </div>
        <div id="father-details" class="details-section-content">
        
        <div class="details-field">
            <label>Full Name:</label>
            <div class="details-field-value"><?php echo esc_html($fname_f . ' ' . $lname_f); ?></div>
        </div>
        
        <div class="details-field">
            <label>Occupation:</label>
            <div class="details-field-value"><?php echo esc_html($occupation_f); ?></div>
        </div>
        
        <?php if (!empty($address_business_f)) : ?>
        <div class="details-field">
            <label>Business Address:</label>
            <div class="details-field-value"><?php echo esc_html($address_business_f); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="details-field">
            <label>Phone Number:</label>
            <div class="details-field-value"><?php echo esc_html($phone_f); ?></div>
        </div>
        
        <?php if (!empty($phone_work_f)) : ?>
        <div class="details-field">
            <label>Work Phone:</label>
            <div class="details-field-value"><?php echo esc_html($phone_work_f); ?></div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($email_f)) : ?>
        <div class="details-field">
            <label>Email Address:</label>
            <div class="details-field-value"><?php echo esc_html($email_f); ?></div>
        </div>
        <?php endif; ?>
    </div>
    </div>
    
    <!-- Mother's Details Section -->
    <div class="details-section">
        <div class="details-section-header" data-section="mother-details">
            Mother's Details
        </div>
        <div id="mother-details" class="details-section-content">
        
        <div class="details-field">
            <label>Full Name:</label>
            <div class="details-field-value"><?php echo esc_html($fname_m . ' ' . $lname_m); ?></div>
        </div>
        
        <div class="details-field">
            <label>Occupation:</label>
            <div class="details-field-value"><?php echo esc_html($occupation_m); ?></div>
        </div>
        
        <?php if (!empty($address_business_m)) : ?>
        <div class="details-field">
            <label>Business Address:</label>
            <div class="details-field-value"><?php echo esc_html($address_business_m); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="details-field">
            <label>Phone Number:</label>
            <div class="details-field-value"><?php echo esc_html($phone_m); ?></div>
        </div>
        
        <?php if (!empty($phone_work_m)) : ?>
        <div class="details-field">
            <label>Work Phone:</label>
            <div class="details-field-value"><?php echo esc_html($phone_work_m); ?></div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($email_m)) : ?>
        <div class="details-field">
            <label>Email Address:</label>
            <div class="details-field-value"><?php echo esc_html($email_m); ?></div>
        </div>
        <?php endif; ?>
    </div>
    </div>
    
    <!-- Doctor's Details Section -->
    <div class="details-section">
        <div class="details-section-header" data-section="doctor-details">
            Family Doctor or Clinic Details
        </div>
        <div id="doctor-details" class="details-section-content">
        
        <?php if (!empty($family_doctor)) : ?>
        <div class="details-field">
            <label>Doctor/Clinic Name:</label>
            <div class="details-field-value"><?php echo esc_html($family_doctor); ?></div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($phone_doctor)) : ?>
        <div class="details-field">
            <label>Mobile Number:</label>
            <div class="details-field-value"><?php echo esc_html($phone_doctor); ?></div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($tel_doctor)) : ?>
        <div class="details-field">
            <label>Office Phone:</label>
            <div class="details-field-value"><?php echo esc_html($tel_doctor); ?></div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($email_doctor)) : ?>
        <div class="details-field">
            <label>Email Address:</label>
            <div class="details-field-value"><?php echo esc_html($email_doctor); ?></div>
        </div>
        <?php endif; ?>
    </div>
    </div>
    
    <!-- Emergency Contact Section -->
    <div class="details-section">
        <div class="details-section-header" data-section="emergency-details">
            Emergency Contact Details
        </div>
        <div id="emergency-details" class="details-section-content">
        
        <div class="details-field">
            <label>Full Name:</label>
            <div class="details-field-value"><?php echo esc_html($fname_emergency . ' ' . $lname_emergency); ?></div>
        </div>
        
        <div class="details-field">
            <label>Mobile Number:</label>
            <div class="details-field-value"><?php echo esc_html($phone_emergency); ?></div>
        </div>
        
        <?php if (!empty($tel_emergency)) : ?>
        <div class="details-field">
            <label>Alternative Phone:</label>
            <div class="details-field-value"><?php echo esc_html($tel_emergency); ?></div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($email_emergency)) : ?>
        <div class="details-field">
            <label>Email Address:</label>
            <div class="details-field-value"><?php echo esc_html($email_emergency); ?></div>
        </div>
        <?php endif; ?>
    </div>
    </div>
    
    <!-- Additional Information Section -->
    <div class="details-section">
        <div class="details-section-header" data-section="additional-details">
            Additional Information
        </div>
        <div id="additional-details" class="details-section-content">
        
        <div class="details-field">
            <label>Additional Information:</label>
            <div class="details-field-value"><?php echo !empty($more_info) ? esc_html($more_info) : 'None provided'; ?></div>
        </div>
    </div>
    </div>
    
    <div class="details-meta">
        <p><strong>Submission Date:</strong> <?php echo esc_html($date); ?></p>
        <p><strong>IP Address:</strong> <?php echo esc_html($ip); ?></p>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        // Initialize accordion - hide all sections except the first one
        $('.details-section-content').not(':first').hide();
        
        // Simple accordion functionality
        $('.details-section-header').on('click', function(e) {
            // Prevent any default behavior
            e.preventDefault();
            e.stopPropagation();
            
            // Get the section content
            const $content = $(this).next('.details-section-content');
            const isActive = $(this).hasClass('active');
            
            // If already active, do nothing (keep it open)
            if (isActive) {
                return;
            }
            
            // Close all sections
            $('.details-section-header').removeClass('active');
            $('.details-section-content').slideUp(300);
            
            // Open the clicked section
            $(this).addClass('active');
            $content.slideDown(300);
            
            // Log for debugging
            console.log('Opened section:', $(this).text().trim());
        });
        
        // Check if accordion is working after a short delay
        setTimeout(function() {
            console.log('Active sections:', $('.details-section-header.active').length);
            console.log('Visible content sections:', $('.details-section-content:visible').length);
            
            // If no sections are active or visible, force the first one to be active
            if ($('.details-section-header.active').length === 0 || $('.details-section-content:visible').length === 0) {
                console.log('No active sections found, activating the first one');
                $('.details-section-header').first().addClass('active');
                $('.details-section-content').first().show();
            }
        }, 500);
    });
    </script>
    <?php
}

/**
 * Add settings page for personal details form
 */
function nkhwazi_add_personal_details_settings_page() {
    add_submenu_page(
        'edit.php?post_type=personal_details',
        __('Personal Details Settings', 'nkhwazischool'),
        __('Settings', 'nkhwazischool'),
        'manage_options',
        'personal-details-settings',
        'nkhwazi_personal_details_settings_page'
    );
}
add_action('admin_menu', 'nkhwazi_add_personal_details_settings_page');

/**
 * Personal details form settings page callback
 */
function nkhwazi_personal_details_settings_page() {
    // Save settings
    if (isset($_POST['nkhwazi_personal_details_settings_nonce']) && wp_verify_nonce($_POST['nkhwazi_personal_details_settings_nonce'], 'nkhwazi_personal_details_settings')) {
        $settings_updated = false;
        
        // Save notification email
        if (isset($_POST['nkhwazi_personal_details_email'])) {
            $email = sanitize_email($_POST['nkhwazi_personal_details_email']);
            if (!empty($email)) {
                update_option('nkhwazi_personal_details_email', $email);
                $settings_updated = true;
            }
        }
        
        // Save CC emails
        if (isset($_POST['nkhwazi_personal_details_cc_emails'])) {
            $cc_emails = sanitize_textarea_field($_POST['nkhwazi_personal_details_cc_emails']);
            
            // Validate and clean CC emails
            $cc_emails_array = explode("\n", $cc_emails);
            $valid_cc_emails = array();
            $invalid_cc_emails = array();
            
            foreach ($cc_emails_array as $cc_email) {
                $cc_email = trim($cc_email);
                if (!empty($cc_email)) {
                    if (is_email($cc_email)) {
                        $valid_cc_emails[] = $cc_email;
                    } else {
                        $invalid_cc_emails[] = $cc_email;
                    }
                }
            }
            
            // Save only valid emails
            $cleaned_cc_emails = implode("\n", $valid_cc_emails);
            update_option('nkhwazi_personal_details_cc_emails', $cleaned_cc_emails);
            $settings_updated = true;
            
            // Show warning for invalid emails
            if (!empty($invalid_cc_emails)) {
                echo '<div class="notice notice-warning is-dismissible"><p>' . 
                    sprintf(__('The following email addresses were invalid and have been removed: %s', 'nkhwazischool'), 
                    '<code>' . implode('</code>, <code>', $invalid_cc_emails) . '</code>') . 
                    '</p></div>';
            }
        }
        
        // Save email subject prefix
        if (isset($_POST['nkhwazi_personal_details_subject_prefix'])) {
            $subject_prefix = sanitize_text_field($_POST['nkhwazi_personal_details_subject_prefix']);
            update_option('nkhwazi_personal_details_subject_prefix', $subject_prefix);
            $settings_updated = true;
        }
        
        // Save email footer text
        if (isset($_POST['nkhwazi_personal_details_email_footer'])) {
            $email_footer = wp_kses_post($_POST['nkhwazi_personal_details_email_footer']);
            update_option('nkhwazi_personal_details_email_footer', $email_footer);
            $settings_updated = true;
        }
        
        if ($settings_updated) {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully.', 'nkhwazischool') . '</p></div>';
        }
    }
    
    // Get current settings
    $current_email = get_option('nkhwazi_personal_details_email', get_option('admin_email'));
    $cc_emails = get_option('nkhwazi_personal_details_cc_emails', '');
    $subject_prefix = get_option('nkhwazi_personal_details_subject_prefix', '[' . get_bloginfo('name') . ']');
    $email_footer = get_option('nkhwazi_personal_details_email_footer', 'This is an automated email from ' . get_bloginfo('name') . ' website.');
    
    ?>
    <div class="wrap">
        <h1><?php _e('Personal Details Form Settings', 'nkhwazischool'); ?></h1>
        
        <form method="post" action="">
            <?php wp_nonce_field('nkhwazi_personal_details_settings', 'nkhwazi_personal_details_settings_nonce'); ?>
            
            <h2><?php _e('Email Notification Settings', 'nkhwazischool'); ?></h2>
            <p><?php _e('Configure how personal details update submissions are sent via email.', 'nkhwazischool'); ?></p>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="nkhwazi_personal_details_email"><?php _e('Primary Notification Email', 'nkhwazischool'); ?></label>
                    </th>
                    <td>
                        <input type="email" name="nkhwazi_personal_details_email" id="nkhwazi_personal_details_email" value="<?php echo esc_attr($current_email); ?>" class="regular-text">
                        <p class="description"><?php _e('Main email address where personal details update submissions will be sent.', 'nkhwazischool'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="nkhwazi_personal_details_cc_emails"><?php _e('Additional CC Recipients', 'nkhwazischool'); ?></label>
                    </th>
                    <td>
                        <textarea name="nkhwazi_personal_details_cc_emails" id="nkhwazi_personal_details_cc_emails" rows="3" class="large-text"><?php echo esc_textarea($cc_emails); ?></textarea>
                        <p class="description"><?php _e('Optional. Enter additional email addresses (one per line). These addresses will receive a copy of each update submission.', 'nkhwazischool'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="nkhwazi_personal_details_subject_prefix"><?php _e('Email Subject Prefix', 'nkhwazischool'); ?></label>
                    </th>
                    <td>
                        <input type="text" name="nkhwazi_personal_details_subject_prefix" id="nkhwazi_personal_details_subject_prefix" value="<?php echo esc_attr($subject_prefix); ?>" class="regular-text">
                        <p class="description"><?php _e('Text to prepend to the email subject line. Default is your site name in brackets.', 'nkhwazischool'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="nkhwazi_personal_details_email_footer"><?php _e('Email Footer Text', 'nkhwazischool'); ?></label>
                    </th>
                    <td>
                        <textarea name="nkhwazi_personal_details_email_footer" id="nkhwazi_personal_details_email_footer" rows="3" class="large-text"><?php echo esc_textarea($email_footer); ?></textarea>
                        <p class="description"><?php _e('Custom text to display in the footer of notification emails.', 'nkhwazischool'); ?></p>
                    </td>
                </tr>
            </table>
            
            <h3><?php _e('Email Testing', 'nkhwazischool'); ?></h3>
            
            <?php
            // Handle test email
            if (isset($_POST['send_test_email']) && isset($_POST['nkhwazi_personal_details_settings_nonce']) && wp_verify_nonce($_POST['nkhwazi_personal_details_settings_nonce'], 'nkhwazi_personal_details_settings')) {
                $to = get_option('nkhwazi_personal_details_email', get_option('admin_email'));
                $subject = get_option('nkhwazi_personal_details_subject_prefix', '[' . get_bloginfo('name') . ']') . ' Test Email';
                
                // Create a simple HTML email
                $message = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Email</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; }
        .header { background-color: #0073aa; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .footer { background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Email</h1>
    </div>
    <div class="content">
        <p>This is a test email from the Nkhwazi School Personal Details Update Form.</p>
        <p>If you are receiving this email, your email configuration is working correctly.</p>
        <p>This email was sent to: ' . $to . '</p>';
                
                // Add CC recipients if configured
                $cc_emails = get_option('nkhwazi_personal_details_cc_emails', '');
                $cc_list = array();
                if (!empty($cc_emails)) {
                    $cc_emails_array = explode("\n", $cc_emails);
                    foreach ($cc_emails_array as $cc_email) {
                        $cc_email = trim($cc_email);
                        if (!empty($cc_email) && is_email($cc_email)) {
                            $cc_list[] = $cc_email;
                            $message .= '<p>CC: ' . $cc_email . '</p>';
                        }
                    }
                }
                
                $message .= '</div>
    <div class="footer">
        <p>' . get_option('nkhwazi_personal_details_email_footer', 'This is an automated email from ' . get_bloginfo('name') . ' website.') . '</p>
    </div>
</body>
</html>';
                
                // Set up email headers
                $headers = array(
                    'Content-Type: text/html; charset=UTF-8',
                    'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
                );
                
                // Add CC headers if needed
                if (!empty($cc_list)) {
                    foreach ($cc_list as $cc) {
                        $headers[] = 'Cc: ' . $cc;
                    }
                }
                
                // Send the email
                $sent = wp_mail($to, $subject, $message, $headers);
                
                if ($sent) {
                    echo '<div class="notice notice-success is-dismissible"><p>' . __('Test email sent successfully!', 'nkhwazischool') . '</p></div>';
                } else {
                    echo '<div class="notice notice-error is-dismissible"><p>' . __('Failed to send test email. Please check your server\'s mail configuration.', 'nkhwazischool') . '</p></div>';
                }
            }
            ?>
            
            <p><?php _e('Send a test email to verify your notification settings:', 'nkhwazischool'); ?></p>
            <p>
                <button type="submit" name="send_test_email" class="button button-secondary"><?php _e('Send Test Email', 'nkhwazischool'); ?></button>
            </p>
            
            <p class="submit">
                <button type="submit" class="button button-primary"><?php _e('Save Settings', 'nkhwazischool'); ?></button>
            </p>
        </form>
    </div>
    <?php
}

/**
 * Enqueue scripts and styles for the personal details form
 */
function nkhwazi_enqueue_personal_details_form_script() {
    if (is_page('update-personal-details')) {
        // Dequeue the application form script to avoid conflicts
        wp_dequeue_script('nkhwazischool-application-form');
        // Enqueue CSS
        wp_enqueue_style(
            'nkhwazischool-personal-details-form',
            get_template_directory_uri() . '/assets/css/application-form.css',
            array(),
            filemtime(get_template_directory() . '/assets/css/application-form.css')
        );
        
        // Enqueue jQuery UI
        wp_enqueue_script('jquery-ui-core');
        wp_enqueue_script('jquery-ui-datepicker');
        wp_enqueue_style(
            'jquery-ui-style',
            'https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css',
            array(),
            '1.12.1'
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'nkhwazischool-personal-details-form',
            get_template_directory_uri() . '/assets/js/personal-details-form.js',
            array('jquery', 'jquery-ui-datepicker'),
            filemtime(get_template_directory() . '/assets/js/personal-details-form.js'),
            true
        );
        
        // Test script removed
        
        // Pass AJAX URL to script
        wp_localize_script(
            'nkhwazischool-personal-details-form',
            'nkhwazi_personal_details_form',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('nkhwazi_personal_details_form_nonce'),
                'recaptcha_site_key' => '6Lf79rcaAAAAALdqWY74mw_n6DtTxR_C5AEL6cfL'
            )
        );
        
        // Test script localization removed
    }
}
add_action('wp_enqueue_scripts', 'nkhwazi_enqueue_personal_details_form_script');

/**
 * Handle personal details form submission via AJAX
 */
function nkhwazi_submit_personal_details_form() {
    // Debug mode
    $debug_mode = true;
    
    // Debug information
    if ($debug_mode) {
        error_log('Personal Details Form Submission - POST data: ' . print_r($_POST, true));
    }
    
    // Check if this is a debug request
    if (isset($_POST['debug']) && $_POST['debug'] === 'true') {
        wp_send_json_success(array(
            'message' => 'Debug mode: Form submission received successfully',
            'post_data' => $_POST
        ));
        wp_die();
    }
    
    // Check nonce
    if (!isset($_POST['nonce'])) {
        wp_send_json_error(array(
            'message' => __('Security check failed: Nonce is missing. Please refresh the page and try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    if (!wp_verify_nonce($_POST['nonce'], 'nkhwazi_personal_details_form_nonce')) {
        error_log('Nonce verification failed. Received nonce: ' . $_POST['nonce']);
        error_log('Expected nonce for: nkhwazi_personal_details_form_nonce');
        
        wp_send_json_error(array(
            'message' => __('Security check failed: Invalid nonce. Please refresh the page and try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Verify reCAPTCHA
    if (!isset($_POST['g-recaptcha-response']) || empty($_POST['g-recaptcha-response'])) {
        wp_send_json_error(array(
            'message' => __('Please complete the reCAPTCHA verification.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Verify with Google reCAPTCHA API
    $recaptcha_secret = '6Lf79rcaAAAAACnBRBWJW8M7XLGq4o-tPhvn7r-J';
    $recaptcha_response = $_POST['g-recaptcha-response'];
    
    $verify_response = wp_remote_post('https://www.google.com/recaptcha/api/siteverify', array(
        'body' => array(
            'secret' => $recaptcha_secret,
            'response' => $recaptcha_response,
            'remoteip' => $_SERVER['REMOTE_ADDR']
        )
    ));
    
    if (is_wp_error($verify_response)) {
        wp_send_json_error(array(
            'message' => __('reCAPTCHA verification failed. Please try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    $response_body = wp_remote_retrieve_body($verify_response);
    $response_data = json_decode($response_body, true);
    
    if (!isset($response_data['success']) || !$response_data['success']) {
        wp_send_json_error(array(
            'message' => __('reCAPTCHA verification failed. Please try again.', 'nkhwazischool')
        ));
        wp_die();
    }
    
    // Check if we have at least the basic student information
    if (empty($_POST['fname']) || empty($_POST['lname'])) {
        wp_send_json_error(array(
            'message' => __('Please provide at least the student\'s first and last name.', 'nkhwazischool')
        ));
        return;
    }
    
    // Sanitize basic input
    $lname = isset($_POST['lname']) ? sanitize_text_field($_POST['lname']) : '';
    $fname = isset($_POST['fname']) ? sanitize_text_field($_POST['fname']) : '';
    $grade = isset($_POST['grade']) ? sanitize_text_field($_POST['grade']) : 'Unknown';
    
    // Create post title
    $post_title = sprintf(__('Personal Details Update for %s %s - %s', 'nkhwazischool'), $fname, $lname, $grade);
    
    // Create new personal details update post
    $post_id = wp_insert_post(array(
        'post_title' => $post_title,
        'post_type' => 'personal_details',
        'post_status' => 'publish'
    ));
    
    if (is_wp_error($post_id)) {
        error_log('Error creating post: ' . $post_id->get_error_message());
        wp_send_json_error(array(
            'message' => __('Failed to save your update. Please try again.', 'nkhwazischool')
        ));
        return;
    }
    
    // Save all POST data as post meta
    foreach ($_POST as $key => $value) {
        if ($key !== 'action' && $key !== 'nonce' && $key !== 'subject' && $key !== 'g-recaptcha-response') {
            // Initialize meta key
            $meta_key = '';
            
            // Handle father's details
            if (strpos($key, '_f') !== false) {
                // Convert lname_f to _father_lname
                $field_name = str_replace('_f', '', $key);
                $meta_key = '_father_' . $field_name;
            }
            // Handle mother's details
            else if (strpos($key, '_m') !== false) {
                // Convert lname_m to _mother_lname
                $field_name = str_replace('_m', '', $key);
                $meta_key = '_mother_' . $field_name;
            }
            // Handle doctor's details
            else if (strpos($key, 'doctor') !== false) {
                // Convert family_doctor to _doctor_name
                if ($key === 'family_doctor') {
                    $meta_key = '_doctor_name';
                } else {
                    $field_name = str_replace('_doctor', '', $key);
                    $meta_key = '_doctor_' . $field_name;
                }
            }
            // Handle emergency contact details
            else if (strpos($key, '_emergency') !== false) {
                // Convert lname_emergency to _emergency_lname
                $field_name = str_replace('_emergency', '', $key);
                $meta_key = '_emergency_' . $field_name;
            }
            // Handle additional information
            else if ($key === 'more_info') {
                $meta_key = '_more_info';
            }
            // Handle pupil details (everything else)
            else {
                $meta_key = '_pupil_' . $key;
            }
            
            // Save the meta
            update_post_meta($post_id, $meta_key, sanitize_text_field($value));
        }
    }
    
    // Save IP address
    update_post_meta($post_id, '_update_ip', $_SERVER['REMOTE_ADDR']);
    
    // Send email notification
    $to = get_option('nkhwazi_personal_details_email', get_option('admin_email'));
    
    // Get email settings
    $subject_prefix = get_option('nkhwazi_personal_details_subject_prefix', '[' . get_bloginfo('name') . ']');
    $email_subject = sprintf(__('%s PERSONAL DETAILS UPDATE: %s %s', 'nkhwazischool'), $subject_prefix, $fname, $lname);
    
    // Create a detailed HTML email
    $email_body = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Personal Details Update</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; }
        .header { background-color: #0073aa; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .section { margin-bottom: 25px; border-bottom: 1px solid #eee; padding-bottom: 15px; }
        .section h2 { color: #0073aa; margin-top: 0; }
        .field { margin-bottom: 10px; }
        .field-label { font-weight: bold; }
        .footer { background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; }
        .button { display: inline-block; background-color: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>PERSONAL DETAILS UPDATE RECEIVED</h1>
    </div>
    <div class="content">
        <p><strong>This is a PERSONAL DETAILS UPDATE, not a new application.</strong></p>
        <p>You have received a new personal details update from your website form.</p>
        
        <div class="section">
            <h2>Student\'s Details</h2>';
    
    // Student's details
    $student_fields = array(
        'fname' => 'First Name',
        'lname' => 'Last Name',
        'dob' => 'Date of Birth',
        'grade' => 'Grade',
        'religion' => 'Religion',
        'address' => 'Home Address',
        'siblings' => 'Siblings at School'
    );
    
    foreach ($student_fields as $field => $label) {
        $value = isset($_POST[$field]) ? sanitize_text_field($_POST[$field]) : 'Not provided';
        if ($field === 'siblings' && empty($value)) {
            $value = 'None';
        }
        $email_body .= '<div class="field"><span class="field-label">' . $label . ':</span> ' . $value . '</div>';
    }
    
    // Father's details
    $email_body .= '</div><div class="section"><h2>Father\'s Details</h2>';
    $father_fields = array(
        'lname_f' => 'Last Name',
        'fname_f' => 'First Name',
        'occupation_f' => 'Occupation',
        'address_business_f' => 'Business Address',
        'phone_f' => 'Phone Number',
        'phone_work_f' => 'Work Phone',
        'email_f' => 'Email Address'
    );
    
    foreach ($father_fields as $field => $label) {
        $value = isset($_POST[$field]) ? sanitize_text_field($_POST[$field]) : '';
        if (!empty($value)) {
            $email_body .= '<div class="field"><span class="field-label">' . $label . ':</span> ' . $value . '</div>';
        }
    }
    
    // Mother's details
    $email_body .= '</div><div class="section"><h2>Mother\'s Details</h2>';
    $mother_fields = array(
        'lname_m' => 'Last Name',
        'fname_m' => 'First Name',
        'occupation_m' => 'Occupation',
        'address_business_m' => 'Business Address',
        'phone_m' => 'Phone Number',
        'phone_work_m' => 'Work Phone',
        'email_m' => 'Email Address'
    );
    
    foreach ($mother_fields as $field => $label) {
        $value = isset($_POST[$field]) ? sanitize_text_field($_POST[$field]) : '';
        if (!empty($value)) {
            $email_body .= '<div class="field"><span class="field-label">' . $label . ':</span> ' . $value . '</div>';
        }
    }
    
    // Doctor's details
    $email_body .= '</div><div class="section"><h2>Family Doctor or Clinic Details</h2>';
    $doctor_fields = array(
        'family_doctor' => 'Doctor/Clinic Name',
        'phone_doctor' => 'Mobile Number',
        'tel_doctor' => 'Office Phone',
        'email_doctor' => 'Email Address'
    );
    
    foreach ($doctor_fields as $field => $label) {
        $value = isset($_POST[$field]) ? sanitize_text_field($_POST[$field]) : '';
        if (!empty($value)) {
            $email_body .= '<div class="field"><span class="field-label">' . $label . ':</span> ' . $value . '</div>';
        }
    }
    
    // Emergency contact details
    $email_body .= '</div><div class="section"><h2>Emergency Contact Details</h2>';
    $emergency_fields = array(
        'lname_emergency' => 'Last Name',
        'fname_emergency' => 'First Name',
        'phone_emergency' => 'Mobile Number',
        'tel_emergency' => 'Alternative Phone',
        'email_emergency' => 'Email Address'
    );
    
    foreach ($emergency_fields as $field => $label) {
        $value = isset($_POST[$field]) ? sanitize_text_field($_POST[$field]) : '';
        if (!empty($value)) {
            $email_body .= '<div class="field"><span class="field-label">' . $label . ':</span> ' . $value . '</div>';
        }
    }
    
    // Additional information
    $email_body .= '</div><div class="section"><h2>Additional Information</h2>';
    $more_info = isset($_POST['more_info']) ? sanitize_text_field($_POST['more_info']) : '';
    $email_body .= '<div class="field"><span class="field-label">Additional Information:</span> ' . (!empty($more_info) ? $more_info : 'None provided') . '</div>';
    
    // Add link to view in dashboard
    $email_body .= '</div>
        <div class="section">
            <h2>Update Details</h2>
            <div class="field"><span class="field-label">Submission Date:</span> ' . current_time('F j, Y \a\t g:i a') . '</div>
            <div class="field"><span class="field-label">IP Address:</span> ' . $_SERVER['REMOTE_ADDR'] . '</div>
            <p><a href="' . admin_url('post.php?post=' . $post_id . '&action=edit') . '" class="button">View Full Update in Dashboard</a></p>
        </div>
    </div>
    <div class="footer">
        <p>' . get_option('nkhwazi_personal_details_email_footer', 'This is an automated email from ' . get_bloginfo('name') . ' website.') . '</p>
    </div>
</body>
</html>';
    
    // Set up email headers for HTML email
    $headers = array(
        'Content-Type: text/html; charset=UTF-8',
        'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
    );
    
    // Process CC recipients
    $cc_emails = get_option('nkhwazi_personal_details_cc_emails', '');
    $all_recipients = array($to); // Start with the main recipient
    
    if (!empty($cc_emails)) {
        // Process CC emails
        $cc_emails_array = explode("\n", $cc_emails);
        $valid_cc_emails = array();
        
        foreach ($cc_emails_array as $cc_email) {
            $cc_email = trim($cc_email);
            if (!empty($cc_email) && is_email($cc_email)) {
                $valid_cc_emails[] = $cc_email;
                $all_recipients[] = $cc_email; // Add to the recipients list
            }
        }
    }
    
    // Log email details for debugging
    error_log('Sending PERSONAL DETAILS UPDATE email to: ' . implode(', ', $all_recipients));
    error_log('Email subject: ' . $email_subject);
    
    // Send the email to all recipients
    $email_sent = false;
    
    // First try sending to all recipients at once
    try {
        $email_sent = wp_mail($to, $email_subject, $email_body, $headers);
    } catch (Exception $e) {
        error_log('Exception when sending email: ' . $e->getMessage());
    }
    
    // If sending to all recipients failed, try sending individually
    if (!$email_sent && count($all_recipients) > 1) {
        error_log('Sending to all recipients failed, trying individual emails');
        
        foreach ($all_recipients as $recipient) {
            try {
                wp_mail($recipient, $email_subject, $email_body, $headers);
            } catch (Exception $e) {
                error_log('Exception when sending individual email to ' . $recipient . ': ' . $e->getMessage());
            }
        }
    }
    
    // Return success response
    wp_send_json_success(array(
        'message' => __('Your personal details have been updated successfully. Thank you! A confirmation email has been sent to our administration team.', 'nkhwazischool'),
        'post_id' => $post_id
    ));
    
    wp_die();
}
add_action('wp_ajax_nkhwazi_submit_personal_details_form', 'nkhwazi_submit_personal_details_form');
add_action('wp_ajax_nopriv_nkhwazi_submit_personal_details_form', 'nkhwazi_submit_personal_details_form');

// Test nonce function removed