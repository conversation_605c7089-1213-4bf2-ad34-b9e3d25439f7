/*
 * Custom styles for Nkhwazi Primary School website
 */
/* Form progress steps */
.form-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  position: relative;
  /* Remove any secondary color line */
}
.form-progress::after {
  display: none !important;
}

/* Additional rule to target any potential secondary color line */
.form-progress::after {
  display: none !important;
}

.form-progress-step {
  position: relative;
  z-index: 2;
  text-align: center;
  transition: all 0.3s ease;
}
.form-progress-step.clickable {
  cursor: pointer;
}
.form-progress-step.clickable:hover .form-progress-step-number {
  background-color: #3649e2;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.form-progress-step.clickable:hover .form-progress-step-label {
  color: #3649e2;
  font-weight: bold;
}
.form-progress-step.active .form-progress-step-number {
  background-color: #3649e2;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.form-progress-step.active .form-progress-step-label {
  color: #3649e2;
  font-weight: bold;
}
.form-progress-step.completed .form-progress-step-number {
  background-color: #2ecc71;
  color: white;
}

/* The form-progress-step-number styles are defined in application-form.css */
.form-progress-step-label {
  font-size: 14px;
  color: #666;
}

/* Form steps */
.form-step {
  display: none !important;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.form-step.active {
  display: block !important;
  opacity: 1;
}

/*
//Ubuntu styles
.ubuntu-light {
  font-family: "Ubuntu", sans-serif;
  font-weight: 300;
  font-style: normal;
}

.ubuntu-regular {
  font-family: "Ubuntu", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.ubuntu-medium {
  font-family: "Ubuntu", sans-serif;
  font-weight: 500;
  font-style: normal;
}

.ubuntu-bold {
  font-family: "Ubuntu", sans-serif;
  font-weight: 700;
  font-style: normal;
}

.ubuntu-light-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 300;
  font-style: italic;
}

.ubuntu-regular-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 400;
  font-style: italic;
}

.ubuntu-medium-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 500;
  font-style: italic;
}

.ubuntu-bold-italic {
  font-family: "Ubuntu", sans-serif;
  font-weight: 700;
  font-style: italic;
}

*/
/* Noto serif styles */
/**
 *
.noto-serif-georgian-<uniquifier> {
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: <weight>;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}
 */
/**
  * HEX Code: #3649e2
  *RGB Code: rgb(54,73,226)
   complementary color: #E2CF36 (rgb(226, 207, 54))
 */
.text-yellow-darker {
  color: rgb(203, 186, 48) !important;
}

.text-primary {
  color: rgb(54, 73, 226) !important;
}

/*General Styles*/
body,
p {
  font-family: "ubuntu", sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 1rem;
  text-align: left;
}

/*** Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: rgb(226, 207, 54);
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  font-weight: normal;
  line-height: 1.3;
  font-variation-settings: "wdth" 100;
  margin: 0.5rem 0;
  text-align: left;
}

h1 {
  font-size: 2.2rem;
  text-align: center;
}

h2 {
  font-size: 1.6rem;
}

h3 {
  font-size: 1.3rem;
}

h4 {
  font-size: 1.1rem;
}

/*** Lists */
/**targeting ONLY content within the main part of the page */
main li {
  font-size: 1rem;
  list-style-type: disc;
}

main ul, main ol {
  margin-top: 1rem;
  margin-bottom: 1rem;
  margin-left: 1rem;
}

.material-symbols-outlined {
  font-family: "Material Symbols Outlined", sans-serif;
  font-weight: 300 !important;
  font-style: normal;
  font-size: 24px !important; /* Adjust size as needed */
  display: inline-block;
  vertical-align: middle !important;
  line-height: 1;
  color: rgb(226, 207, 54) !important;
}

.material-symbols-outlined:hover {
  color: #e2cf36 !important;
}

.material-symbols-outlined-alt {
  font-family: "Material Symbols Outlined", sans-serif;
  font-weight: 400 !important;
  font-style: normal;
  font-size: 48px !important; /* Adjust size as needed */
  display: inline-block;
  vertical-align: middle !important;
  line-height: 1;
  color: rgb(203, 186, 48) !important;
}

/**Header styles */
.website-name {
  color: rgb(54, 73, 226) !important;
}

.rounded-div {
  border-radius: 10px;
  overflow: hidden;
  /* Add word breaking properties to prevent text overflow */
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  -webkit-hyphens: auto;
          hyphens: auto;
}

/* Management sidebar specific styles */
.management-sidebar a {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  -webkit-hyphens: auto;
          hyphens: auto;
  display: block;
  width: 100%;
}

/* Logo and school name container */
.nav-text {
  background: none repeat scroll 0 0 rgb(54, 73, 226);
}

.logo-container {
  display: flex;
  align-items: left;
  justify-content: center;
  flex-wrap: wrap;
}

.logo-container .logo {
  margin-right: 15px;
}

.logo-container h1 {
  margin: 0;
  color: rgb(54, 73, 226);
  font-size: 2.2rem;
  text-align: left;
  margin-top: 30px;
  font-weight: 600;
}

/* Centered menu styles */
.centered-menu {
  display: flex;
  justify-content: center; /* Changed from left to center */
  flex-wrap: wrap;
  float: none !important;
}

.centered-menu > li {
  float: none !important;
  display: inline-block;
}

.social-bar-footer {
  border-bottom: 2px solid rgba(54, 73, 226, 0.7);
}

/** Event styles */
.event-date {
  margin-top: 10px;
  background-color: rgb(226, 207, 54);
  color: #ffffff;
  padding: 6px 10px;
}

.event-card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 20px;
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.event-card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}

.category-padding {
  padding: 15px 10px !important;
}

/* Reusable image styles */
.rounded-image-top {
  border-radius: 10px 10px 0 0 !important;
  overflow: hidden;
}

/* General rounded image style for non-card images */
.rounded-image, .carousel-default .item img, .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img {
  border-radius: 10px;
  overflow: hidden;
}
/* For nested images, ensure they have rounded top corners only */
.rounded-image img, .carousel-default .item img img, .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img img {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
}

.full-img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
}

.full-width-img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  display: block;
  margin: 0 auto;
  max-width: 1720px;
}

/* Extracurricular page styles */
.extracurricular-summary {
  background-color: #f8f8f8;
  border-left: 4px solid rgb(54, 73, 226);
  padding: 20px;
  margin-bottom: 30px;
  border-radius: 0 10px 10px 0;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-left: 4px solid rgb(54, 73, 226); /* Maintain the left border */
  transition: all 0.3s ease;
}
.extracurricular-summary:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}
.extracurricular-summary p {
  margin-bottom: 10px;
  font-size: 1.1rem;
  line-height: 1.5;
}
.extracurricular-summary p:last-child {
  margin-bottom: 0;
}
.extracurricular-summary p strong {
  color: rgb(54, 73, 226);
  font-weight: 600;
}

/* Single Extracurricular Activity Styles */
.background-primary-hightlight {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
}
.background-primary-hightlight p {
  font-size: 1.1rem;
  line-height: 1.5;
  font-weight: 500;
  color: #222;
}
.background-primary-hightlight .text-dark {
  font-weight: 600;
  color: black;
}
.background-primary-hightlight a {
  color: rgb(54, 73, 226);
  text-decoration: none;
}
.background-primary-hightlight a:hover {
  text-decoration: underline;
}

/* Extracurricular Item Page Image */
.extracurricular-page-image {
  width: 100%;
  height: auto;
  max-height: 500px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Activity items on extracurricular pages */
.activities-list .activity-item {
  transition: all 0.3s ease;
}
.activities-list .activity-item:hover {
  transform: translateY(-3px);
}
.activities-list .activity-item .activity-header {
  border-radius: 8px;
  overflow: hidden;
  padding: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.activities-list .activity-item .activity-header:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}
.activities-list .activity-item .activity-thumbnail {
  width: 100%;
  height: auto;
  border-radius: 5px;
  -o-object-fit: cover;
     object-fit: cover;
  aspect-ratio: 16/9;
}
.activities-list .activity-item h4 {
  margin-bottom: 5px;
  font-size: 1.1rem;
  line-height: 1.3;
}
.activities-list .activity-item p {
  margin: 0 0 5px 0;
}

/* Buttons with primary background */
.button.background-primary {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 30px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid rgb(54, 73, 226);
}
.button.background-primary:hover {
  background-color: transparent !important;
  color: rgb(54, 73, 226) !important;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Blog author and category links */
.blog-author,
.blog-category {
  color: #0000ff !important;
}
.blog-author:hover,
.blog-category:hover {
  text-decoration: underline;
}

/* About Nkhwazi Primary School section styles */
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:first-child {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  text-align: left;
}
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:first-child h2,
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:first-child p,
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:first-child a {
  text-align: left;
}

/* Apply rounded corners to the About Nkhwazi Primary School image */
.nkhwazi-stats {
  background-color: #eaeaea;
  padding: 0.8rem;
  border-radius: 10px;
}

.number-stat {
  border: 1px solid #ffffff;
  background-color: #f2f2f2;
  border-radius: 10px;
}

/** Links */
/* Font colors */
.background-white,
.background-white p,
a.background-white,
.background-white a,
.background-white a:active {
  color: #444;
}

.background-white a:link {
  color: #0000ff;
}

.background-white a:visited {
  color: #800080;
}

.background-white a:hover {
  color: #ff0000;
}

.category-filter {
  color: #222;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

/* Teacher filter section */
.teacher-filter-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 20px;
}
.teacher-filter-buttons .button {
  margin: 5px;
  transition: all 0.3s ease;
}

.teacher-filter-btn {
  background-color: white !important;
  color: black !important;
  border: 1px solid black !important;
  border-radius: 5px !important;
  padding: 8px 15px !important;
}

.teacher-filter-buttons .button.active {
  background-color: rgb(54, 73, 226) !important;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
.teacher-filter-buttons .button:hover {
  background-color: rgb(54, 73, 226) !important;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.background-grey {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.background-grey:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}

.box-shadow {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.box-shadow:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}

/* Notification styles */
.notification-card {
  transition: all 0.3s ease;
  background-color: #ffffff !important;
  border: 1px solid #cccccc; /* Grey border around all sides */
  border-left: 4px solid rgb(226, 207, 54); /* Thick left border with secondary color */
  border-radius: 10px !important;
  padding: 20px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
}
.notification-card h3 {
  color: rgb(54, 73, 226);
  font-weight: 600;
}
.notification-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08) !important;
}
.notification-card small {
  color: #777;
  font-size: 0.85rem;
  display: block;
  margin-bottom: 12px;
}
.notification-card p {
  color: #333;
  line-height: 1.5;
}

.notification-detail {
  background-color: #ffffff !important;
  border-radius: 0 10px 10px 0 !important;
  border-left: 4px solid rgb(54, 73, 226);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}
.notification-detail h2 {
  color: rgb(54, 73, 226);
  font-weight: 600;
  margin-bottom: 10px;
}
.notification-detail .margin-bottom-20 {
  line-height: 1.6;
}
.notification-detail .margin-bottom-20 p {
  margin-bottom: 15px;
}
.notification-detail .margin-bottom-20 ul, .notification-detail .margin-bottom-20 ol {
  margin-left: 20px;
  margin-bottom: 15px;
}
.notification-detail h2 {
  color: rgb(54, 73, 226);
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 10px;
}
.notification-detail small {
  color: #777;
  font-size: 0.85rem;
  display: block;
  margin-bottom: 15px;
}

.display-block {
  display: block;
}

/* Breadcrumbs Styling */
.position-relative {
  position: relative;
}

.breadcrumbs-container {
  position: absolute;
  bottom: 5px; /* Changed from -15px to keep it inside the header */
  left: 10px;
  z-index: 10;
}

.background-white-transparent {
  background-color: rgba(255, 255, 255, 0.85); /* Semi-transparent white */
}

.breadcrumbs {
  font-size: 0.8rem;
  text-align: left;
}
.breadcrumbs .breadcrumb-item {
  display: inline-block;
}
.breadcrumbs .breadcrumb-item a {
  color: #0000ff;
  text-decoration: none;
}
.breadcrumbs .breadcrumb-item a:hover {
  text-decoration: underline;
  color: #ff0000;
}
.breadcrumbs .breadcrumb-separator {
  display: inline-block;
  margin: 0 5px;
  color: #cccccc;
}

.padding-1x {
  padding: 10px 15px;
}

.padding-2x {
  padding: 20px 25px;
}

/* Gallery thumbnails in single-gallery.php */
.image-with-hover-overlay img {
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-with-hover-overlay {
  border-radius: 10px 10px 0 0 !important;
  overflow: hidden;
}

/* Responsive adjustments for breadcrumbs */
@media screen and (min-width: 992px) {
  /* Adjustments for large screens */
  .breadcrumbs-container {
    bottom: 15px;
  }
}
@media screen and (max-width: 768px) {
  /* Adjustments for small screens */
  .breadcrumbs-container {
    position: relative;
    bottom: auto;
  }
}
/* Lightcase Gallery Fixes */
/* Fix for iframe scrollbars in lightcase galleries */
#lightcase-content {
  overflow: hidden !important;
}

#lightcase-content .lightcase-contentInner {
  overflow: hidden !important;
}

/* Application Form Field with Counter Styles */
.field-with-counter {
  position: relative;
  margin-bottom: 20px; /* Add space between fields */
  width: 100%;
}
.field-with-counter input, .field-with-counter textarea, .field-with-counter select {
  width: 100%;
  margin-bottom: 0 !important; /* Remove default bottom margin */
}

/* Application Form Character Counter Styles */
.form-header {
  background-color: #ffffff;
  clear: both;
}

.character-counter {
  font-size: 0.8rem;
  text-align: right;
  margin-top: 5px;
  color: #666;
  transition: all 0.3s ease;
  padding: 2px 5px;
  border-radius: 3px;
  display: block;
}
.character-counter.warning {
  color: #e2a736;
  font-weight: bold;
  background-color: rgba(226, 167, 54, 0.1);
}
.character-counter.error {
  color: #e74c3c;
  font-weight: bold;
  background-color: rgba(231, 76, 60, 0.1);
}

.field-error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 5px;
  display: block;
  /* When inside a field-with-counter */
}
.field-with-counter .field-error-message {
  margin-bottom: 5px;
}

/* Form field validation styles */
input.error,
textarea.error,
select.error {
  border-color: #e74c3c !important;
  border-left-width: 3px !important;
}

input.valid,
textarea.valid,
select.valid {
  border-color: #2ecc71 !important;
  border-left-width: 3px !important;
}

/* Default border style for form fields */
input.default-border,
textarea.default-border,
select.default-border {
  border-color: #ddd !important;
  border-left-width: 1px !important;
}

/* Character counter styles */
.field-with-counter {
  position: relative;
  margin-bottom: 15px;
}

.character-counter {
  position: absolute;
  right: 10px;
  top: -20px;
  font-size: 12px;
  color: #777;
}

.character-counter.warning {
  color: #f39c12;
}

.character-counter.error {
  color: #e74c3c;
}

/* Field error message */
.field-error-message {
  display: block;
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
}

/* Form message container */
.form-message {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: none;
}

.form-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.form-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Any error message */
.any-error {
  color: #e74c3c;
  font-weight: bold;
}

/* Ensure proper sizing for lightcase content */
#lightcase-case {
  max-width: 100vw !important;
  max-height: 100vh !important;
}

@media screen and (max-width: 768px) {
  /* Adjustments for small screens */
  .breadcrumbs-container {
    left: auto;
    margin-top: 10px;
    margin-bottom: -25px;
  }
  .breadcrumbs {
    font-size: 0.7rem;
    width: 100%;
  }
}
.background-white a:active {
  color: #ff00ff;
}

/* Blog Styles */
/* Blog post metadata container */
.blog-meta-container {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  /* Responsive adjustments */
}
.blog-meta-container .meta-item {
  font-size: 0.95rem;
}
.blog-meta-container .meta-item.date {
  color: #222;
}
.blog-meta-container .meta-item.author a, .blog-meta-container .meta-item.category a {
  color: #0000ff;
}
.blog-meta-container .meta-item.author a:hover, .blog-meta-container .meta-item.category a:hover {
  color: #ff0000;
  text-decoration: underline;
}
.blog-meta-container .meta-item.author a .text-dark, .blog-meta-container .meta-item.category a .text-dark {
  color: #222;
  font-weight: normal;
}
@media screen and (max-width: 768px) {
  .blog-meta-container .meta-item {
    font-size: 0.9rem;
  }
}
@media screen and (max-width: 480px) {
  .blog-meta-container {
    flex-direction: column;
  }
  .blog-meta-container .meta-item {
    width: 100%;
    margin-bottom: 5px;
    text-align: center !important;
  }
  .blog-meta-container .meta-item:last-child {
    margin-bottom: 0;
  }
}

/* Blog Category Navigation - Horizontal */
.blog-categories-horizontal {
  margin-bottom: 30px;
  /* Responsive adjustments */
  /* Medium screens - 2 per line */
  /* Small screens - stacked */
}
.blog-categories-horizontal .blog-categories-menu {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%; /* Ensure full width */
  box-sizing: border-box; /* Include padding in width calculation */
}
.blog-categories-horizontal .blog-categories-menu li {
  margin: 0 10px 10px 0;
  list-style-type: none;
  box-sizing: border-box; /* Include padding in width calculation */
}
.blog-categories-horizontal .blog-categories-menu .blog-category-link {
  display: inline-block;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 5px;
  text-align: center;
  transition: all 0.3s ease;
  color: #222;
  font-weight: 500;
  cursor: pointer;
  width: 100%; /* Ensure full width */
  box-sizing: border-box; /* Include padding in width calculation */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; /* Add ellipsis for long category names */
}
.blog-categories-horizontal .blog-categories-menu .blog-category-link:hover {
  background-color: #e0e0e0;
  color: #0000ff;
  text-decoration: none;
}
.blog-categories-horizontal .blog-categories-menu .blog-category-link.active {
  background-color: rgb(54, 73, 226);
  color: white;
}
.blog-categories-horizontal .blog-categories-menu .blog-category-link.active:hover {
  background-color: #1d30c8;
  text-decoration: none;
}
@media screen and (max-width: 768px) and (min-width: 481px) {
  .blog-categories-horizontal .blog-categories-menu {
    display: flex;
    flex-wrap: wrap;
  }
  .blog-categories-horizontal .blog-categories-menu li {
    width: calc(50% - 10px); /* 2 per line with some margin */
    margin-right: 10px;
    margin-bottom: 10px;
  }
  .blog-categories-horizontal .blog-categories-menu li:nth-child(2n) {
    margin-right: 0; /* Remove right margin for every second item */
  }
  .blog-categories-horizontal .blog-categories-menu .blog-category-link {
    display: block;
    width: 100%;
  }
}
@media screen and (max-width: 480px) {
  .blog-categories-horizontal .blog-categories-menu {
    flex-direction: column;
  }
  .blog-categories-horizontal .blog-categories-menu li {
    margin-right: 0;
    width: 100%;
  }
  .blog-categories-horizontal .blog-categories-menu .blog-category-link {
    display: block;
    width: 100%;
  }
}

.category-filter {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #222;
}

.no-posts-message {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 5px;
  text-align: center;
  margin-top: 20px;
}
.no-posts-message p {
  margin: 0;
  color: #222;
}

/* Blog Images with Rounded Corners */
.single-post article img,
.blog article img,
.blog-sidebar img,
.s-12.m-4.l-4 img,
.s-12.m-12.l-12 img,
.wp-block-image img {
  border-radius: 10px !important;
  overflow: hidden !important;
}

/* Blog Home Button */
.rounded-btn {
  border-radius: 30px;
  padding: 10px 20px;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;
}
.rounded-btn:hover {
  background-color: #1d30c8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Blog Home Link in content area */
.blog-home-link-container {
  border-top: 1px solid #eeeeee;
  padding-top: 20px;
}
.blog-home-link-container a {
  font-size: 1.1rem;
  font-weight: 500;
  display: inline-block;
  transition: all 0.3s ease;
}
.blog-home-link-container a:hover {
  transform: translateX(-5px);
  text-decoration: underline;
}
.blog-home-link-container a i {
  transition: all 0.3s ease;
}
.blog-home-link-container a:hover i {
  transform: translateX(-3px);
}

/* Blog Category Navigation */
.blog-category-nav {
  margin-bottom: 20px !important;
}
.blog-category-nav .category-link, .blog-category-nav .blog-category-link {
  color: #0000ff;
  text-decoration: none;
  padding: 10px 5px !important;
  transition: all 0.3s ease;
  display: inline-block;
}
.blog-category-nav .category-link:hover, .blog-category-nav .blog-category-link:hover {
  color: #ff0000;
}
.blog-category-nav .category-link.active, .blog-category-nav .blog-category-link.active {
  font-weight: bold;
  border-bottom: 2px solid #0000ff;
}

/* School Categories Styles */
/* Home page school category card */
.home-category-card {
  transition: all 0.3s ease;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 15px;
  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}
.home-category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}
/* Fix for category card images to ensure consistent layout */
.home-category-card img.rounded-image-top,
.school-category img.rounded-image-top {
  border-radius: 10px 10px 0 0 !important;
  overflow: hidden;
  margin-top: -15px;
  margin-left: -15px;
  margin-right: -15px;
  width: calc(100% + 30px);
  max-width: calc(100% + 30px);
  display: block;
  object-fit: cover;
  height: auto;
  box-sizing: border-box;
}

/* Restore original blog post image styling */
.blog-post img.rounded-image-top {
  border-radius: 10px 10px 0 0 !important;
  overflow: hidden;
  width: 100%;
}

/* Fix for home category card image spacing issue - Alternative approach */
.home-category-card {
  padding: 0 !important;
}

.home-category-card img.rounded-image-top {
  margin: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
  border-radius: 10px 10px 0 0 !important;
  display: block !important;
  object-fit: cover !important;
  height: auto !important;
}

.home-category-card h3,
.home-category-card p,
.home-category-card .margin-top-bottom-20 {
  padding-left: 15px !important;
  padding-right: 15px !important;
}

.home-category-card h3 {
  padding-top: 15px !important;
}

.home-category-card .margin-top-bottom-20 {
  padding-bottom: 15px !important;
}

.school-category {
  transition: all 0.3s ease;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 0;
  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

/* Ensure school-category has the same padding as blog-post */
.school-category.blog-post {
  padding: 15px;
}
.school-category:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}
.school-category img {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  margin-bottom: 0 !important;
  border-radius: 10px 10px 0 0;
}
.school-category h3 {
  margin-top: 15px;
  text-align: center;
  padding: 0 15px;
}
.school-category p {
  flex-grow: 1;
  padding: 0 15px;
}
.school-category .text-more-info {
  display: inline-block;
  margin: 15px auto;
  text-align: center;
  width: auto;
  background-color: rgb(54, 73, 226);
  color: white !important;
  padding: 8px 20px;
  border-radius: 5px;
  font-weight: 500;
  transition: all 0.3s ease;
}
.school-category .text-more-info:hover {
  background-color: #1d30c8;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Category Info Box Styles */
.category-info-box {
  border: 1px solid #eaeaea;
  margin-bottom: 20px;
}
.category-info-box h3 {
  color: rgb(54, 73, 226);
  font-size: 1.2rem;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 5px;
}
.category-info-box p {
  margin-bottom: 10px;
}

/* Category Sidebar Styles */
.category-sidebar-item {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.category-sidebar-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Single Extracurricular Page Styles */
.activity-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.activity-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.activity-link {
  display: block;
  text-decoration: none;
  color: inherit;
}
.activity-link:hover {
  text-decoration: none;
  color: inherit;
}

/* Activity Single Page Styles */
.activity-content {
  line-height: 1.6;
}
.activity-content p {
  margin-bottom: 1rem;
}
.activity-content h3, .activity-content h4 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}
.activity-content ul, .activity-content ol {
  margin-left: 1.5rem;
  margin-bottom: 1.5rem;
}
.activity-content img {
  max-width: 100%;
}

/* 404 Error Page Styles */
.error-404 {
  text-align: center;
}
.error-404 .error-icon {
  font-size: 120px;
  color: rgb(203, 186, 48);
  margin-bottom: 20px;
}
.error-404 .error-title {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: rgb(54, 73, 226);
}
.error-404 .error-description {
  font-size: 1.2rem;
  margin-bottom: 30px;
}
.error-404 .error-actions {
  margin-top: 30px;
}
.error-404 .error-actions .text-more-info {
  margin: 0 10px;
}
.error-404 .search-form {
  max-width: 500px;
  margin: 0 auto;
}
.error-404 .search-form input[type=search] {
  border: 1px solid #cccccc;
  border-radius: 5px;
  padding: 10px 15px;
  width: 70%;
}
.error-404 .search-form button {
  background-color: rgb(54, 73, 226);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.error-404 .search-form button:hover {
  background-color: #1d30c8;
}

/* Job Listings and Job Detail Page Styles */
/* Compatible with WordPress custom post types */
.job-card,
.type-job,
.hentry.job {
  transition: all 0.3s ease;
}
.job-card .job-card-inner,
.job-card .entry-content-wrap,
.type-job .job-card-inner,
.type-job .entry-content-wrap,
.hentry.job .job-card-inner,
.hentry.job .entry-content-wrap {
  border-radius: 10px;
  padding: 20px;
  background-color: #ffffff; /* Ensure white background */
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}
.job-card .job-card-inner:hover,
.job-card .entry-content-wrap:hover,
.type-job .job-card-inner:hover,
.type-job .entry-content-wrap:hover,
.hentry.job .job-card-inner:hover,
.hentry.job .entry-content-wrap:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  transform: translateY(-3px);
}
.job-card h3,
.job-card .entry-title,
.type-job h3,
.type-job .entry-title,
.hentry.job h3,
.hentry.job .entry-title {
  margin-top: 0;
}
.job-card h3 a,
.job-card .entry-title a,
.type-job h3 a,
.type-job .entry-title a,
.hentry.job h3 a,
.hentry.job .entry-title a {
  color: inherit;
  text-decoration: none;
}
.job-card h3 a:hover,
.job-card .entry-title a:hover,
.type-job h3 a:hover,
.type-job .entry-title a:hover,
.hentry.job h3 a:hover,
.hentry.job .entry-title a:hover {
  text-decoration: none;
}

.job-meta {
  background-color: rgba(54, 73, 226, 0.05);
  padding: 10px;
  border-radius: 5px;
}

.job-excerpt {
  font-style: italic;
  border-left: 3px solid rgb(54, 73, 226);
  padding-left: 15px;
}

.job-description,
.entry-content {
  /* WordPress image alignment classes */
}
.job-description h3,
.entry-content h3 {
  margin-top: 25px;
  margin-bottom: 10px;
  color: rgb(54, 73, 226);
}
.job-description ul, .job-description ol,
.entry-content ul,
.entry-content ol {
  margin-left: 20px;
}
.job-description img.alignright,
.entry-content img.alignright {
  float: right;
  margin: 0 0 1em 1em;
}
.job-description img.alignleft,
.entry-content img.alignleft {
  float: left;
  margin: 0 1em 1em 0;
}
.job-description img.aligncenter,
.entry-content img.aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.job-description .alignright,
.entry-content .alignright {
  float: right;
  margin: 0 0 1em 1em;
}
.job-description .alignleft,
.entry-content .alignleft {
  float: left;
  margin: 0 1em 1em 0;
}
.job-description .aligncenter,
.entry-content .aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.job-application {
  background-color: #eeeeee;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid rgb(226, 207, 54);
}

.job-actions {
  margin-top: 20px;
}
.job-actions .button {
  margin-right: 10px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}
.job-actions .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.activity-header {
  padding: 10px;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
}
.activity-header:hover {
  background-color: #f0f0f0;
}

.activity-thumbnail {
  border-radius: 5px;
}

.activity-details {
  border-top: 1px solid #e0e0e0;
  border-radius: 0 0 8px 8px;
}

.cursor-pointer {
  cursor: pointer;
}

.background-light-grey {
  background-color: #eeeeee;
  border-radius: 8px;
}

.category-sidebar-image {
  width: 100%;
  height: 100px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 8px 8px 0 0;
}

.category-sidebar-title {
  padding: 10px;
  text-align: center;
  font-weight: 500;
  color: #222;
  background-color: #f8f8f8;
}

.category-sidebar-link {
  text-decoration: none;
  color: inherit;
}
.category-sidebar-link:hover {
  text-decoration: none;
}

/* Blog Post Styling */
.blog-post {
  transition: all 0.3s ease;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  padding: 15px;
  border-radius: 10px;
  position: relative;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.blog-image {
  margin-top: 8px;
}

.blog-post:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
}

/* Gallery card specific styling */
.gallery-card {
  padding: 0 0 0 0 !important; /* Remove all padding for gallery cards */
  background-color: #ffffff !important; /* Apply white background to entire card */
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05) !important; /* Enhanced shadow */
  overflow: hidden; /* Ensure content doesn't overflow */
  transition: all 0.3s ease;
  margin-bottom: 15px !important; /* Space between the info button and the bottom edge of the card*/
  border-radius: 10px !important; /* Round all corners of the card */
  border: 1px solid rgba(0, 0, 0, 0.08); /* Subtle border for better definition */
}

.gallery-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08) !important; /* Enhanced shadow on hover */
}

/* Team Card Styling */
.card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 0; /* Changed from 5px to remove extra space */
  padding: 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.08); /* Subtle border for better definition */
  /* Ensure no internal spacing that might create gaps */
  box-sizing: border-box;
  /* Content wrapper for all text and buttons */
  /* Remove spacer that was pushing content apart */
}
.card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.08);
  transform: translateY(-5px);
}
.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}
.card .team-photo {
  width: 100%;
  display: block;
  transition: all 0.3s ease;
  margin: 0;
  padding: 0;
}
.card h3 {
  margin-top: 15px;
  font-size: 1.3rem;
}
.card h3, .card .title, .card .read-all {
  padding-left: 15px;
  padding-right: 15px;
  margin-top: 15px; /* Add space between image and text */
}
.card .team-link {
  color: rgb(54, 73, 226);
  text-decoration: none;
  transition: color 0.3s ease;
}
.card .team-link:hover {
  color: #1d30c8;
}
.card .title {
  color: #222;
  font-size: 1.1rem !important;
  font-weight: 300;
  margin: 5px 0 5px 0;
  padding: 0 10px;
}
.card:after {
  display: none; /* Hide the spacer completely */
}
.card .read-all {
  display: inline-block;
  margin: 5px 0 0; /* Removed bottom margin completely */
  padding: 8px 20px;
  background-color: rgb(54, 73, 226);
  color: white;
  border-radius: 10px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}
.card .read-all:hover {
  background-color: #1d30c8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Fix for modal content */
.modal {
  border-radius: 12px;
  max-width: 800px;
  margin: 0 auto;
}
.modal h2, .modal h3 {
  color: rgb(54, 73, 226);
}
.modal .margin-bottom.padding {
  padding: 20px 30px;
}
.modal .modal-close-button {
  background-color: rgb(54, 73, 226);
  color: white;
  border-radius: 30px;
  padding: 8px 20px;
  display: inline-block;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}
.modal .modal-close-button:hover {
  background-color: #1d30c8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Gallery label styling */
.gallery-label {
  position: absolute;
  top: 25px;
  right: 15px;
  background-color: rgb(54, 73, 226);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  z-index: 2;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Extracurricular label styling */
.extracurricular-label {
  background-color: rgb(226, 207, 54);
  color: #333;
  font-weight: 600;
}

/* Teacher category header styling */
.teacher-category-header {
  background-color: rgb(54, 73, 226);
  color: white;
  padding: 10px 0;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  width: 100%;
  border-radius: 10px 10px 0 0;
  margin: 0;
}

/* Teaching staff card specific styling */
.card.gallery-card[data-category] {
  /* Team photo with sharp corners */
}
.card.gallery-card[data-category] .gallery-content {
  padding: 15px;
  padding-bottom: 0; /* Removed bottom padding */
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.card.gallery-card[data-category] .gallery-content h3 {
  margin-top: 10px;
  margin-bottom: 5px;
}
.card.gallery-card[data-category] .gallery-content .title {
  margin-bottom: 8px; /* Reduced bottom margin */
}
.card.gallery-card[data-category] .gallery-content .read-all {
  display: inline-block;
  margin-bottom: 0; /* Removed bottom margin */
  margin-top: auto; /* Push the button to the bottom of the flex container */
}
.card.gallery-card[data-category] .team-photo {
  width: 100%;
  display: block;
  margin: 0;
  padding: 0;
  border-radius: 0 !important; /* Sharp corners */
}

/* Management page specific styles */
.page-management .gallery-card {
  padding-bottom: 0 !important;
}
.page-management .gallery-card .gallery-content {
  padding-bottom: 0 !important;
}
.page-management .gallery-card .gallery-content div {
  margin-bottom: 0 !important;
}
.page-management .gallery-card .gallery-content .read-all {
  margin-bottom: 0 !important;
}

/* Gallery image hover effect */
.gallery-card a img.rounded-image-top,
.gallery-card a img.rounded-image,
.gallery-card a .carousel-default .item img,
.carousel-default .item .gallery-card a img,
.gallery-card a .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img,
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child .gallery-card a img,
.gallery-card a .team-photo.rounded-image {
  transition: all 0.3s ease;
  border-radius: 10px 10px 0 0 !important; /* Rounded top corners only for gallery images */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add shadow to the image */
  width: 100%; /* Ensure image takes full width */
  display: block; /* Remove any extra space */
}

.gallery-card a:hover img.rounded-image-top,
.gallery-card a:hover img.rounded-image,
.gallery-card a:hover .carousel-default .item img,
.carousel-default .item .gallery-card a:hover img,
.gallery-card a:hover .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img,
.grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child .gallery-card a:hover img,
.gallery-card a:hover .team-photo.rounded-image {
  opacity: 0.85;
  transform: scale(1.02);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Gallery card text content */
.gallery-content {
  padding: 15px 15px 0px; /* Removed bottom padding to fix extra space */
  margin-top: 0;
  margin-bottom: 0; /* Added to remove bottom margin */
  /* Fix for the container of the View Bio button */
}
.gallery-content h3 {
  margin-top: 0;
  margin-bottom: 8px; /* Reduced bottom margin */
}
.gallery-content p {
  margin-bottom: 8px; /* Reduced bottom margin */
}
.gallery-content .text-more-info {
  display: inline-block;
  margin-bottom: 0; /* Removed bottom margin */
}
.gallery-content div {
  margin-bottom: 0 !important; /* Remove bottom margin from button container */
}

.blog-post img.rounded-image, .blog-post .carousel-default .item img, .carousel-default .item .blog-post img, .blog-post .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child img, .grid.margin article.s-12.m-12.l-6.margin-m-bottom:last-child .blog-post img {
  width: 100%;
  border-radius: 10px 10px 0 0;
}

.blog-post h3 {
  margin-top: 15px;
  font-size: 1.3rem;
  line-height: 1.3;
}

.blog-post p {
  margin: 10px 0;
  line-height: 1.5;
}

.blog-author a,
.blog-category a {
  color: #555;
  text-decoration: none;
  transition: color 0.3s ease;
}

.blog-author a:hover,
.blog-category a:hover {
  color: #0074d9;
}

/* Nationality Dropdown Styling */
select[name=nationality],
select[name=nationality_f],
select[name=nationality_m] {
  max-height: 300px;
  overflow-y: auto !important;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
}
select[name=nationality] option,
select[name=nationality_f] option,
select[name=nationality_m] option {
  padding: 8px 12px;
}
select[name=nationality] option:hover,
select[name=nationality_f] option:hover,
select[name=nationality_m] option:hover {
  background-color: #f5f5f5;
}

/* Nationality Dropdown Styling */
select[name=nationality],
select[name=nationality_f],
select[name=nationality_m] {
  max-height: 300px;
  overflow-y: auto !important;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
}
select[name=nationality] option,
select[name=nationality_f] option,
select[name=nationality_m] option {
  padding: 8px 12px;
}
select[name=nationality] option:hover,
select[name=nationality_f] option:hover,
select[name=nationality_m] option:hover {
  background-color: #f5f5f5;
}

/* Pagination Styling - Removed duplicate styles */
/* Blog Details Link Styling */
.blog-categories {
  background-color: rgb(203, 186, 48);
  padding: 0.8rem;
  margin-bottom: 20px;
  color: #ffffff;
}

.blog-post .text-more-info {
  display: inline-block;
  margin-top: 10px;
  font-weight: 500;
  position: relative;
  padding-right: 20px;
}

.blog-post .text-more-info:hover:after {
  transform: translateX(5px);
}

/* Blog Post Category Tags */
.blog-category a {
  background-color: #f1f1f1;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 0.85em;
  transition: background-color 0.3s ease;
}

.blog-category a:hover {
  background-color: #e0e0e0;
}

/* Footer styles */
footer .section.background-blue {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
}
footer .bottom-footer {
  margin-bottom: 0.5rem;
}
footer .padding.background-yellow {
  border-radius: 0 0 10px 10px;
  overflow: hidden;
}

.footer-links {
  border-top: 1px solid rgb(226, 207, 54);
  padding-top: 0.625rem;
}
.footer-links p {
  margin-bottom: 15px; /* Adjust this value to increase/decrease the gap */
}

.admin-contact-info {
  padding-top: 1rem;
  margin-top: 2rem;
}

.admin-info-item {
  background: rgba(54, 73, 226, 0.6);
  padding: 0.8rem !important;
  padding-top: 0.8rem !important;
  border-radius: 10px;
  overflow: hidden;
}

.footer-links {
  border-top: 1px solid rgb(226, 207, 54);
  padding-top: 1rem;
}
.footer-links p {
  margin-bottom: 10px; /* Adjust this value to increase/decrease the gap */
}

footer h4 {
  color: rgb(226, 207, 54) !important;
  font-family: "Noto Serif Georgian", serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
  font-variation-settings: "wdth" 100;
}

.admin-name,
.admin-phone {
  word-wrap: break-word;
}

/* Slider styles */
/*** Navigation bar styles */
.top-nav li a,
.background-white .top-nav li a {
  color: #002633;
  font-size: 1rem;
  padding: 0.7em 1.25em;
}

nav {
  border-bottom: 4px solid rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.5rem 0;
  position: relative;
  z-index: 15;
}

.top-nav ul ul {
  background: rgb(226, 207, 54) none repeat scroll 0 0;
}

.top-nav li ul li {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.top-nav li ul li:last-child {
  border-bottom: 0;
}

.top-nav li ul li a,
.background-white .top-nav li ul li a,
.top-nav .active-item li a {
  background: #eeeeee none repeat scroll 0 0;
  color: rgb(54, 73, 226);
}

@media screen and (min-width: 769px) {
  footer h4 {
    text-align: center !important;
  }
  .developer-credit {
    text-align: right;
  }
}
/* Application Form Styles */
.application-form {
  color: rgb(54, 73, 226);
  font-weight: 600;
}

.any-error {
  color: #C81010;
  font-weight: bold;
}

.any-success {
  color: #06a10b;
  font-weight: bold;
}

/* Style for required fields */
form.customform input.required,
form.customform select.required,
form.customform textarea.required {
  border-left: 4px solid #C81010;
}

/* Improve form field spacing */
form.customform input,
form.customform select,
form.customform textarea {
  margin-bottom: 1rem;
}

/* Style for form section headings */
h3.application-form {
  margin-top: 2rem;
  font-size: 1.4rem;
}

h4.text-left {
  margin-top: 1.5rem;
  color: rgb(54, 73, 226);
  font-weight: 600;
}

/* Style for submit and reset buttons */
.submit-btn {
  background-color: rgb(54, 73, 226) !important;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background-color: #1d30c8 !important;
}

.cancel-btn {
  background-color: #C81010 !important;
  transition: background-color 0.3s ease;
}

.cancel-btn:hover {
  background-color: #990c0c !important;
}

/* Datepicker styling */
.ui-datepicker {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
}

.ui-datepicker-header {
  background-color: rgb(54, 73, 226);
  color: white;
  border-radius: 3px;
  padding: 5px;
}

.ui-datepicker-calendar th {
  padding: 5px;
  text-align: center;
}

.ui-datepicker-calendar td {
  padding: 2px;
  text-align: center;
}

.ui-datepicker-calendar a {
  display: block;
  padding: 5px;
  text-decoration: none;
  border-radius: 3px;
}

.ui-datepicker-calendar a:hover {
  background-color: rgb(226, 207, 54);
  color: white;
}

/* Mobile styles */
@media screen and (max-width: 768px) {
  /* Ensure the hamburger menu is visible */
  .nav-text {
    display: block !important;
  }
  .top-nav li a,
  .background-white .top-nav li a {
    background: #eeeeee none repeat scroll 0 0;
    color: #000;
    font-size: 1.2em;
    padding: 1em;
    text-align: center;
  }
  .top-nav li ul li ul li a {
    background: none repeat scroll 0 0 #456274;
  }
  .top-nav {
    background: none repeat scroll 0 0 #eeeeee;
  }
  .top-nav li ul li a,
  .background-white .top-nav li ul li a,
  .top-nav .active-item li a {
    background: #cccccc none repeat scroll 0 0;
    color: #000;
  }
  h1 {
    font-size: 1.7rem;
    text-align: center;
  }
  h2 {
    font-size: 1.4rem;
  }
  h3 {
    font-size: 1.1rem;
  }
  h4 {
    font-size: 1rem;
  }
  .blog-post h3 {
    font-size: 1.1rem;
  }
  .homepage-headers,
  .page-headers {
    font-size: 1.4rem;
  }
  .number-stat {
    margin-bottom: 15px;
  }
  /* More info button */
  a.text-more-info {
    margin-bottom: 2rem;
  }
  hr.break-alt {
    margin: 5px 0 !important;
  }
  /* Mobile centered menu styles */
  .centered-menu {
    display: block;
  }
  .centered-menu > li {
    display: block;
  }
  .admin-info-item {
    margin-bottom: 1rem;
    text-align: center;
  }
  footer h4 {
    text-align: left;
  }
}
@media screen and (max-width: 480px) {
  .developer-credit {
    margin-top: 1rem !important;
  }
}
/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .blog-post {
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 480px) {
  .pagination li a {
    padding: 6px 12px;
  }
  .blog-author, .blog-category, .blog-date {
    font-size: 0.8rem;
  }
}
/* Fix for teacher cards grid to prevent cards from bumping into each other */
.grid.margin {
  grid-row-gap: 20px; /* Add vertical spacing between rows for all grids */
}

/* Specific styling for teacher cards grid */
.teacher-cards-grid {
  grid-row-gap: 50px !important; /* Add more vertical spacing between rows for teacher cards */
  display: grid !important; /* Ensure grid display is enforced */
  margin-bottom: 30px !important; /* Add bottom margin to the entire grid */
}

/* Utility class for smaller bottom margins */
.margin-bottom-5 {
  margin-bottom: 5px !important;
}

/* Specific styling for management page cards */
.page-management .gallery-card .gallery-content {
  padding: 10px 10px 5px; /* Further reduced padding for management page */
}

/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 0;
  margin: 30px 0;
  flex-wrap: wrap;
}

.pagination li {
  margin: 0 5px;
  display: inline-block;
}

.pagination a {
  display: block;
  padding: 8px 15px;
  text-decoration: none;
  color: #222;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 5px;
  transition: all 0.3s ease;
}
.pagination a:hover {
  background-color: #e8eafc;
  color: rgb(54, 73, 226);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.pagination a.active-page {
  background-color: rgb(54, 73, 226);
  color: #ffffff;
  border-color: rgb(54, 73, 226);
  font-weight: bold;
}
.pagination a.previous-page, .pagination a.next-page {
  background-color: #eeeeee;
}
.pagination a.previous-page:hover, .pagination a.next-page:hover {
  background-color: #cccccc;
  color: #222;
}
.pagination a.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* WordPress Default Pagination Styles */
.pagination-nav {
  margin: 0; /* Remove margin since the container has padding */
  background-color: transparent !important; /* Override Responsee background */
}
.pagination-nav .page-numbers {
  display: flex !important;
  justify-content: center !important;
  flex-wrap: wrap !important;
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
  background-color: transparent !important; /* Ensure no background from Responsee */
  /* Style for previous and next buttons */
}
.pagination-nav .page-numbers li {
  margin: 0 5px 5px 0 !important;
  display: inline-block !important;
  list-style-type: none !important;
  background-color: transparent !important; /* Ensure no background from Responsee */
  padding: 0 !important; /* Reset padding */
}
.pagination-nav .page-numbers a, .pagination-nav .page-numbers span {
  display: inline-block !important;
  padding: 8px 15px !important;
  background-color: #ffffff !important;
  border: 1px solid #cccccc !important;
  border-radius: 5px !important;
  color: #222 !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}
.pagination-nav .page-numbers a:hover, .pagination-nav .page-numbers span:hover {
  background-color: #e8eafc !important;
  color: rgb(54, 73, 226) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
}
.pagination-nav .page-numbers .current {
  background-color: rgb(54, 73, 226) !important;
  color: #ffffff !important;
  border-color: rgb(54, 73, 226) !important;
  font-weight: bold !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}
.pagination-nav .page-numbers .dots {
  background: none !important;
  border: none !important;
  padding: 8px 5px !important;
}
.pagination-nav .page-numbers .prev, .pagination-nav .page-numbers .next {
  background-color: rgb(226, 207, 54) !important;
  color: #ffffff !important;
  border-color: rgb(226, 207, 54) !important;
  font-weight: bold !important;
}
.pagination-nav .page-numbers .prev:hover, .pagination-nav .page-numbers .next:hover {
  background-color: #c8b51d !important;
  border-color: #c8b51d !important;
  color: #ffffff !important;
}

/* Add specific spacing control for pagination container */
.pagination-container {
  margin-top: 30px; /* Keep space above pagination */
  margin-bottom: 15px; /* Reduce space below pagination by half */
  background-color: #f5f5f5 !important; /* Light grey background - with !important to override Responsee */
  border-radius: 10px !important; /* Rounded corners */
  padding: 15px !important; /* Add some padding */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important; /* Subtle shadow */
}

/* Additional spacing control for specific sections */
.section .line:last-child .pagination-container {
  margin-bottom: 15px; /* Ensure consistent spacing before footer */
}

/* Ensure proper spacing after content */
.grid.margin + .pagination-container {
  margin-top: 30px; /* Maintain space after content */
}

/* Responsive adjustments for pagination */
@media screen and (max-width: 768px) {
  .pagination-nav .page-numbers li {
    margin-bottom: 10px;
  }
}
/* Contact Form Styles */
.contact-form {
  position: relative;
}
.contact-form .form-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 100;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
}
.contact-form .spinner {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid rgb(54, 73, 226);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
.contact-form .form-message {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: none;
  text-align: center;
  font-weight: bold;
}
.contact-form .form-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}
.contact-form .form-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
.contact-form .submit-btn {
  position: relative;
  transition: all 0.3s ease;
}
.contact-form .submit-btn.submitting {
  background-color: #cccccc !important;
  cursor: not-allowed;
}
.contact-form .submit-btn:hover {
  background-color: #1d30c8 !important;
}
.contact-form .spinner-text {
  display: inline-block;
  position: relative;
}
.contact-form .spinner-text:after {
  content: "...";
  position: absolute;
  right: -12px;
  animation: ellipsis 1.5s infinite;
}
@keyframes ellipsis {
  0% {
    content: ".";
  }
  33% {
    content: "..";
  }
  66% {
    content: "...";
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}/*# sourceMappingURL=custom-style.css.map */