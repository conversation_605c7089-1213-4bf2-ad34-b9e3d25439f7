<?php
/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no home.php file exists.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<main role="main">

  <!-- Main Carousel -->
  <?php get_template_part('includes/carousel'); ?>
  <div class="line">
    <hr class="break margin-top-40 margin-bottom-0">
  </div>

  <!-- Posts -->
  <section class="section-small-padding background-white">
    <div class="line">
      <div class="margin">
        <div class="s-12 m-12 l-12">

          <!--About Nkhwazi Primary School-->
          <div class="grid margin">
            <article class="s-12 m-12 l-6 margin-m-bottom">
              <h2 class="text-strong text-uppercase"><a class="text-dark text-primary-hover" href="<?php echo esc_url(home_url('/')); ?>">Nkhwazi Primary School</a></h2>
              <p>Founded in 1971, Nkhwazi Primary School is one of the oldest private schools in Lusaka. It has a
                long-standing tradition of academic excellence and community involvement.</p>
              <a class="text-more-info text-primary-hover margin-top-20" href="<?php echo esc_url(home_url('/')); ?>">More About Us</a>
            </article>
            <article class="s-12 m-12 l-6 margin-m-bottom">
              <img class="margin-bottom" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-06.jpg" alt="">
            </article>
          </div>

          <!-- School Categories -->
          <hr class="break">
          <div class="line">
            <h2 class="text-padding-small background-primary text-white text-strong text-uppercase margin-bottom-30"><a
                href="<?php echo esc_url(home_url('/school-categories/')); ?>"><i class="icon-spread margin-right-10"></i>School Categories</a></h2>
          </div>
          <div class="line">
            <div class="grid margin">
              <div class="s-12 m-6 l-4 margin-m-bottom">
                <img class="margin-bottom rounded-image-top" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-08.jpg" alt="Lower School">
                <h3 class="text-strong"><a class="text-dark text-primary-hover" href="<?php echo esc_url(home_url('/page-single-category/?category=lower')); ?>">Lower School</a>
                </h3>
                <p>Lower School covers children from age 5-7 years.</p>
                <p>Reception to Grade 2/ Ages 5-7 years</p>
                <div class="margin-top-bottom-20 center">
                  <a class="text-more-info" href="<?php echo esc_url(home_url('/page-single-category/?category=lower')); ?>">Category Details</a>
                </div>
              </div>

              <div class="s-12 m-6 l-4 margin-m-bottom">
                <img class="margin-bottom rounded-image-top" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-09.jpg" alt="Middle School">
                <h3 class="text-strong"><a class="text-dark text-primary-hover" href="<?php echo esc_url(home_url('/page-single-category/?category=middle')); ?>">Middle School
                  </a></h3>
                <p>Middle School covers children from age 8-9 years.</p>
                <p>Grade 3 to 4/ Ages 8-9 years</p>
                <div class="margin-top-bottom-20 center">
                  <a class="text-more-info" href="<?php echo esc_url(home_url('/page-single-category/?category=middle')); ?>">Category Details</a>
                </div>
              </div>

              <div class="s-12 m-6 l-4 margin-m-bottom">
                <img class="margin-bottom rounded-image-top" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-09.jpg" alt="Upper School">
                <h3 class="text-strong"><a class="text-dark text-primary-hover" href="<?php echo esc_url(home_url('/page-single-category/?category=upper')); ?>">Upper Primary
                  </a></h3>
                <p>Upper School covers children from age 10-12 years.</p>
                <p>Grade 5 to 7/ Ages 10-12 years</p>
                <div class="margin-top-bottom-20 center">
                  <a class="text-more-info" href="<?php echo esc_url(home_url('/page-single-category/?category=upper')); ?>">Category Details</a>
                </div>
              </div>

            </div>
          </div>
          
          <!-- Our Numbers -->
          <hr class="break">
          <div class="line">
            <h2 class="text-padding-small background-blue text-white text-strong text-uppercase margin-bottom-30"><a
                href="<?php echo esc_url(home_url('/')); ?>"><i class="icon-chart margin-right-20"></i>Our Numbers Say</a></h2>
          </div>

          <div class="grid margin nkhwazi-stats">

            <div class="s-12 m-6 l-3 number-stat">
              <div class="block">
                <div class="count-to text-center">
                  <span class="timer h1 text-size-60 text-strong text-yellow-darker" data-from="0" data-to="100"
                    data-speed="2000"></span> <span class="h1 text-size-50 text-strong text-yellow-darker">%</span>
                  <p class="h1 text-size-20 margin-top-10 text-thin text-center">Grade 7 Pass Rate, 2024</p>
                </div>
              </div>
            </div>
            <div class="s-12 m-6 l-3 number-stat">
              <div class="block">
                <div class="count-to text-center">
                  <span class="timer h1 text-size-60 text-strong text-yellow-darker" data-from="0" data-to="837"
                    data-speed="2000"></span> <span class="h1 text-size-50 text-strong text-yellow-darker"></span>
                  <p class="h1 text-size-20 margin-top-10 text-thin text-center">Top score at Grade 7, 2024</p>
                </div>
              </div>
            </div>
            <div class="s-12 m-6 l-3 number-stat">
              <div class="block">
                <div class="count-to text-center">
                  <span class="timer h1 text-size-60 text-strong text-yellow-darker" data-from="0" data-to="30"
                    data-speed="2000"></span> <span class="h1 text-size-50 text-strong text-yellow-darker"></span>
                  <p class="h1 text-size-20 margin-top-10 text-thin text-center">Average Class Size!</p>
                </div>
              </div>
            </div>

            <div class="s-12 m-6 l-3 number-stat">
              <div class="block">
                <div class="count-to text-center">
                  <span class="timer h1 text-size-60 text-strong text-yellow-darker" data-from="0" data-to="732"
                    data-speed="2000"></span> <span class="h1 text-size-50 text-strong text-yellow-darker"></span>
                  <p class="h1 text-size-20 margin-top-10 text-thin text-center">Total number of pupils</p>
                </div>
              </div>
            </div>

          </div>

        <!-- Our Blog -->
          <hr class="break">
          <div class="line">
            <h2 class="text-padding-small background-blue text-white text-strong text-uppercase margin-bottom-30"><a
                href="<?php echo esc_url(home_url('/blog/')); ?>"><i class="icon-newspaper margin-right-20"></i>Our Blog</a></h2>
          </div>
        
          <div class="grid margin">
            <?php
            // Get the latest 2 blog posts
            $args = array(
                'post_type' => 'post',
                'posts_per_page' => 2,
            );
            $query = new WP_Query($args);
            
            if ($query->have_posts()) :
                while ($query->have_posts()) : $query->the_post();
            ?>
              <article class="s-12 m-12 l-6 margin-m-bottom blog-post">
                <?php if (has_post_thumbnail()) : ?>
                  <img class="rounded-image margin-bottom blog-image" src="<?php the_post_thumbnail_url(); ?>" alt="<?php the_title_attribute(); ?>">
                <?php else : ?>
                  <img class="rounded-image margin-bottom blog-image" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-12.jpg" alt="<?php the_title_attribute(); ?>">
                <?php endif; ?>
                
                <h3 class="text-strong"><a class="text-dark text-orange-hover" href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                </h3>
                <div class="grid margin">
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12"><?php echo get_the_date(); ?></div>
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-author"><a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>"
                      class="blog-author">By <?php the_author(); ?></a></div>
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-category">
                    <?php
                    $categories = get_the_category();
                    if (!empty($categories)) {
                        echo '<a href="' . esc_url(get_category_link($categories[0]->term_id)) . '" class="blog-category">' . esc_html($categories[0]->name) . '</a>';
                    }
                    ?>
                  </div>
                </div>

                <p><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
                <a class="text-more-info text-primary-hover margin-bottom-30" href="<?php the_permalink(); ?>">Blog Details</a>
              </article>
            <?php
                endwhile;
                wp_reset_postdata();
            else :
            ?>
              <article class="s-12 m-12 l-6 margin-m-bottom blog-post">
                <img class="rounded-image margin-bottom blog-image" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-12.jpg" alt="">
                <h3 class="text-strong"><a class="text-dark text-orange-hover" href="#">Sample Blog Post</a>
                </h3>
                <div class="grid margin">
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12">14 April 2025</div>
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-author"><a href="#"
                      class="blog-author">By Admin</a></div>
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-category"><a href="#"
                      class="blog-category">Nkhwazi News</a></div>
                </div>

                <p>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat.</p>
                <a class="text-more-info text-primary-hover margin-bottom-30" href="#">Blog Details</a>
              </article>
              
              <article class="s-12 m-12 l-6 margin-m-bottom blog-post">
                <img class="rounded-image margin-bottom blog-image" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-13.jpg" alt="">
                <h3 class="text-strong"><a class="text-dark text-orange-hover" href="#">Sample Blog Post 2</a>
                </h3>
                <div class="grid margin">
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12">24 May 2025</div>
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12"><a href="#" class="blog-author">By
                      Admin</a></div>
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12"><a href="#"
                      class="blog-category">Nkhwazi News</a></div>
                </div>

                <p>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat.</p>
                <a class="text-more-info text-primary-hover margin-bottom-30" href="#">Blog Details</a>
              </article>
            <?php endif; ?>
          </div>

          <!-- Upcoming Events -->
          <hr class="break">
          <div class="line">
            <h2 class="text-padding-small background-blue text-white text-strong text-uppercase margin-bottom-30"><a
                href="<?php echo esc_url(home_url('/upcoming-events/')); ?>"><i class="icon-sli-calendar margin-right-20"></i>Upcoming Events</a></h2>
          </div>
        
          <div class="grid margin">
            <!-- Here you would integrate with an events plugin or custom post type -->
            <article class="s-12 m-12 l-6 margin-m-bottom event-card padding">
              <div class="grid text-left">
                <div class="s-12 m-12 l-1 text-left">
                  <i class="icon-sli-clock text-strong text-secondary text-size-20"></i>
                </div>
                <div class="s-11 m-11 l-11 margin-bottom-10">
                  <div class="admin-name margin-left-10 text-size-16">Due: <span class="text-size-14 rounded-div text-primary-dark  event-date">14-08-25 / 9:00 AM</span></div>
                </div>
              </div>
              <h3 class="text-strong"><a class="text-dark text-aqua-hover" href="#">Open Day 2025</a>
              </h3>
              <p>Come and experience what makes us different! We are hosting an open day for prospective parents and pupils ...</p>
            <a class="text-more-info text-primary-hover" href="#">Event Details</a>
            </article>
            <article class="s-12 m-12 l-6 margin-m-bottom event-card padding">
              <div class="grid text-left">
                <div class="s-12 m-12 l-1 text-left">
                  <i class="icon-sli-clock text-strong text-secondary text-size-20"></i>
                </div>
                <div class="s-11 m-11 l-11 margin-bottom-10">
                  <div class="admin-name margin-left-10 text-size-16">Due: <span class="text-size-14 rounded-div text-primary-dark event-date">22-07-25 / 10:00</span></div>
                </div>
              </div>
              <h3 class="text-strong"><a class="text-dark text-orange-hover" href="#">Provincial Sports Tournament</a>
              </h3>
              <p>The annual; sports tournament that draws participants from across Lusaka Province...</p>
              <a class="text-more-info text-primary-hover" href="#">Event Details</a>
            </article>
          </div>

        </div>


      </div>
    </div>
  </section>
</main>

<?php
get_footer();