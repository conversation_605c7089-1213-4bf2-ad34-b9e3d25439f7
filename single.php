<?php
/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1"><?php the_title(); ?></h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <div class="s-12 m-8 l-8">
            <!-- Single Blog Content -->
            <article>
              <?php
              // Only show meta container for posts (not custom post types unless specified)
              $post_type = get_post_type();
              $show_meta = ($post_type === 'post' || apply_filters('nkhwazi_show_post_meta', false, $post_type));
              
              if ($show_meta) : 
              ?>
              <!-- Blog post metadata in rounded container -->
              <div class="blog-meta-container">
                <div class="s-4 m-4 l-4 meta-item date text-center blog-date">
                  <?php echo get_the_date(get_option('date_format')); ?>
                </div>
                <div class="s-4 m-4 l-4 meta-item author text-center">
                  <?php 
                  $author_id = get_post_field('post_author', get_the_ID());
                  $author_name = get_the_author_meta('display_name', $author_id);
                  $author_url = get_author_posts_url($author_id);
                  ?>
                  <a href="<?php echo esc_url($author_url); ?>" class="blog-author">
                    <?php echo '<span class="text-dark"> By </span>' . esc_html($author_name); ?>
                  </a>
                </div>
                <div class="s-4 m-4 l-4 meta-item category text-center">
                  <?php
                  // Handle different taxonomies based on post type
                  if ($post_type === 'post') {
                    $categories = get_the_category();
                    if (!empty($categories)) {
                      echo '<a href="' . esc_url(get_category_link($categories[0]->term_id)) . '" class="blog-category">' .
                           '<span class="text-dark">In </span>' . esc_html($categories[0]->name) . '</a>';
                    }
                  } else {
                    // Get taxonomies for the current post type
                    $taxonomies = get_object_taxonomies($post_type, 'objects');
                    if (!empty($taxonomies)) {
                      // Use the first taxonomy
                      $taxonomy = reset($taxonomies);
                      $terms = get_the_terms(get_the_ID(), $taxonomy->name);
                      
                      if (!empty($terms) && !is_wp_error($terms)) {
                        $term = reset($terms);
                        echo '<a href="' . esc_url(get_term_link($term)) . '" class="blog-category">' .
                             '<span class="text-dark">In </span>' . esc_html($term->name) . '</a>';
                      }
                    }
                  }
                  ?>
                </div>
              </div>
              <?php endif; ?>

              <!-- Blog Image -->
              <?php 
              $blog_image = get_field('blog_image');
              if ($blog_image) : 
                $blog_image_url = wp_get_attachment_image_url($blog_image['ID'], 'blog-thumbnail'); 
                if (!$blog_image_url) {
                  $blog_image_url = $blog_image['url']; // Fallback to original if size doesn't exist
                }
              ?>
              <div class="rounded-image">
                <img class="margin-bottom" src="<?php echo esc_url($blog_image_url); ?>" alt="<?php echo esc_attr($blog_image['alt'] ? $blog_image['alt'] : get_the_title()); ?>">
              </div>
              <?php endif; ?>
              
              <div class="s-12 m-12 l-12">
                <!-- Blog Content -->
                <?php the_content(); ?>
              </div>
            </article>
          </div>

          <!-- Sidebar -->
          <?php get_template_part('includes/blog_sidebar'); ?>
        </div>
      </div>
    </div>
  </article>
</main>

<?php
get_footer();