<?php
/**
 * Template Name: Contact Page
 * Description: This template is intended for the contact us page
 */

// Include the contact form functionality
require_once('includes/contact-form.php');

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="assets/img/parallax-06.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Contact US</h1>
      </header>
    </div>
    <div class="section background-white">
      <div class="line">
        <div class="margin">
 <!-- Content starts below - Don't type anything above this comment -->

<div class="s-12 m-12 l-12">
  <!-- Gutenberg content -->
  <?php 
  while (have_posts()) :
      the_post();
      the_content();
  endwhile;
  ?>
</div>
          <!-- Company Information -->
          <div class="s-12 m-12 l-5">
            <h2 class="text-strong ">School Contact Details</h2>
            <hr class="break break-small background-primary">
            <div class="float-left">
              <i class="icon-placepin background-primary icon-circle-small text-size-20"></i>
            </div>
            <div class="margin-left-80 margin-bottom">
              <h4 class="text-strong margin-bottom-0">School Address</h4>
              <p>Responsive Street 7<br>
                London<br>
                UK, Europe
              </p>
            </div>
            <div class="float-left">
              <i class="icon-paperplane_ico background-primary icon-circle-small text-size-20"></i>
            </div>
            <!-- Email addresses -->
            <div class="margin-left-80 margin-bottom">
              <h4 class="text-strong margin-bottom-0">E-mail</h4>
              <?php
              // Get admin contacts
              $admin_contacts = nkhwazi_get_admin_contacts();
              
              if (!empty($admin_contacts)) {
                  foreach ($admin_contacts as $contact) {
                      echo '<p class="margin-bottom-10">' . esc_html($contact['job_title']) . ' <br><span class="margin-left-10"><a href="mailto:' . esc_attr($contact['email']) . '">' . esc_html($contact['email']) . '</a></span></p>';
                  }
              } else {
                  echo '<p class="margin-bottom-10">General Inquiries <br><span class="margin-left-10"><a href="mailto:<EMAIL>"><EMAIL></a></span></p>';
              }
              ?>
            </div>
            <div class="float-left">
              <i class="icon-smartphone background-primary icon-circle-small text-size-20"></i>
            </div>
            <div class="margin-left-80">
              <h4 class="text-strong margin-bottom-0">Phone Numbers</h4>
              <p>0800 4521 800 50<br>
                0450 5896 625 16<br>
                0798 6546 465 15
              </p>
            </div>
          </div>

          <!-- Contact Form -->
          <div class="s-12 m-12 l-7">
            <h2 class="text-strong margin-m-top-50">Contact Us</h2>
            <hr class="break break-small background-primary">
            
            <!-- Contact Form Container - with relative positioning -->
            <div style="position: relative;">
              <!-- Form Messages -->
              <div class="form-message" style="display: none; padding: 15px; margin-bottom: 20px; border-radius: 4px; text-align: center; font-weight: bold;"></div>
              
              <!-- Loading Indicator -->
              <div class="form-loading" style="display: none;">
                  <div class="spinner"></div>
                  <p style="margin-top: 15px; font-weight: bold;">Sending your message...</p>
              </div>
              
              <style>
              /* Form styles */
              .form-message {
                  padding: 15px;
                  margin-bottom: 20px;
                  border-radius: 4px;
                  display: none;
                  text-align: center;
                  font-weight: bold;
              }
              
              .form-message.success {
                  background-color: #d4edda;
                  color: #155724;
                  border: 1px solid #c3e6cb;
              }
              
              .form-message.error {
                  background-color: #f8d7da;
                  color: #721c24;
                  border: 1px solid #f5c6cb;
              }
              
              .form-loading {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  background-color: rgba(255, 255, 255, 0.8);
                  z-index: 100;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
              }
              
              .spinner {
                  display: inline-block;
                  width: 50px;
                  height: 50px;
                  border: 5px solid #f3f3f3;
                  border-top: 5px solid #3498db;
                  border-radius: 50%;
                  animation: spin 1s linear infinite;
              }
              
              @keyframes spin {
                  0% { transform: rotate(0deg); }
                  100% { transform: rotate(360deg); }
              }
              
              .spinner-text:after {
                  content: '...';
                  animation: ellipsis 1.5s infinite;
              }
              
              @keyframes ellipsis {
                  0% { content: '.'; }
                  33% { content: '..'; }
                  66% { content: '...'; }
              }
              
              .field-with-counter {
                  position: relative;
                  width: 100%;
                  margin-bottom: 10px;
              }
              
              .character-counter {
                  position: absolute;
                  right: 10px;
                  top: -20px;
                  font-size: 12px;
                  color: #777;
              }
              
              .field-error-message {
                  color: #e74c3c;
                  font-size: 12px;
                  margin-top: 5px;
                  display: block;
              }
              
              .recaptcha-error-message {
                  color: #e74c3c;
                  font-size: 12px;
                  margin-top: 5px;
                  text-align: center;
              }
              
              .submit-btn.submitting {
                  background-color: #cccccc !important;
                  cursor: not-allowed;
              }
              </style>
              
              <!-- Contact Form -->
              <form name="contactForm" class="customform contact-form" method="post" enctype="multipart/form-data" onsubmit="return false;" novalidate>
              <?php
              // Get admin contacts for recipient dropdown
              $admin_contacts = nkhwazi_get_admin_contacts();
              
              if (count($admin_contacts) > 1) {
                  // Show dropdown only if there are multiple contacts
                  echo '<div class="grid margin">
                      <div class="s-12 m-8 l-6">
                          <select class="required" name="recipient">
                              <option value="">Select Recipient</option>';
                  
                  foreach ($admin_contacts as $contact) {
                      echo '<option value="' . esc_attr($contact['email']) . '">' . esc_html($contact['job_title']) . '</option>';
                  }
                  
                  echo '</select>
                      </div>
                  </div>';
              } elseif (count($admin_contacts) == 1) {
                  // If only one contact, use it as the default recipient
                  echo '<input type="hidden" name="recipient" value="' . esc_attr($admin_contacts[0]['email']) . '">';
              }
              ?>

              <div class="grid margin">
                <div class="s-12 m-6 l-7">
                  <input name="name" class="required name" placeholder="Your name" title="Your name" type="text" />
                </div>
                <div class="s-12 m-6 l-5">
                  <input name="phone" class="" placeholder="Your Phone Number" title="Your Phone number (Optional)"
                    type="text" />
                </div>
                <div class="s-12 m-12 l-8">
                  <input name="email" class="required email" placeholder="Your e-mail" title="Your e-mail"
                    type="email" />
                </div>

                <div class="s-12">
                  <input name="subject" class="required subject" placeholder="Subject" title="Subject" type="text" />
                </div>
                <div class="s-12">
                  <textarea name="message" class="required message" placeholder="Your message" rows="6"></textarea>
                </div>
                
                <!-- reCAPTCHA -->
                <div class="s-12 m-12 l-12 text-center margin-bottom">
                    <div class="g-recaptcha-container">
                        <div class="g-recaptcha" data-sitekey="6Lf79rcaAAAAALdqWY74mw_n6DtTxR_C5AEL6cfL"></div>
                        <div class="recaptcha-error-message"></div>
                    </div>
                </div>
                
                <!--submit button -->
                <div class="s-12 m-6 l-6">
                  <button type="button" class="button rounded-btn submit-btn s-12 margin-bottom text-white background-blue">Send your Message</button>
                </div>
              </div>

            </form>
            </div><!-- End of form container with relative positioning -->
          </div>

        </div>
      </div>
    </div>



    <div class="line">
      <h2 class="text-dark text-strong margin-m-top-50">Where we are located</h2>

      <div class='margin-top-bottom-20'>

        <span class='hide-l hide-xl hide-xxl'><iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3846.2284300191445!2d28.294016364850272!3d-15.41821758928599!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1940f353839a7ea7%3A0x8a82f68499432955!2sNkhwazi+Primary+School%2C+Ituna+Rd%2C+Lusaka%2C+Zambia!5e0!3m2!1sen!2suk!4v1523659030624"
            width="350" height="300" frameborder="0" style="border:0" allowfullscreen>
          </iframe></span>
        <span class='hide-s hide-m'><iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3846.2284300191445!2d28.294016364850272!3d-15.41821758928599!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1940f353839a7ea7%3A0x8a82f68499432955!2sNkhwazi+Primary+School%2C+Ituna+Rd%2C+Lusaka%2C+Zambia!5e0!3m2!1sen!2suk!4v1523659030624"
            width="1280" height="450" frameborder="0" style="border:0" allowfullscreen>
          </iframe></span>
      </div>
    </div>

  </article>

</main>

<script>
// Complete form validation and submission handler
jQuery(document).ready(function() {
    // Use jQuery instead of $ to avoid conflicts
    var $ = jQuery;
    console.log('Contact form script initialized');
    
    // Debug form elements
    console.log('Form found:', $('.contact-form').length);
    console.log('Submit button found:', $('.contact-form button').length);
    console.log('Form loading indicator found:', $('.form-loading').length);
    // Define validation rules
    const validationRules = {
        'name': {
            required: true,
            minLength: 2,
            maxLength: 60,
            errorMessage: 'Name must be between 2 and 30 characters'
        },
        'email': {
            required: true,
            isEmail: true,
            errorMessage: 'Please enter a valid email address'
        },
        'phone': {
            required: false,
            minLength: 6,
            maxLength: 20,
            errorMessage: 'Phone number must be between 6 and 20 characters'
        },
        'subject': {
            required: true,
            minLength: 4,
            maxLength: 100,
            errorMessage: 'Subject must be between 4 and 100 characters'
        },
        'message': {
            required: true,
            minLength: 20,
            maxLength: 2000,
            errorMessage: 'Message must be between 20 and 2000 characters'
        }
    };
    
    // Initialize form
    const $form = $('.contact-form');
    
    // Add direct click handler to submit button
    $form.find('button').on('click', function(e) {
        console.log('Submit button clicked');
        // Manually trigger form submission
        $form.submit();
    });
    
    // Add character counters to fields
    $form.find('input, textarea').each(function() {
        const $field = $(this);
        const fieldName = $field.attr('name');
        
        // Skip if no validation rules for this field
        if (!validationRules[fieldName]) {
            return;
        }
        
        const rules = validationRules[fieldName];
        
        // Add data attributes for validation
        if (rules.required) {
            $field.addClass('required');
        }
        
        if (rules.isEmail) {
            $field.addClass('email');
        }
        
        // Create character counter container
        if (rules.minLength || rules.maxLength) {
            // Create wrapper if it doesn't exist
            if (!$field.parent().hasClass('field-with-counter')) {
                $field.wrap('<div class="field-with-counter" style="position: relative; width: 100%; margin-bottom: 10px;"></div>');
                $field.after('<div class="character-counter" style="position: absolute; right: 10px; top: -20px; font-size: 12px; color: #777;"></div>');
            }
            
            // Update counter on load
            updateCharacterCount($field);
            
            // Update counter on input
            $field.on('input', function() {
                updateCharacterCount($(this));
            });
        }
        
        // Add validation on blur
        $field.on('blur', function() {
            validateField($(this));
        });
        
        // Add validation on input (after first blur)
        $field.on('input', function() {
            if ($field.hasClass('touched')) {
                validateField($(this));
            }
        });
        
        // Mark as touched on blur
        $field.on('blur', function() {
            $field.addClass('touched');
        });
    });
    
    // Handle form submission
    $form.on('submit', function(e) {
        e.preventDefault();
        console.log('Form submitted - event triggered');
        
        // Debug form data
        console.log('Form data:', $(this).serialize());
        console.log('Form valid:', $(this).valid ? $(this).valid() : 'No jQuery validation');
        
        // Validate all fields
        let isValid = true;
        $form.find('input, textarea, select').each(function() {
            const $field = $(this);
            const fieldName = $field.attr('name');
            
            // Skip if no validation rules for this field
            if (!validationRules[fieldName]) {
                return;
            }
            
            // Mark as touched to trigger validation
            $field.addClass('touched');
            
            // Validate field
            if (!validateField($field)) {
                isValid = false;
            }
        });
        
        if (!isValid) {
            // Show error message
            $form.find('.form-message')
                .removeClass('success')
                .addClass('error')
                .html('Please fill in all required fields correctly.')
                .show();
            
            // Scroll to first error
            $('html, body').animate({
                scrollTop: $form.find('.error').first().offset().top - 100
            }, 300);
            
            return;
        }
        
        // Validate reCAPTCHA
        const recaptchaResponse = grecaptcha.getResponse();
        if (recaptchaResponse.length === 0) {
            // Show error message
            const $recaptchaContainer = $form.find('.g-recaptcha-container');
            $recaptchaContainer.find('.recaptcha-error-message').remove();
            $recaptchaContainer.append('<div class="recaptcha-error-message">Please complete the reCAPTCHA verification</div>');
            
            // Show form message
            $form.find('.form-message')
                .removeClass('success')
                .addClass('error')
                .html('Please complete the reCAPTCHA verification.')
                .show();
            
            return;
        }
        
        // Show loading indicator
        const $loadingIndicator = $form.find('.form-loading');
        const $submitButton = $form.find('button');
        
        console.log('Loading indicator element:', $loadingIndicator.length);
        console.log('Submit button element:', $submitButton.length);
        
        // Force display of loading indicator
        $loadingIndicator.attr('style', 'position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0.8); z-index: 100; display: flex !important; flex-direction: column; justify-content: center; align-items: center;');
        
        // Hide form message and disable button
        $form.find('.form-message').hide();
        $submitButton.prop('disabled', true);
        $submitButton.addClass('submitting');
        $submitButton.html('<span class="spinner-text">Sending...</span>');
        
        console.log('Loading indicator shown:', $loadingIndicator.is(':visible'));
        console.log('Loading indicator display:', $loadingIndicator.css('display'));
        
        // Collect form data
        const formData = new FormData($form[0]);
        
        // Add action and nonce
        formData.append('action', 'nkhwazi_submit_contact_form');
        formData.append('nonce', '<?php echo wp_create_nonce('nkhwazi_contact_form_nonce'); ?>');
        
        // Add reCAPTCHA response
        formData.append('g-recaptcha-response', recaptchaResponse);
        
        // Submit form via AJAX
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('AJAX Success Response:', response);
                
                // Hide loading indicator
                $loadingIndicator.hide();
                $submitButton.removeClass('submitting');
                $submitButton.html('Send your Message');
                $submitButton.prop('disabled', false);
                
                console.log('AJAX response received:', response);
                
                if (response.success) {
                    console.log('Showing success message');
                    
                    // Show success message with forced display
                    const $message = $form.find('.form-message');
                    $message.removeClass('error')
                           .addClass('success')
                           .html(response.data.message);
                    
                    // Force display with !important
                    $message.attr('style', 'display: block !important; padding: 15px; margin-bottom: 20px; border-radius: 4px; text-align: center; font-weight: bold; background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;');
                    
                    console.log('Success message element:', $message.length);
                    console.log('Success message visible:', $message.is(':visible'));
                    console.log('Success message display:', $message.css('display'));
                    
                    // Reset form
                    $form[0].reset();
                    $form.find('.valid, .error').removeClass('valid error touched');
                    $form.find('.field-error-message').remove();
                    
                    // Reset character counters
                    $form.find('.character-counter').each(function() {
                        $(this).text('');
                    });
                    
                    // Reset reCAPTCHA
                    grecaptcha.reset();
                    
                    // Scroll to top of form
                    $('html, body').animate({
                        scrollTop: $form.offset().top - 50
                    }, 300);
                    
                    // Set a timeout to hide the message after 5 seconds
                    setTimeout(function() {
                        $message.fadeOut(1000);
                    }, 5000);
                } else {
                    console.log('Showing error message');
                    
                    // Show error message with forced display
                    const $message = $form.find('.form-message');
                    $message.removeClass('success')
                           .addClass('error')
                           .html(response.data.message);
                    
                    // Force display with !important
                    $message.attr('style', 'display: block !important; padding: 15px; margin-bottom: 20px; border-radius: 4px; text-align: center; font-weight: bold; background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;');
                    
                    console.log('Error message element:', $message.length);
                    console.log('Error message visible:', $message.is(':visible'));
                    console.log('Error message display:', $message.css('display'));
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', status, error);
                console.log('Response Text:', xhr.responseText);
                
                // Hide loading indicator
                $loadingIndicator.hide();
                $submitButton.removeClass('submitting');
                $submitButton.html('Send your Message');
                $submitButton.prop('disabled', false);
                
                console.log('AJAX error occurred');
                
                // Show error message with forced display
                const $message = $form.find('.form-message');
                $message.removeClass('success')
                       .addClass('error')
                       .html('An error occurred while submitting the form. Please try again.');
                
                // Force display with !important
                $message.attr('style', 'display: block !important; padding: 15px; margin-bottom: 20px; border-radius: 4px; text-align: center; font-weight: bold; background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;');
                
                console.log('Error message element:', $message.length);
                console.log('Error message visible:', $message.is(':visible'));
                console.log('Error message display:', $message.css('display'));
            }
        });
    });
    
    // Helper function to validate a field
    function validateField($field) {
        const fieldName = $field.attr('name');
        const rules = validationRules[fieldName];
        
        // Skip if no validation rules for this field
        if (!rules) {
            return true;
        }
        
        // Get field value
        const value = $field.val();
        
        // Get container (either field-with-counter or parent)
        const $container = $field.parent().hasClass('field-with-counter') ? $field.parent() : $field.parent();
        
        // Remove existing error message
        $container.find('.field-error-message').remove();
        
        // Check if required and empty
        if (rules.required && value === '') {
            $field.removeClass('valid').addClass('error');
            $container.append('<span class="field-error-message" style="color: #e74c3c; font-size: 12px; margin-top: 5px; display: block;">This field is required</span>');
            return false;
        }
        
        // Skip further validation if empty and not required
        if (value === '' && !rules.required) {
            $field.removeClass('error valid');
            return true;
        }
        
        // Check min length
        if (rules.minLength && value.length < rules.minLength) {
            $field.removeClass('valid').addClass('error');
            $container.append('<span class="field-error-message" style="color: #e74c3c; font-size: 12px; margin-top: 5px; display: block;">' + rules.errorMessage + '</span>');
            return false;
        }
        
        // Check max length
        if (rules.maxLength && value.length > rules.maxLength) {
            $field.removeClass('valid').addClass('error');
            $container.append('<span class="field-error-message" style="color: #e74c3c; font-size: 12px; margin-top: 5px; display: block;">' + rules.errorMessage + '</span>');
            return false;
        }
        
        // Check email format
        if (rules.isEmail && !isValidEmail(value)) {
            $field.removeClass('valid').addClass('error');
            $container.append('<span class="field-error-message" style="color: #e74c3c; font-size: 12px; margin-top: 5px; display: block;">' + rules.errorMessage + '</span>');
            return false;
        }
        
        // Field is valid
        $field.removeClass('error').addClass('valid');
        return true;
    }
    
    // Helper function to update character count
    function updateCharacterCount($field) {
        const fieldName = $field.attr('name');
        const rules = validationRules[fieldName];
        
        // Skip if no validation rules for this field
        if (!rules) {
            return;
        }
        
        // Skip if no min/max length
        if (!rules.minLength && !rules.maxLength) {
            return;
        }
        
        const value = $field.val();
        const currentLength = value.length;
        
        // Get counter element
        const $counter = $field.parent().find('.character-counter');
        
        // Determine counter text and class
        let counterText = '';
        let counterClass = '';
        
        if (currentLength === 0 && !rules.required) {
            // Optional field that's empty
            counterText = 'Optional';
        } else if (rules.minLength && currentLength < rules.minLength) {
            // Below minimum length
            counterText = currentLength + '/' + rules.minLength + ' min';
            counterClass = 'warning';
        } else if (rules.maxLength && currentLength > rules.maxLength) {
            // Exceeds maximum length
            counterText = currentLength + '/' + rules.maxLength + ' (too long)';
            counterClass = 'error';
        } else if (rules.maxLength && currentLength >= rules.maxLength * 0.9) {
            // Approaching maximum length
            const remaining = rules.maxLength - currentLength;
            counterText = remaining + ' character' + (remaining !== 1 ? 's' : '') + ' left';
            counterClass = 'warning';
        } else if (rules.maxLength) {
            // Within limits with maximum
            counterText = currentLength + '/' + rules.maxLength;
        } else if (rules.minLength) {
            // Above minimum length with no maximum
            counterText = currentLength + ' characters';
        }
        
        // Update counter
        $counter.text(counterText);
        
        // Update counter class
        $counter.removeClass('warning error');
        if (counterClass) {
            $counter.addClass(counterClass);
            
            // Add inline styles based on class
            if (counterClass === 'warning') {
                $counter.css('color', '#f39c12');
            } else if (counterClass === 'error') {
                $counter.css('color', '#e74c3c');
            }
        } else {
            $counter.css('color', '#777');
        }
    }
    
    // Helper function to validate email
    function isValidEmail(email) {
        const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
        return emailRegex.test(email);
    }
});
</script>

<?php get_footer(); ?>