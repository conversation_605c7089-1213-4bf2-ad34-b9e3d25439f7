<?php
/**
 * Template Name: Extracurricular Activities
 * Description: A full-width layout template showcasing the school's extracurricular activities.
 * Author: <PERSON>
 */

require_once('includes/header.php');

// This would be replaced with actual data from WordPress database
$extracurricular_activities = [
    [
        'name' => 'Annual Football Tournament',
        'card_image' => 'assets/img/card-images/football-tournament.jpg',
        'page_image' => 'assets/img/page-images/football-tournament-header.jpg',
        'activity_scope' => 'External',
        'excerpt' => 'Our annual inter-school football tournament brings together teams from across the region to compete in a friendly but competitive environment.',
        'extracurricular_type' => 'Sports Teams'
    ],
    [
        'name' => 'Science Fair',
        'card_image' => 'assets/img/card-images/science-fair.jpg',
        'page_image' => 'assets/img/page-images/science-fair-header.jpg',
        'activity_scope' => 'Internal',
        'excerpt' => 'Students showcase their scientific knowledge and creativity through innovative projects and experiments at our annual science fair.',
        'extracurricular_type' => 'Academic Clubs'
    ],
    [
        'name' => 'Choir Competition',
        'card_image' => 'assets/img/card-images/choir-competition.jpg',
        'page_image' => 'assets/img/page-images/choir-competition-header.jpg',
        'activity_scope' => 'External',
        'excerpt' => 'Our school choir participates in regional competitions, showcasing vocal talents and musical abilities developed through regular practice.',
        'extracurricular_type' => 'Music & Arts'
    ],
    [
        'name' => 'Debate Club Sessions',
        'card_image' => 'assets/img/card-images/debate-club.jpg',
        'page_image' => 'assets/img/page-images/debate-club-header.jpg',
        'activity_scope' => 'Internal',
        'excerpt' => 'Weekly debate sessions help students develop critical thinking, public speaking skills, and confidence in expressing their viewpoints.',
        'extracurricular_type' => 'Academic Clubs'
    ],
    [
        'name' => 'Traditional Dance Performance',
        'card_image' => 'assets/img/card-images/traditional-dance.jpg',
        'page_image' => 'assets/img/page-images/traditional-dance-header.jpg',
        'activity_scope' => 'External',
        'excerpt' => 'Students learn and perform traditional Zambian dances, preserving cultural heritage while developing coordination and teamwork.',
        'extracurricular_type' => 'Music & Arts'
    ],
    [
        'name' => 'Community Clean-up Day',
        'card_image' => 'assets/img/card-images/community-cleanup.jpg',
        'page_image' => 'assets/img/page-images/community-cleanup-header.jpg',
        'activity_scope' => 'External',
        'excerpt' => 'Students and staff work together to clean up the local community, promoting environmental awareness and community service.',
        'extracurricular_type' => 'Community Service'
    ],
    [
        'name' => 'Netball Tournament',
        'card_image' => 'assets/img/card-images/netball-tournament.jpg',
        'page_image' => 'assets/img/page-images/netball-tournament-header.jpg',
        'activity_scope' => 'External',
        'excerpt' => 'Our netball teams compete against other schools in friendly matches and tournaments throughout the academic year.',
        'extracurricular_type' => 'Sports Teams'
    ],
    [
        'name' => 'Computer Coding Workshop',
        'card_image' => 'assets/img/card-images/coding-workshop.jpg',
        'page_image' => 'assets/img/page-images/coding-workshop-header.jpg',
        'activity_scope' => 'Internal',
        'excerpt' => 'Students learn basic programming concepts and develop simple applications in our technology club workshops.',
        'extracurricular_type' => 'Technology Club'
    ],
    [
        'name' => 'Cultural Exchange Program',
        'card_image' => 'assets/img/card-images/cultural-exchange.jpg',
        'page_image' => 'assets/img/page-images/cultural-exchange-header.jpg',
        'activity_scope' => 'External',
        'excerpt' => 'Our pen pal program connects students with peers from different countries to share cultural experiences and build global awareness.',
        'extracurricular_type' => 'Cultural Exchange'
    ]
];

// Get unique extracurricular types for filtering
$extracurricular_types = array_unique(array_column($extracurricular_activities, 'extracurricular_type'));
$activity_scopes = array_unique(array_column($extracurricular_activities, 'activity_scope'));
?>

<!-- MAIN -->
<main role="main">
    <!-- Content -->
    <article>
        <div class="line">
            <header class="rounded-div section background-primary background-transparent text-center"
                data-image-src="assets/img/parallax-02.jpg">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Extracurricular Activities</h1>
            </header>
        </div>

        <div class="section background-white">
            <div class="line">
                <div class="s-12 m-12 l-12">
                    <p class="text-center margin-bottom-30">Explore the wide range of extracurricular activities offered at Nkhwazi Primary School. These activities complement our academic curriculum and help students develop important life skills, discover new interests, and build lasting friendships.</p>
                </div>

                <!-- Filter Section -->
                <div class="s-12 m-12 l-12 margin-bottom-30">
                    <div class="background-primary-hightlight padding-2x">
                        <h3 class="text-center margin-bottom-20">Filter Activities</h3>
                        
                        <!-- Category Filter -->
                        <div class="s-12 m-12 l-12">
                            <p class="category-filter">By Extracurricular Category:</p>
                            <div class="blog-category-nav text-center">
                                <a href="#" class="category-link active" data-filter="all">All Categories</a>
                                <?php foreach ($extracurricular_types as $type): ?>
                                <a href="#" class="category-link" data-filter="<?php echo htmlspecialchars($type); ?>"><?php echo htmlspecialchars($type); ?></a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Extracurricular Activities Cards -->
                <div class="grid margin activities-container">
                    <?php foreach ($extracurricular_activities as $index => $activity): ?>
                    <div class="s-12 m-4 l-4 margin-m-bottom blog-post gallery-card activity-item" 
                         data-category="<?php echo htmlspecialchars($activity['extracurricular_type']); ?>"
                         data-scope="<?php echo htmlspecialchars($activity['activity_scope']); ?>">
                        <span class="gallery-label extracurricular-label"><?php echo htmlspecialchars($activity['activity_scope']); ?></span>
                        <a href="<?php echo esc_url(get_permalink($activity['id'])); ?>">
                            <img class="rounded-image blog-image" src="<?php echo htmlspecialchars($activity['card_image']); ?>" alt="<?php echo htmlspecialchars($activity['name']); ?>">
                        </a>
                        <div class="gallery-content">
                            <h3 class="text-strong text-uppercase">
                                <a class="text-dark text-orange-hover" href="<?php echo esc_url(get_permalink($activity['id'])); ?>">
                                    <?php echo htmlspecialchars($activity['name']); ?>
                                </a>
                            </h3>
                            <div class="s-12 m-12 l-12">
                                <p class="margin-bottom text-dark"><?php echo htmlspecialchars($activity['excerpt']); ?></p>
                                <p class="text-small"><strong>Category:</strong> 
                                    <a href="page-single-extracurricular.php?slug=<?php echo urlencode(strtolower(str_replace(' ', '-', $activity['extracurricular_type']))); ?>">
                                        <?php echo htmlspecialchars($activity['extracurricular_type']); ?>
                                    </a>
                                </p>
                                <a class="text-more-info text-primary-hover margin-bottom-20" href="<?php echo esc_url(get_permalink($activity['id'])); ?>">Learn More</a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <div class="line text-center pagination-container">
                    <div class="s-12 margin-bottom">
                        <ul class="pagination">
                            <li><a href="#" class="previous-page">Prev</a></li>
                            <li><a href="#" class="active-page">1</a></li>
                            <li><a href="#">2</a></li>
                            <li><a href="#">3</a></li>
                            <li><a href="#" class="next-page">Next</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </article>
</main>

<!-- JavaScript for filtering activities -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Category filter functionality
    const categoryLinks = document.querySelectorAll('.category-link');
    const activityItems = document.querySelectorAll('.activity-item');
    
    let currentCategory = 'all';
    
    // Function to filter activities
    function filterActivities() {
        activityItems.forEach(item => {
            const itemCategory = item.getAttribute('data-category');
            
            if (currentCategory === 'all' || itemCategory === currentCategory) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }
    
    // Category filter event listeners
    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all category links
            categoryLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Update current category
            currentCategory = this.getAttribute('data-filter');
            
            // Filter activities
            filterActivities();
        });
    });
});
</script>

<?php require_once('includes/footer.php'); ?>