<?php 
/**
 * Template Name: Apply Online
 *
 * This template is intended for pages that require a focused, distraction-free layout
 * by removing the sidebar. It provides a narrower content area, which enhances 
 * readability, especially for text-heavy content.
 *
 * Usage: Apply this template to pages where simplicity and ease of reading are priorities.
 */

// Include the nationalities file for dropdown options
require_once('includes/nationalities.php');
// Include the application form functionality
require_once('includes/application-form.php');

get_header();
?>

<!-- MAIN -->
<main role="main">
    <div class="line">
        <header class="rounded-div section background-blue background-transparent text-center"
            data-image-src="assets/img/parallax-02.jpg">
            <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Apply Online
            </h1>
            <!-- Page ID: 380 -->
        </header>
    </div>
    <!-- Content -->
    <article class="s-12 m-12 l-9 center">
        <div class="section background-white">
            <!-- put page content below, do not edit above this comment -->

            <?php 
            while (have_posts()) :
                the_post();
                the_content();
            endwhile;
            ?>

            <!-- Datepicker and form validation are initialized in the header -->

            <!-- School Information -->
            <div class="s-12 m-12 l-12 margin-bottom-20 form-header">
                <h2 class="text-uppercase text-strong">Application Form</h2>

                <p>We are excited that you would like to apply for a place at our School. Please fill out the
                    Form below. We will get in touch at the earliest time possible. Please note:</p>
                <ul>
                    <li>A <span class='any-error'>red</span> left border signifies a required field which must
                        be filled in</li>
                    <li>The placeholder text is for your guidance only, it disappears as you begin typing</li>
                </ul>
            </div>


            <!-- Application Form -->
            <form id="application-form" name="applicationForm" class="customform" method="post"
                enctype="multipart/form-data">
                <input type="hidden" name="subject" value="New Online Application Form Submission">

                <!-- Form Messages -->
                <div class="form-message"></div>

                <!-- Loading Indicator -->
                <div class="form-loading">
                    <div class="spinner"></div>
                    <p>Submitting your application...</p>
                </div>

                <!-- Form Progress Indicator -->
                <div class="form-progress s-12 m-12 l-12">
                    <div class="form-progress-step">
                        <div class="form-progress-step-number">1</div>
                        <div class="form-progress-step-label">Pupil Details</div>
                    </div>
                    <div class="form-progress-step">
                        <div class="form-progress-step-number">2</div>
                        <div class="form-progress-step-label">Father's Details</div>
                    </div>
                    <div class="form-progress-step">
                        <div class="form-progress-step-number">3</div>
                        <div class="form-progress-step-label">Mother's Details</div>
                    </div>
                    <div class="form-progress-step">
                        <div class="form-progress-step-number">4</div>
                        <div class="form-progress-step-label">Additional Information</div>
                    </div>
                </div>

                <!-- Step 1: Pupil's Particulars -->
                <div class="form-step">
                    <h3 class="text-strong margin-top-20 form-section-title">Part 1: Pupil's Particulars</h3>

                    <div class="grid margin">
                        <div class="s-12 m-5 l-5">
                            <label><span class='margin-bottom-10'>Last Name</span></label>
                            <input name="lname" class="required" placeholder="Last Name" title="Pupil's Last Name"
                                type="text" required data-min-length="2" data-max-length="30" />
                        </div>
                        <div class="s-12 m-5 l-5">
                            <label><span class='margin-bottom-10'>First Name</span></label>
                            <input name="fname" class="required" placeholder="First Name" title="Pupil's First Name"
                                type="text" required data-min-length="2" data-max-length="30" />
                        </div>
                        <div class="s-12 m-2 l-2">
                            <label><span class='margin-bottom-10'>Sex</span></label>
                            <select name="sex" class="required" required>
                                <option value="">Select</option>
                                <option value="M">Male</option>
                                <option value="F">Female</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-6 l-4">
                            <label><span class='margin-bottom-10'>Date of Birth</span></label>
                            <input id="datepicker" name="dob" class="required" placeholder="Date of Birth"
                                title="Pupil's Date of Birth" type="text" required />
                        </div>
                        <div class="s-12 m-6 l-4">
                            <label><span class='margin-bottom-10'>Nationality</span></label>
                            <select name="nationality" class="required" required>
                                <?php echo get_nationality_options(); ?>
                            </select>
                        </div>
                        <div class="s-12 m-8 l-4">
                            <label><span class='margin-bottom-10'>Grade Applied For</span></label>
                            <select name="grade" class="required border-radius" required>
                                <option value="">Select Grade</option>
                                <option value="Reception">Reception</option>
                                <option value="Grade 1">Grade 1</option>
                                <option value="Grade 2">Grade 2</option>
                                <option value="Grade 3">Grade 3</option>
                                <option value="Grade 4">Grade 4</option>
                                <option value="Grade 5">Grade 5</option>
                                <option value="Grade 6">Grade 6</option>
                                <option value="Grade 7">Grade 7</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-6 l-6">
                            <label><span class='margin-bottom-10'>Previous School</span></label>
                            <input name="previous_school" class="required" placeholder="Previous School"
                                title="Previous School" type="text" required data-min-length="5" data-max-length="60" />
                        </div>

                        <div class="s-12 m-6 l-6">
                            <label><span class='margin-bottom-10'>Does the child have any handicap or special
                                    needs?</span></label>
                            <textarea name="handicap" class="" placeholder="If yes, please specify. If no, write 'None'"
                                rows="3" data-min-length="0" data-max-length="200"></textarea>
                        </div>
                    </div>

                    <!-- Step 1 Navigation -->
                    <div class="form-navigation">
                        <div></div> <!-- Empty div for flex alignment -->
                        <div class="s-12 m-6 l-4 center">
                            <button type="button"
                                class="next-step button rounded-full-btn text-white background-blue">Next:
                                Father's Details</button>
                        </div>

                    </div>
                </div><!-- End of Step 1 -->

                <!-- Step 2: Family Information - Father's Details -->
                <div class="form-step">
                    <h3 class="application-form margin-bottom-10 margin-top form-section-title">Part 2: Father or
                        Guardian's Details</h3>

                    <div class="grid margin">
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Last Name</span></label>
                            <input name="lname_f" class="required" placeholder="Father's Last Name"
                                title="Father's Last Name" type="text" required data-min-length="2"
                                data-max-length="30" />
                        </div>
                        <div class="s-12 m-6 l-6">
                            <label><span class='margin-bottom-10'>First Name</span></label>
                            <input name="fname_f" class="required" placeholder="Father's First Name"
                                title="Father's First Name" type="text" required data-min-length="2"
                                data-max-length="30" />
                        </div>
                        <div class="s-12 m-8 l-3">
                            <label><span class='margin-bottom-10'>Nationality</span></label>
                            <select name="nationality_f" class="required border-radius" required>
                                <?php echo get_nationality_options(); ?>
                            </select>
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>ID/Passport Number</span></label>
                            <input name="identity_f" class="required" placeholder="ID/Passport Number"
                                title="Father's ID/Passport Number" type="text" required data-min-length="4"
                                data-max-length="30" />
                        </div>
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>Occupation</span></label>
                            <input name="occupation_f" class="required" placeholder="Occupation"
                                title="Father's Occupation" type="text" required data-min-length="2"
                                data-max-length="30" />
                        </div>
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>Employer</span></label>
                            <input name="employer_f" class="required" placeholder="Employer" title="Father's Employer"
                                type="text" required data-min-length="3" data-max-length="30" />
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-12 l-12">
                            <label><span class='margin-bottom-10'>Physical Address</span></label>
                            <input name="address_f" class="required" placeholder="Physical Address"
                                title="Father's Physical Address" type="text" required data-min-length="5"
                                data-max-length="60" />
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-7 l-8">
                            <label><span class='margin-bottom-10'>Postal Address</span></label>
                            <input name="address_postal_f" class="" placeholder="Postal Address"
                                title="Father's Postal Address" type="text" data-min-length="0" data-max-length="60" />
                        </div>
                        <div class="s-12 m-5 l-4">
                            <label><span class='margin-bottom-10'>Email Address</span></label>
                            <input name="email_f" class="required email" placeholder="Email Address"
                                title="Father's Email Address" type="email" required data-validate-email="true" />
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>Phone Number</span></label>
                            <input name="phone_f" class="required" placeholder="Phone Number"
                                title="Father's Phone Number" type="text" required data-min-length="4"
                                data-max-length="20" />
                        </div>
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>Work Phone</span></label>
                            <input name="phone_work_f" class="" placeholder="Work Phone" title="Father's Work Phone"
                                type="text" data-min-length="0" data-max-length="20" />
                        </div>
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>Residence Status</span></label>
                            <select name="res_status_f" class="required border-radius" required>
                                <option value="">Select Status</option>
                                <option value="Permanent">Permanent</option>
                                <option value="Temporary">Temporary</option>
                            </select>
                        </div>
                    </div>

                    <!-- Step 2 Navigation -->
                    <div class="form-navigation">
                        <button type="button"
                            class="prev-step button rounded-btn text-dark background-white border-dark">Previous:
                            Pupil's Details</button>
            
         <button type="button" class="next-step button rounded-full-btn text-white background-blue">Next:
                            Mother's Details</button>
               
               
                    </div>
                </div><!-- End of Step 2 -->

                <!-- Step 3: Family Information - Mother's Details -->
                <div class="form-step">
                    <h3 class="application-form margin-bottom-10 margin-top form-section-title">Part 3: Mother's Details
                    </h3>

                    <div class="grid margin">
                        <div class="s-12 m-6 l-3">
                            <label><span class='margin-bottom-10'>Last Name</span></label>
                            <input name="lname_m" class="required" placeholder="Mother's Last Name"
                                title="Mother's Last Name" type="text" required data-min-length="2"
                                data-max-length="30" />
                        </div>
                        <div class="s-12 m-6 l-6">
                            <label><span class='margin-bottom-10'>First Name</span></label>
                            <input name="fname_m" class="required" placeholder="Mother's First Name"
                                title="Mother's First Name" type="text" required data-min-length="2"
                                data-max-length="30" />
                        </div>
                        <div class="s-12 m-8 l-3">
                            <label><span class='margin-bottom-10'>Nationality</span></label>
                            <select name="nationality_m" class="required border-radius" required>
                                <?php echo get_nationality_options(); ?>
                            </select>
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>ID/Passport Number</span></label>
                            <input name="identity_m" class="required" placeholder="ID/Passport Number"
                                title="Mother's ID/Passport Number" type="text" required data-min-length="4"
                                data-max-length="30" />
                        </div>
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>Occupation</span></label>
                            <input name="occupation_m" class="required" placeholder="Occupation"
                                title="Mother's Occupation" type="text" required data-min-length="2"
                                data-max-length="30" />
                        </div>
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>Employer</span></label>
                            <input name="employer_m" class="required" placeholder="Employer" title="Mother's Employer"
                                type="text" required data-min-length="3" data-max-length="30" />
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-12 l-12">
                            <label><span class='margin-bottom-10'>Physical Address</span></label>
                            <input name="address_m" class="required" placeholder="Physical Address"
                                title="Mother's Physical Address" type="text" required data-min-length="5"
                                data-max-length="60" />
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-7 l-8">
                            <label><span class='margin-bottom-10'>Postal Address</span></label>
                            <input name="address_postal_m" class="" placeholder="Postal Address"
                                title="Mother's Postal Address" type="text" data-min-length="0" data-max-length="60" />
                        </div>
                        <div class="s-12 m-5 l-4">
                            <label><span class='margin-bottom-10'>Email Address</span></label>
                            <input name="email_m" class="required email" placeholder="Email Address"
                                title="Mother's Email Address" type="email" required data-validate-email="true" />
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>Phone Number</span></label>
                            <input name="phone_m" class="required" placeholder="Phone Number"
                                title="Mother's Phone Number" type="text" required data-min-length="4"
                                data-max-length="20" />
                        </div>
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>Work Phone</span></label>
                            <input name="phone_work_m" class="" placeholder="Work Phone" title="Mother's Work Phone"
                                type="text" data-min-length="0" data-max-length="20" />
                        </div>
                        <div class="s-12 m-4 l-4">
                            <label><span class='margin-bottom-10'>Residence Status</span></label>
                            <select name="res_status_m" class="required border-radius" required>
                                <option value="">Select Status</option>
                                <option value="Permanent">Permanent</option>
                                <option value="Temporary">Temporary</option>
                            </select>
                        </div>
                    </div>



                    <!-- Step 3 Navigation -->
                    <div class="form-navigation">
                        <button type="button"
                            class="prev-step button rounded-btn text-dark background-white border-dark">Previous:
                            Father's Details</button>
                        <button type="button" class="next-step button rounded-full-btn text-white background-blue">Next:
                            Additional Information</button>
                    </div>
                </div><!-- End of Step 3 -->

                <!-- Step 4: Additional Information -->
                <div class="form-step">
                    <h3 class="text-strong margin-top-20 form-section-title">Part 4: Additional Information</h3>

                    <div class="grid margin">
                        <div class="s-12 m-12 l-6">
                            <label><span class='margin-bottom-10'>Who will be responsible for paying
                                    fees?</span></label>
                            <select name="fees_payer" class="required border-radius" required>
                                <option value="">Select Fee Payer</option>
                                <option value="Father">Father</option>
                                <option value="Mother">Mother</option>
                                <option value="Both Parents">Both Parents</option>
                                <option value="Guardian">Guardian</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-7 l-7">
                            <label><span class='margin-bottom-10'>Does the applicant have any siblings at the
                                    school?</span></label>
                            <textarea name="siblings" class=""
                                placeholder="If yes, please provide their names and grades. If no, write 'None'"
                                rows="3" data-min-length="0" data-max-length="100"></textarea>
                        </div>
                        <div class="s-12 m-5 l-5">
                            <label><span class='margin-bottom-10'>Would you like to be placed on the waiting list if
                                    there is no space available?</span></label>
                            <select name="waiting_list" class="required border-radius" required>
                                <option value="">Select Option</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                            </select>
                        </div>
                        <div class="s-12 m-12 l-12">
                            <label><span class='margin-bottom-10'>Any additional information you would like to
                                    provide?</span></label>
                            <textarea name="more_info" class="" placeholder="Additional information (optional)" rows="3"
                                data-min-length="0" data-max-length="400"></textarea>
                        </div>
                    </div>

                    <div class="grid margin">
                        <div class="s-12 m-12 l-12">
                            <h4 class="text-strong">Declaration</h4>
                            <p>By submitting this application, I declare that:</p>
                            <ul>
                                <li>The information provided is true and correct to the best of my knowledge.</li>
                                <li>I agree to abide by the school's policies and procedures.</li>
                                <li>I am responsible for paying all prescribed fees and deposits if my child is
                                    enrolled.</li>
                                <li>My child shall be subject to the rules and disciplines of the school.</li>
                                <li>If any details of this application are found false, my child will immediately lose
                                    his/her place at the school.</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Step 4 Navigation -->
                    <div class="form-navigation">
                        <button type="button"
                            class="prev-step button rounded-btn text-dark background-white border-dark">Previous:
                            Mother's Details</button>
                        <div></div>
                    </div>

                    <!-- reCAPTCHA -->
                    <div class="grid margin">
                        <div class="s-12 m-12 l-12 text-center">
                            <div class="g-recaptcha-container">
                                <div class="g-recaptcha" data-sitekey="6Lf79rcaAAAAALdqWY74mw_n6DtTxR_C5AEL6cfL"></div>
                                <div class="recaptcha-error-message"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="grid margin">
                        <div class="s-12 m-12 l-12">
                            <button class="s-12 m-5 l-4 center button submit-btn rounded-full-btn text-white background-blue"
                                type="submit">Submit Application</button>
                        </div>
                    </div>
                </div><!-- End of Step 4 -->

            </form>
        </div>

        <!-- put page content above, do not edit below this comment -->
        </div>
    </article>
</main>
<?php get_footer(); ?>