/**
 * Contact Form JavaScript
 * 
 * Handles form validation, AJAX submission, and UI interactions
 */
(function($) {
    'use strict';

    // Add CSS keyframes for spinner animation
    const spinnerStyle = `
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        @keyframes ellipsis {
            0% { content: '.'; }
            33% { content: '..'; }
            66% { content: '...'; }
        }
    `;
    $('<style>').text(spinnerStyle).appendTo('head');

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Document ready - initializing contact form');
        // Initialize form
        initContactForm();
    });
    
    // Fallback for jQuery ready event
    $(window).on('load', function() {
        console.log('Window loaded - checking if form is initialized');
        if ($('.contact-form').length && !$('.contact-form').data('initialized')) {
            console.log('Form not initialized yet - initializing now');
            initContactForm();
        }
    });

    /**
     * Initialize the contact form
     */
    function initContactForm() {
        const $form = $('.contact-form');
        
        if (!$form.length) {
            console.log('Form not found in the DOM');
            return;
        }
        
        // Check if already initialized
        if ($form.data('initialized')) {
            console.log('Form already initialized');
            return;
        }
        
        console.log('Initializing contact form');
        
        // Make sure the form has position relative for proper loading indicator positioning
        $form.css('position', 'relative');
        
        // Mark as initialized
        $form.data('initialized', true);
        
        // Set validation rules for fields
        setupValidationRules($form);
        
        // Initialize form validation
        initFormValidation($form);
        
        // Make sure the form message container is properly styled
        const $messageContainer = $form.find('.form-message');
        $messageContainer.css({
            'padding': '15px',
            'margin-bottom': '20px',
            'border-radius': '4px',
            'display': 'none',
            'text-align': 'center',
            'font-weight': 'bold'
        });
        
        // Make sure the loading indicator is properly styled
        const $loadingIndicator = $form.find('.form-loading');
        $loadingIndicator.css({
            'position': 'absolute',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background-color': 'rgba(255, 255, 255, 0.8)',
            'z-index': '100',
            'display': 'none',
            'flex-direction': 'column',
            'justify-content': 'center',
            'align-items': 'center'
        });
        
        // Style the spinner
        $loadingIndicator.find('.spinner').css({
            'display': 'inline-block',
            'width': '50px',
            'height': '50px',
            'border': '5px solid #f3f3f3',
            'border-top': '5px solid #3498db',
            'border-radius': '50%',
            'animation': 'spin 1s linear infinite'
        });
        
        // Handle form submission
        $form.on('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted');
            
            // Validate all fields before submission
            if (!validateAllFields($form)) {
                showFormMessage($form, 'Please fill in all required fields correctly.', 'error');
                return;
            }
            
            // Validate reCAPTCHA
            if (!validateRecaptcha($form)) {
                showFormMessage($form, 'Please complete the reCAPTCHA verification.', 'error');
                return;
            }
            
            // Submit form via AJAX
            submitFormAjax($form);
        });
    }
    
    /**
     * Setup validation rules for form fields
     */
    function setupValidationRules($form) {
        // Name fields: 2-60 characters
        $form.find('input[name="name"]').attr({
            'data-min-length': 2,
            'data-max-length': 60,
            'data-error-message': 'Name must be between 2 and 30 characters'
        });
        
        // Email field: valid email
        $form.find('input[name="email"]').attr({
            'data-validate-email': true,
            'data-error-message': 'Please enter a valid email address'
        });
        
        // Phone field: 6-20 characters (optional)
        $form.find('input[name="phone"]').attr({
            'data-min-length': 6,
            'data-max-length': 20,
            'data-error-message': 'Phone number must be between 6 and 20 characters'
        });
        
        // Subject field: 4-100 characters
        $form.find('input[name="subject"]').attr({
            'data-min-length': 4,
            'data-max-length': 100,
            'data-error-message': 'Subject must be between 4 and 50 characters'
        });
        
        // Message field: 20-2000 characters
        $form.find('textarea[name="message"]').attr({
            'data-min-length': 20,
            'data-max-length': 2000,
            'data-error-message': 'Message must be between 20 and 500 characters'
        });
    }
    
    /**
     * Initialize form validation
     */
    function initFormValidation($form) {
        // Initialize character counters for fields with min/max length
        initCharacterCounters($form);
        
        // Validate required fields on blur
        $form.find('.required').on('blur', function() {
            validateField($(this));
        });
        
        // Validate email fields on blur
        $form.find('.email').on('blur', function() {
            validateEmail($(this));
        });
        
        // Validate fields on change for select elements
        $form.find('select.required').on('change', function() {
            validateField($(this));
        });
        
        // Live validation as user types (after first blur)
        $form.find('input.required, textarea.required').on('input', function() {
            validateField($(this));
            updateCharacterCount($(this));
        });
        
        // Live validation for email fields
        $form.find('input.email').on('input', function() {
            validateEmail($(this));
        });
        
        // Live validation for all fields with min/max length
        $form.find('[data-min-length], [data-max-length]').on('input', function() {
            updateCharacterCount($(this));
            validateField($(this));
        });
        
        // Initialize character counters on page load
        $form.find('[data-min-length], [data-max-length]').each(function() {
            updateCharacterCount($(this));
        });
    }
    
    /**
     * Initialize character counters for fields with min/max length
     */
    function initCharacterCounters($form) {
        $form.find('[data-min-length], [data-max-length]').each(function() {
            const $field = $(this);
            
            // Create a container for the field and counter if it doesn't exist
            if (!$field.parent().hasClass('field-with-counter')) {
                // Get the field's current container
                const $fieldContainer = $field.parent();
                
                // Create a wrapper for the field
                $field.wrap('<div class="field-with-counter"></div>');
                
                // Add character counter after the field inside the wrapper
                $field.after('<div class="character-counter"></div>');
                updateCharacterCount($field);
            }
        });
    }
    
    /**
     * Update character count display
     */
    function updateCharacterCount($field) {
        const minLength = parseInt($field.attr('data-min-length'), 10) || 0;
        const maxLength = parseInt($field.attr('data-max-length'), 10) || 0;
        
        if (!minLength && !maxLength) {
            return;
        }
        
        const currentLength = $field.val().length;
        
        // Find the counter within the field's wrapper
        let $counter;
        if ($field.parent().hasClass('field-with-counter')) {
            $counter = $field.parent().find('.character-counter');
        } else {
            // For backward compatibility
            $counter = $field.next('.character-counter');
        }
        
        // Create counter if it doesn't exist
        if (!$counter.length) {
            if ($field.parent().hasClass('field-with-counter')) {
                $field.after('<div class="character-counter"></div>');
                $counter = $field.parent().find('.character-counter');
            } else {
                $field.after('<div class="character-counter"></div>');
                $counter = $field.next('.character-counter');
            }
        }
        
        let counterText = '';
        let counterClass = '';
        
        // Different messages based on validation state
        if (currentLength === 0 && !$field.hasClass('required')) {
            // Optional field that's empty
            counterText = 'Optional';
        } else if (minLength > 0 && currentLength < minLength && (currentLength > 0 || $field.hasClass('required'))) {
            // Below minimum length (only show warning if field has content or is required)
            counterText = currentLength + '/' + minLength + ' min';
            counterClass = 'warning';
        } else if (maxLength && currentLength > maxLength) {
            // Exceeds maximum length
            counterText = currentLength + '/' + maxLength + ' (too long)';
            counterClass = 'error';
        } else if (maxLength && currentLength >= maxLength * 0.9) {
            // Approaching maximum length
            const remaining = maxLength - currentLength;
            counterText = remaining + ' character' + (remaining !== 1 ? 's' : '') + ' left';
            counterClass = 'warning';
        } else if (maxLength) {
            // Within limits with maximum
            counterText = currentLength + '/' + maxLength;
        } else if (minLength > 0 && currentLength >= minLength) {
            // Above minimum length with no maximum
            counterText = currentLength + ' characters';
        }
        
        $counter.text(counterText).removeClass('warning error').addClass(counterClass);
    }
    
    /**
     * Validate a single field
     */
    function validateField($field) {
        // Get the container (either field-with-counter or parent)
        const $container = $field.parent().hasClass('field-with-counter') ? $field.parent() : $field.parent();
        
        // Remove existing error message
        $container.find('.field-error-message').remove();
        
        // Check if field is empty
        if ($field.val() === '' && $field.hasClass('required')) {
            $field.removeClass('valid').addClass('error');
            
            // Add error message in the appropriate place
            if ($container.hasClass('field-with-counter')) {
                // If using the new structure, add after the counter
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">This field is required</span>');
                } else {
                    $field.after('<span class="field-error-message">This field is required</span>');
                }
            } else {
                // Legacy support
                $field.after('<span class="field-error-message">This field is required</span>');
            }
            return false;
        }
        
        // Skip validation if field is empty and not required
        if ($field.val() === '' && !$field.hasClass('required')) {
            $field.removeClass('error valid');
            return true;
        }
        
        // Check min and max length if specified
        const minLength = parseInt($field.attr('data-min-length'), 10) || 0;
        const maxLength = parseInt($field.attr('data-max-length'), 10) || 0;
        const fieldValue = $field.val();
        const errorMessage = $field.attr('data-error-message') || 'Please check this field';
        
        if (minLength > 0 && fieldValue.length < minLength) {
            $field.removeClass('valid').addClass('error');
            
            // Add error message in the appropriate place
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">' + errorMessage + '</span>');
                } else {
                    $field.after('<span class="field-error-message">' + errorMessage + '</span>');
                }
            } else {
                $field.after('<span class="field-error-message">' + errorMessage + '</span>');
            }
            return false;
        }
        
        if (maxLength > 0 && fieldValue.length > maxLength) {
            $field.removeClass('valid').addClass('error');
            
            // Add error message in the appropriate place
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">' + errorMessage + '</span>');
                } else {
                    $field.after('<span class="field-error-message">' + errorMessage + '</span>');
                }
            } else {
                $field.after('<span class="field-error-message">' + errorMessage + '</span>');
            }
            return false;
        }
        
        // Field is valid
        $field.removeClass('error').addClass('valid');
        return true;
    }
    
    /**
     * Validate email field
     */
    function validateEmail($field) {
        // Get the container (either field-with-counter or parent)
        const $container = $field.parent().hasClass('field-with-counter') ? $field.parent() : $field.parent();
        
        // Remove existing error message
        $container.find('.field-error-message').remove();
        
        // Check if field is empty and required
        if ($field.val() === '' && $field.hasClass('required')) {
            $field.removeClass('valid').addClass('error');
            
            // Add error message in the appropriate place
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">Email is required</span>');
                } else {
                    $field.after('<span class="field-error-message">Email is required</span>');
                }
            } else {
                $field.after('<span class="field-error-message">Email is required</span>');
            }
            return false;
        }
        
        // Skip validation if field is empty and not required
        if ($field.val() === '' && !$field.hasClass('required')) {
            $field.removeClass('error valid');
            return true;
        }
        
        // Check if email is valid
        if ($field.val() !== '' && !isValidEmail($field.val())) {
            $field.removeClass('valid').addClass('error');
            
            // Add error message in the appropriate place
            if ($container.hasClass('field-with-counter')) {
                const $counter = $container.find('.character-counter');
                if ($counter.length) {
                    $counter.after('<span class="field-error-message">Please enter a valid email address</span>');
                } else {
                    $field.after('<span class="field-error-message">Please enter a valid email address</span>');
                }
            } else {
                $field.after('<span class="field-error-message">Please enter a valid email address</span>');
            }
            return false;
        }
        
        // Email is valid
        $field.removeClass('error').addClass('valid');
        return true;
    }
    
    /**
     * Check if email is valid
     */
    function isValidEmail(email) {
        const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
        return emailRegex.test(email);
    }
    
    /**
     * Validate all fields in the form
     */
    function validateAllFields($form) {
        let isValid = true;
        
        // Validate all required fields
        $form.find('.required').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });
        
        // Validate all email fields
        $form.find('.email').each(function() {
            if (!validateEmail($(this))) {
                isValid = false;
            }
        });
        
        // Validate all fields with min/max length
        $form.find('[data-min-length], [data-max-length]').each(function() {
            // Skip if it's not required and empty
            if (!$(this).hasClass('required') && $(this).val() === '') {
                return;
            }
            
            if (!validateField($(this))) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    /**
     * Validate reCAPTCHA
     */
    function validateRecaptcha($form) {
        const recaptchaResponse = grecaptcha.getResponse();
        
        if (recaptchaResponse.length === 0) {
            // Show error message
            const $recaptchaContainer = $form.find('.g-recaptcha-container');
            $recaptchaContainer.find('.recaptcha-error-message').remove();
            $recaptchaContainer.append('<div class="recaptcha-error-message">Please complete the reCAPTCHA verification</div>');
            return false;
        }
        
        // Remove error message if exists
        $form.find('.recaptcha-error-message').remove();
        return true;
    }
    
    /**
     * Submit form via AJAX
     */
    function submitFormAjax($form) {
        console.log('Submitting form via AJAX');
        
        // Show loading indicator
        const $loadingIndicator = $form.find('.form-loading');
        const $submitButton = $form.find('button[type="submit"]');
        
        // Make sure the form has position relative for proper loading indicator positioning
        $form.css('position', 'relative');
        
        // Position the loading indicator properly
        $loadingIndicator.css({
            'position': 'absolute',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background-color': 'rgba(255, 255, 255, 0.8)',
            'z-index': '100',
            'display': 'flex',
            'flex-direction': 'column',
            'justify-content': 'center',
            'align-items': 'center'
        });
        
        // Make sure the spinner is visible
        $loadingIndicator.find('.spinner').css({
            'display': 'inline-block',
            'width': '50px',
            'height': '50px',
            'border': '5px solid #f3f3f3',
            'border-top': '5px solid #3498db',
            'border-radius': '50%',
            'animation': 'spin 1s linear infinite'
        });
        
        // Show loading indicator and update button text
        $loadingIndicator.show();
        $form.find('.form-message').hide();
        $submitButton.prop('disabled', true);
        $submitButton.addClass('submitting');
        $submitButton.html('<span class="spinner-text">Sending...</span>');
        
        console.log('Loading indicator shown:', $loadingIndicator.is(':visible'));
        console.log('Button disabled:', $submitButton.prop('disabled'));
        
        // Collect form data
        const formData = new FormData($form[0]);
        
        // Add action and nonce
        formData.append('action', 'nkhwazi_submit_contact_form');
        formData.append('nonce', nkhwazi_contact_form.nonce);
        
        // Add reCAPTCHA response
        const recaptchaResponse = grecaptcha.getResponse();
        formData.append('g-recaptcha-response', recaptchaResponse);
        
        // Convert FormData to object for logging
        const formDataObj = {};
        formData.forEach((value, key) => {
            formDataObj[key] = value;
        });
        
        // Log submission details
        console.log('Submitting form with data:', formDataObj);
        console.log('AJAX URL:', nkhwazi_contact_form.ajax_url);
        
        $.ajax({
            url: nkhwazi_contact_form.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('AJAX Success Response:', response);
                
                // Hide loading indicator and restore button
                $loadingIndicator.hide();
                $submitButton.removeClass('submitting');
                $submitButton.html('Send your Message');
                $submitButton.prop('disabled', false);
                
                if (response.success) {
                    // Show success message
                    showFormMessage($form, response.data.message, 'success');
                    
                    // Reset form
                    $form[0].reset();
                    $form.find('.valid, .error').removeClass('valid error');
                    $form.find('.field-error-message').remove();
                    
                    // Reset reCAPTCHA
                    grecaptcha.reset();
                    
                    // Reset character counters
                    $form.find('.character-counter').each(function() {
                        $(this).text('');
                    });
                    
                    // Scroll to top of form
                    $('html, body').animate({
                        scrollTop: $form.offset().top - 50
                    }, 300);
                } else {
                    // Show error message
                    showFormMessage($form, response.data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', status, error);
                console.log('Response Text:', xhr.responseText);
                
                // Hide loading indicator and restore button
                $loadingIndicator.hide();
                $submitButton.removeClass('submitting');
                $submitButton.html('Send your Message');
                $submitButton.prop('disabled', false);
                
                // Show error message
                showFormMessage($form, 'An error occurred while submitting the form. Please try again.', 'error');
            }
        });
    }
    
    /**
     * Show form message
     */
    function showFormMessage($form, message, type) {
        console.log('Showing form message:', message, 'Type:', type);
        
        const $messageContainer = $form.find('.form-message');
        
        // Clear any existing classes and add the appropriate one
        $messageContainer.removeClass('success error').addClass(type);
        
        // Set the message and ensure it's visible with proper styling
        $messageContainer.html(message).css({
            'display': 'block',
            'padding': '15px',
            'margin-bottom': '20px',
            'border-radius': '4px',
            'text-align': 'center',
            'font-weight': 'bold',
            'opacity': '1',
            'visibility': 'visible'
        });
        
        // Add specific styling based on message type
        if (type === 'success') {
            $messageContainer.css({
                'background-color': '#d4edda',
                'color': '#155724',
                'border': '1px solid #c3e6cb'
            });
        } else if (type === 'error') {
            $messageContainer.css({
                'background-color': '#f8d7da',
                'color': '#721c24',
                'border': '1px solid #f5c6cb'
            });
        }
        
        // Force the message to be visible
        $messageContainer.attr('style', $messageContainer.attr('style') + '; display: block !important;');
        
        console.log('Message container visible:', $messageContainer.is(':visible'));
        console.log('Message container display:', $messageContainer.css('display'));
        
        // Scroll to message
        $('html, body').animate({
            scrollTop: $messageContainer.offset().top - 100
        }, 300);
        
        // If it's a success message, keep it visible for at least 5 seconds
        if (type === 'success') {
            setTimeout(function() {
                // Fade out slowly after 5 seconds
                $messageContainer.fadeOut(1000);
            }, 5000);
        }
    }
    
})(jQuery);