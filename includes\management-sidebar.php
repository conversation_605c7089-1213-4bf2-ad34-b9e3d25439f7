<?php
// Get management types to display in sidebar
$sidebar_management_types = get_posts(array(
    'post_type' => 'management_type',
    'posts_per_page' => -1,
    'orderby' => 'meta_value_num',
    'meta_key' => 'priority',
    'order' => 'ASC',
));

// Define CSS classes for different categories (will cycle through these)
$css_classes = array(
    'facebook',
    'twitter',
    'google',
    'linkedin',
    'instagram',
    'pinterest'
);
?>
<div class="s-12 m-4 l-2">
  <aside>
    
    <div class="line">
      <h5 class="text-center text-uppercase margin-top-30 margin-bottom-20 text-strong">Categories</h5>
      <div class="margin">

        <?php 
        // Loop through each management type
        $class_index = 0;
        foreach ($sidebar_management_types as $type) :
            $type_name = get_field('management_type_name', $type->ID);
            $css_class = $css_classes[$class_index % count($css_classes)];
            $class_index++;
            
            // Get the management page ID (using the provided ID 378)
            $management_page_id = 378;
            $management_page_url = get_permalink($management_page_id);
            
            // Create URL with type parameter
            $type_slug = sanitize_title($type_name);
            $type_url = add_query_arg('type', $type_slug, $management_page_url);
        ?>
        <div class="s-12 m-12 l-12">
          <a href="<?php echo esc_url($type_url); ?>" class="rounded-div category-padding <?php echo esc_attr($css_class); ?> text-social margin-bottom">
            <?php echo esc_html($type_name); ?>
          </a>
        </div>
        <?php endforeach; ?>
        
        <!-- Teaching Staff link (always appears last) -->
        <div class="s-12 m-12 l-12">
          <a href="<?php echo esc_url(get_permalink(376)); ?>" class="rounded-div rss category-padding text-social margin-bottom">
            Teaching Staff
          </a>
        </div>
  
      </div>
    </div>

  </aside>
</div> <!-- sidebar ends -->