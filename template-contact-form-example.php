<?php
/**
 * Template Name: Contact Page
 *
 * @package YaoZambia
 */
get_header();
?>
<main class="bg-white py-12">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs using Flexy Breadcrumb shortcode -->
        <div class="mb-6 text-sm text-gray-600 max-w-7xl mx-auto">
            <?php echo do_shortcode('[flexy_breadcrumb]'); ?>
        </div>

        <h1 class="text-3xl md:text-4xl font-andada-pro font-bold mb-10 text-center text-yao-primary-700">Contact Us
        </h1>

        <div class="flex flex-col lg:flex-row gap-10 max-w-7xl mx-auto">
            <!-- Contact Information - Smaller section (now on left) -->
            <div class="lg:w-2/5 bg-gray-50 p-6 md:p-10 rounded-lg shadow-md">
                <h2 class="text-2xl font-andada-pro font-bold mb-6 text-yao-primary-700">Head Office - Mongu</h2>

                <?php
                // Get the head office address (identifier = 1)
                $address = yaozambia_get_address(1);
                if ($address) {
                    ?>
                <div class="space-y-6">
                    <!-- Address -->
                    <div>
                        <h3 class="text-lg font-semibold mb-2 flex items-center gap-2 text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-5 h-5 text-yao-primary-600">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21" />
                            </svg>
                            Head Office
                        </h3>
                        <address class="not-italic text-gray-600 ml-7">
                            <p class="font-medium">Youth Activists Organisation</p>
                            <?php if (!empty($address['address_line_1'])) : ?>
                            <p><?php echo esc_html($address['address_line_1']); ?></p>
                            <?php endif; ?>

                            <?php if (!empty($address['address_line_2'])) : ?>
                            <p><?php echo esc_html($address['address_line_2']); ?></p>
                            <?php endif; ?>

                            <?php if (!empty($address['city'])) : ?>
                            <p><?php echo esc_html($address['city']); ?></p>
                            <?php endif; ?>
                        </address>
                    </div>

                    <!-- Phone -->
                    <?php if (!empty($address['phone1']) || !empty($address['phone2'])) : ?>
                    <div>
                        <h3 class="text-lg font-semibold mb-2 flex items-center gap-2 text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-5 h-5 text-yao-primary-600">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                            </svg>
                            Phone
                        </h3>
                        <div class="ml-7 space-y-1">
                            <?php if (!empty($address['phone1'])) : ?>
                            <p class="text-gray-600">
                                <a href="tel:<?php echo esc_attr(preg_replace('/\s+/', '', $address['phone1'])); ?>"
                                    class="hover:text-yao-primary-600 transition-colors">
                                    <?php echo esc_html($address['phone1']); ?>
                                </a>
                            </p>
                            <?php endif; ?>

                            <?php if (!empty($address['phone2'])) : ?>
                            <p class="text-gray-600">
                                <a href="tel:<?php echo esc_attr(preg_replace('/\s+/', '', $address['phone2'])); ?>"
                                    class="hover:text-yao-primary-600 transition-colors">
                                    <?php echo esc_html($address['phone2']); ?>
                                </a>
                            </p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Email -->
                    <?php if (!empty($address['email'])) : ?>
                    <div>
                        <h3 class="text-lg font-semibold mb-2 flex items-center gap-2 text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-5 h-5 text-yao-primary-600">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                            </svg>
                            Email
                        </h3>
                        <div class="ml-7">
                            <p class="text-gray-600">
                                <a href="mailto:<?php echo esc_attr($address['email']); ?>"
                                    class="hover:text-yao-primary-600 transition-colors">
                                    <?php echo esc_html($address['email']); ?>
                                </a>
                            </p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Social Media -->
                    <div>
                        <h3 class="text-lg font-semibold mb-2 flex items-center gap-2 text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-5 h-5 text-yao-primary-600">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M20.893 13.393l-1.135-1.135a2.252 2.252 0 01-.421-.585l-1.08-2.16a.414.414 0 00-.663-.107.827.827 0 01-.812.21l-1.273-.363a.89.89 0 00-.738 1.595l.587.39c.59.395.674 1.23.172 1.732l-.2.2c-.212.212-.33.498-.33.796v.41c0 .409-.11.809-.32 1.158l-1.315 2.191a2.11 2.11 0 01-1.81 1.025 1.055 1.055 0 01-1.055-1.055v-1.172c0-.92-.56-1.747-1.414-2.089l-.655-.261a2.25 2.25 0 01-1.383-2.46l.007-.042a2.25 2.25 0 01.29-.787l.09-.15a2.25 2.25 0 012.37-1.048l1.178.236a1.125 1.125 0 001.302-.795l.208-.73a1.125 1.125 0 00-.578-1.315l-.665-.332-.091.091a2.25 2.25 0 01-1.591.659h-.18c-.249 0-.487.1-.662.274a.931.931 0 01-1.458-1.137l1.411-2.353a2.25 2.25 0 00.286-.76m11.928 9.869A9 9 0 008.965 3.525m11.928 9.868A9 9 0 118.965 3.525" />
                            </svg>
                            Connect With Us
                        </h3>
                        <div class="ml-7">
                            <a href="https://facebook.com/yaozambia"
                                class="inline-flex items-center text-gray-600 hover:text-yao-primary-600 transition-colors"
                                target="_blank" rel="noopener noreferrer">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
                                    class="w-5 h-5 mr-2">
                                    <path
                                        d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                                </svg>
                                Facebook
                            </a>
                        </div>
                    </div>
                </div>
                <?php
                } else {
                    // Fallback if no address is found
                    ?>
                <div class="space-y-6">
                    <!-- Address -->
                    <div>
                        <h3 class="text-lg font-semibold mb-2 flex items-center gap-2 text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-5 h-5 text-yao-primary-600">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21" />
                            </svg>
                            Head Office
                        </h3>
                        <address class="not-italic text-gray-600 ml-7">
                            <p class="font-medium">Youth Activists Organisation</p>
                            <p>P.O Box 9100431, NAPSA Building</p>
                            <p>Fifth Floor, Room 533</p>
                            <p>Mongu</p>
                        </address>
                    </div>

                    <!-- Phone -->
                    <div>
                        <h3 class="text-lg font-semibold mb-2 flex items-center gap-2 text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-5 h-5 text-yao-primary-600">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                            </svg>
                            Phone
                        </h3>
                        <div class="ml-7">
                            <p class="text-gray-600">
                                <a href="tel:+260976330092" class="hover:text-yao-primary-600 transition-colors">
                                    +260 976 330092
                                </a>
                            </p>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div>
                        <h3 class="text-lg font-semibold mb-2 flex items-center gap-2 text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-5 h-5 text-yao-primary-600">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M20.893 13.393l-1.135-1.135a2.252 2.252 0 01-.421-.585l-1.08-2.16a.414.414 0 00-.663-.107.827.827 0 01-.812.21l-1.273-.363a.89.89 0 00-.738 1.595l.587.39c.59.395.674 1.23.172 1.732l-.2.2c-.212.212-.33.498-.33.796v.41c0 .409-.11.809-.32 1.158l-1.315 2.191a2.11 2.11 0 01-1.81 1.025 1.055 1.055 0 01-1.055-1.055v-1.172c0-.92-.56-1.747-1.414-2.089l-.655-.261a2.25 2.25 0 01-1.383-2.46l.007-.042a2.25 2.25 0 01.29-.787l.09-.15a2.25 2.25 0 012.37-1.048l1.178.236a1.125 1.125 0 001.302-.795l.208-.73a1.125 1.125 0 00-.578-1.315l-.665-.332-.091.091a2.25 2.25 0 01-1.591.659h-.18c-.249 0-.487.1-.662.274a.931.931 0 01-1.458-1.137l1.411-2.353a2.25 2.25 0 00.286-.76m11.928 9.869A9 9 0 008.965 3.525m11.928 9.868A9 9 0 118.965 3.525" />
                            </svg>
                            Connect With Us
                        </h3>
                        <div class="ml-7">
                            <a href="https://facebook.com/yaozambia"
                                class="inline-flex items-center text-gray-600 hover:text-yao-primary-600 transition-colors"
                                target="_blank" rel="noopener noreferrer">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
                                    class="w-5 h-5 mr-2">
                                    <path
                                        d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                                </svg>
                                Facebook
                            </a>
                        </div>
                    </div>
                </div>
                <?php
                }
                ?>
            </div>

            <!-- Contact Form - Larger section (now on right) -->
            <div class="lg:w-3/5 bg-gray-50 p-6 md:p-10 rounded-lg shadow-md order-first lg:order-last">
                <h2 class="text-2xl font-andada-pro font-bold mb-6 text-yao-primary-700">Send Us a Message</h2>

                <form id="yao-contact-form" class="space-y-6" x-data="{
                    formData: {
                        full_name: '',
                        email: '',
                        subject: '',
                        phone: '',
                        message: '',
                        recaptcha: ''
                    },
                    errors: {
                        full_name: '',
                        email: '',
                        subject: '',
                        phone: '',
                        message: '',
                        recaptcha: ''
                    },
                    isSubmitting: false,
                    responseMessage: '',
                    responseType: '',
                    showResponse: false,
                    touched: {
                        full_name: false,
                        email: false,
                        subject: false,
                        phone: false,
                        message: false,
                        recaptcha: false
                    },

                    get isFormValid() {
                        // Only validate if fields have been touched
                        if (!this.touched.full_name || !this.touched.email ||
                            !this.touched.subject || !this.touched.message) {
                            return false;
                        }

                        // Check if reCAPTCHA is enabled
                        <?php if (get_option('yao_recaptcha_site_key')) : ?>
                        return this.validateName() &&
                               this.validateEmail() &&
                               this.validateSubject() &&
                               this.validatePhone() &&
                               this.validateMessage() &&
                               this.validateRecaptcha();
                        <?php else : ?>
                        return this.validateName() &&
                               this.validateEmail() &&
                               this.validateSubject() &&
                               this.validatePhone() &&
                               this.validateMessage();
                        <?php endif; ?>
                    },

                    validateName() {
                        this.touched.full_name = true;
                        if (this.formData.full_name.trim() === '') {
                            this.errors.full_name = 'Please enter your full name';
                            return false;
                        } else if (this.formData.full_name.trim().length < 4) {
                            this.errors.full_name = 'Name must be at least 4 characters';
                            return false;
                        } else {
                            this.errors.full_name = '';
                            return true;
                        }
                    },

                    validateEmail() {
                        this.touched.email = true;
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (this.formData.email.trim() === '') {
                            this.errors.email = 'Please enter your email address';
                            return false;
                        } else if (!emailRegex.test(this.formData.email.trim())) {
                            this.errors.email = 'Please enter a valid email address';
                            return false;
                        } else {
                            this.errors.email = '';
                            return true;
                        }
                    },

                    validateSubject() {
                        this.touched.subject = true;
                        if (this.formData.subject.trim() === '') {
                            this.errors.subject = 'Please enter a subject';
                            return false;
                        } else if (this.formData.subject.trim().length < 4) {
                            this.errors.subject = 'Subject must be at least 4 characters';
                            return false;
                        } else {
                            this.errors.subject = '';
                            return true;
                        }
                    },

                    validatePhone() {
                        this.touched.phone = true;
                        if (this.formData.phone.trim() !== '' && this.formData.phone.trim().length < 4) {
                            this.errors.phone = 'Phone number must be at least 4 characters';
                            return false;
                        } else {
                            this.errors.phone = '';
                            return true;
                        }
                    },

                    validateMessage() {
                        this.touched.message = true;
                        if (this.formData.message.trim() === '') {
                            this.errors.message = 'Please enter your message';
                            return false;
                        } else if (this.formData.message.trim().length < 10) {
                            this.errors.message = 'Message must be at least 10 characters';
                            return false;
                        } else {
                            this.errors.message = '';
                            return true;
                        }
                    },

                    <?php if (get_option('yao_recaptcha_site_key')) : ?>
                    validateRecaptcha() {
                        this.touched.recaptcha = true;
                        
                        // Check if reCAPTCHA is verified using our global state
                        if (window.recaptchaVerified === true) {
                            this.errors.recaptcha = '';
                            return true;
                        }
                        
                        // Check if we have a token in our form data
                        if (this.formData.recaptcha && this.formData.recaptcha.length > 0) {
                            this.errors.recaptcha = '';
                            return true;
                        }
                        
                        // If not verified, show error
                        this.errors.recaptcha = 'Please complete the reCAPTCHA verification';
                        return false;
                    },
                    <?php endif; ?>

                    validateAllFields() {
                        // Mark all fields as touched
                        this.touched = {
                            full_name: true,
                            email: true,
                            subject: true,
                            phone: true,
                            message: true
                            <?php if (get_option('yao_recaptcha_site_key')) : ?>,
                            recaptcha: true
                            <?php endif; ?>
                        };

                        // Run all validations
                        const nameValid = this.validateName();
                        const emailValid = this.validateEmail();
                        const subjectValid = this.validateSubject();
                        const phoneValid = this.validatePhone();
                        const messageValid = this.validateMessage();
                        <?php if (get_option('yao_recaptcha_site_key')) : ?>
                        const recaptchaValid = this.validateRecaptcha();
                        
                        return nameValid && emailValid && subjectValid && phoneValid && messageValid && recaptchaValid;
                        <?php else : ?>
                        return nameValid && emailValid && subjectValid && phoneValid && messageValid;
                        <?php endif; ?>
                    },

                    submitForm() {
                        // Validate all fields and stop if not valid
                        if (!this.validateAllFields()) {
                            // Scroll to the first error
                            const firstErrorField = document.querySelector('.border-red-500');
                            if (firstErrorField) {
                                firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                firstErrorField.focus();
                            }
                            return;
                        }

                        this.isSubmitting = true;
                        this.showResponse = false;

                        // Prepare form data
                        const formElement = document.getElementById('yao-contact-form');
                        const formData = new FormData(formElement);
                        formData.append('action', 'yao_submit_contact_form');
                        
                        // Add reCAPTCHA response if available
                        <?php if (get_option('yao_recaptcha_site_key')) : ?>
                        // Get reCAPTCHA response from hidden input
                        const recaptchaResponse = document.getElementById('recaptcha-response').value;
                        if (recaptchaResponse) {
                            formData.append('g-recaptcha-response', recaptchaResponse);
                        } else if (this.formData.recaptcha) {
                            formData.append('g-recaptcha-response', this.formData.recaptcha);
                        }
                        
                        // Also include it as a backup
                        if (this.formData.recaptcha) {
                            formData.append('recaptcha', this.formData.recaptcha);
                        }
                        <?php endif; ?>

                        // Send AJAX request
                        fetch(yao_contact_form.ajax_url, {
                            method: 'POST',
                            body: formData,
                        })
                        .then(response => response.json())
                        .then(data => {
                            this.isSubmitting = false;
                            this.showResponse = true;

                            if (data.success) {
                                this.responseType = 'success';
                                this.responseMessage = data.data.message;

                                // Reset form
                                this.formData = {
                                    full_name: '',
                                    email: '',
                                    subject: '',
                                    phone: '',
                                    message: ''
                                };

                                // Reset touched state
                                this.touched = {
                                    full_name: false,
                                    email: false,
                                    subject: false,
                                    phone: false,
                                    message: false
                                };

                                // Scroll to the top of the form to show the message
                                document.getElementById('form-response').scrollIntoView({ behavior: 'smooth', block: 'start' });
                            } else {
                                this.responseType = 'error';
                                this.responseMessage = data.data.message || 'An error occurred. Please try again.';
                            }
                        })
                        .catch(error => {
                            this.isSubmitting = false;
                            this.showResponse = true;
                            this.responseType = 'error';
                            this.responseMessage = 'An error occurred. Please try again.';
                            console.error('Error:', error);
                        });
                    }
                }" @submit.prevent="submitForm">
                    <?php wp_nonce_field('yao_contact_form_nonce', 'yao_contact_nonce'); ?>

                    <!-- Form Response Message -->
                    <div id="form-response"
                         x-show="showResponse"
                         x-transition
                         :class="{
                            'bg-green-50 text-green-700 border border-green-200': responseType === 'success',
                            'bg-red-50 text-red-700 border border-red-200': responseType === 'error'
                         }"
                         class="p-4 rounded-md mb-6"
                         x-text="responseMessage">
                    </div>

                    <!-- Full Name -->
                    <div>
                        <label for="full_name" class="block text-sm font-medium text-gray-700 mb-1">Full Name <span
                                class="text-red-600">*</span></label>
                        <input type="text" id="full_name" name="full_name" required
                            x-model="formData.full_name"
                            @blur="validateName()"
                            @input="validateName()"
                            :class="{'border-red-500': errors.full_name}"
                            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-yao-primary-500 focus:border-yao-primary-500">
                        <div x-show="errors.full_name" x-text="errors.full_name" class="text-red-600 text-sm mt-1"></div>
                    </div>

                    <!-- Email Address -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address <span
                                class="text-red-600">*</span></label>
                        <input type="email" id="email" name="email" required
                            x-model="formData.email"
                            @blur="validateEmail()"
                            @input="validateEmail()"
                            :class="{'border-red-500': errors.email}"
                            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-yao-primary-500 focus:border-yao-primary-500">
                        <div x-show="errors.email" x-text="errors.email" class="text-red-600 text-sm mt-1"></div>
                    </div>

                    <!-- Subject -->
                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Subject <span
                                class="text-red-600">*</span></label>
                        <input type="text" id="subject" name="subject" required
                            x-model="formData.subject"
                            @blur="validateSubject()"
                            @input="validateSubject()"
                            :class="{'border-red-500': errors.subject}"
                            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-yao-primary-500 focus:border-yao-primary-500">
                        <div x-show="errors.subject" x-text="errors.subject" class="text-red-600 text-sm mt-1"></div>
                    </div>

                    <!-- Phone (optional) -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone (optional)</label>
                        <input type="tel" id="phone" name="phone"
                            x-model="formData.phone"
                            @blur="validatePhone()"
                            @input="validatePhone()"
                            :class="{'border-red-500': errors.phone}"
                            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-yao-primary-500 focus:border-yao-primary-500">
                        <div x-show="errors.phone" x-text="errors.phone" class="text-red-600 text-sm mt-1"></div>
                    </div>

                    <!-- Message -->
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message <span
                                class="text-red-600">*</span></label>
                        <textarea id="message" name="message" rows="6" required
                            x-model="formData.message"
                            @blur="validateMessage()"
                            @input="validateMessage()"
                            :class="{'border-red-500': errors.message}"
                            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-yao-primary-500 focus:border-yao-primary-500"></textarea>
                        <div x-show="errors.message" x-text="errors.message" class="text-red-600 text-sm mt-1"></div>
                    </div>

                    <!-- reCAPTCHA -->
                    <?php if (get_option('yao_recaptcha_site_key')) : ?>
                    <div class="mt-4">
                        <!-- Hidden input to store reCAPTCHA response -->
                        <input type="hidden" id="recaptcha-response" name="g-recaptcha-response" x-model="formData.recaptcha">
                        
                        <!-- reCAPTCHA container -->
                        <div id="recaptcha-container"></div>
                        
                        <!-- Error message -->
                        <div x-show="errors.recaptcha" x-text="errors.recaptcha" class="text-red-600 text-sm mt-1"></div>
                    </div>
                    
                    <!-- reCAPTCHA script -->
                    <script>
                        // Global variable to track reCAPTCHA state
                        window.recaptchaVerified = false;
                        
                        // Function to manually update Alpine.js state
                        function updateRecaptchaState(token, isValid) {
                            // Get the form element
                            const form = document.getElementById('yao-contact-form');
                            if (!form) return;
                            
                            // Get the hidden input
                            const hiddenInput = document.getElementById('recaptcha-response');
                            if (hiddenInput) {
                                hiddenInput.value = token;
                                // Dispatch input event to trigger Alpine reactivity
                                hiddenInput.dispatchEvent(new Event('input', { bubbles: true }));
                            }
                            
                            // Set global state
                            window.recaptchaVerified = isValid;
                            
                            // Force re-evaluation of form validity
                            if (form.__x && form.__x.$data) {
                                const formData = form.__x.$data;
                                
                                // Update Alpine.js model
                                formData.formData.recaptcha = token;
                                formData.touched.recaptcha = true;
                                formData.errors.recaptcha = isValid ? '' : 'Please complete the reCAPTCHA verification';
                                
                                // Force re-evaluation
                                if (typeof formData.validateAllFields === 'function') {
                                    formData.validateAllFields();
                                }
                                
                                // Manually trigger reactivity by modifying a field and then restoring it
                                const originalName = formData.formData.full_name;
                                formData.formData.full_name = originalName + ' ';
                                setTimeout(() => {
                                    formData.formData.full_name = originalName;
                                }, 10);
                            }
                            
                            // Directly update the button state based on reCAPTCHA validity
                            const submitButton = document.getElementById('submit-button');
                            if (submitButton) {
                                if (!isValid) {
                                    // If reCAPTCHA is invalid, disable the button
                                    submitButton.disabled = true;
                                    submitButton.classList.add('opacity-75', 'cursor-not-allowed', 'bg-gray-400');
                                    submitButton.classList.remove('bg-yao-primary-600', 'hover:bg-yao-primary-700');
                                } else {
                                    // If reCAPTCHA is valid, check other fields before enabling
                                    checkFormValidity();
                                }
                            }
                            
                            // Force a check of form validity after a short delay
                            setTimeout(checkFormValidity, 100);
                        }
                        
                        // Callback when reCAPTCHA is completed
                        function onRecaptchaCompleted(token) {
                            // Reset any error state
                            const form = document.getElementById('yao-contact-form');
                            if (form && form.__x && form.__x.$data) {
                                form.__x.$data.errors.recaptcha = '';
                            }
                            
                            // Set global verification state to true
                            window.recaptchaVerified = true;
                            
                            // Update the hidden input
                            const hiddenInput = document.getElementById('recaptcha-response');
                            if (hiddenInput) {
                                hiddenInput.value = token;
                                hiddenInput.dispatchEvent(new Event('input', { bubbles: true }));
                            }
                            
                            // Update Alpine.js state
                            if (form && form.__x && form.__x.$data) {
                                form.__x.$data.formData.recaptcha = token;
                                form.__x.$data.touched.recaptcha = true;
                                
                                // Force re-evaluation
                                if (typeof form.__x.$data.validateAllFields === 'function') {
                                    form.__x.$data.validateAllFields();
                                }
                            }
                            
                            // Directly enable the button if other fields are valid
                            const submitButton = document.getElementById('submit-button');
                            if (submitButton) {
                                // Check other fields
                                checkFormValidity();
                                
                                // Force another check after a short delay
                                setTimeout(checkFormValidity, 100);
                            }
                            
                            // Also use the standard update function as a backup
                            updateRecaptchaState(token, true);
                        }
                        
                        // Callback when reCAPTCHA expires
                        function onRecaptchaExpired() {
                            console.log('reCAPTCHA expired');
                            
                            // First update the reCAPTCHA state
                            updateRecaptchaState('', false);
                            
                            // Set error message
                            const form = document.getElementById('yao-contact-form');
                            if (form && form.__x && form.__x.$data) {
                                form.__x.$data.errors.recaptcha = 'reCAPTCHA has expired. Please check the box again.';
                                form.__x.$data.formData.recaptcha = '';
                            }
                            
                            // Clear the hidden input
                            const hiddenInput = document.getElementById('recaptcha-response');
                            if (hiddenInput) {
                                hiddenInput.value = '';
                                hiddenInput.dispatchEvent(new Event('input', { bubbles: true }));
                            }
                            
                            // Reset global state
                            window.recaptchaVerified = false;
                            
                            // Directly disable the button
                            const submitButton = document.getElementById('submit-button');
                            if (submitButton) {
                                submitButton.disabled = true;
                                submitButton.classList.add('opacity-75', 'cursor-not-allowed', 'bg-gray-400');
                                submitButton.classList.remove('bg-yao-primary-600', 'hover:bg-yao-primary-700');
                            }
                            
                            // Force form validity check
                            setTimeout(checkFormValidity, 100);
                            
                            // Reset reCAPTCHA after a short delay to allow user to see the error
                            setTimeout(function() {
                                try {
                                    // Reset the reCAPTCHA widget
                                    if (typeof grecaptcha !== 'undefined' && window.recaptchaWidgetId !== null) {
                                        grecaptcha.reset(window.recaptchaWidgetId);
                                    }
                                } catch (e) {
                                    console.error('Error resetting reCAPTCHA:', e);
                                }
                            }, 1000);
                        }
                        
                        // Callback when reCAPTCHA has an error
                        function onRecaptchaError() {
                            updateRecaptchaState('', false);
                        }
                        
                        // Load and render reCAPTCHA
                        window.onload = function() {
                            // Create script element
                            const script = document.createElement('script');
                            script.src = 'https://www.google.com/recaptcha/api.js?onload=onRecaptchaApiLoad&render=explicit';
                            script.async = true;
                            script.defer = true;
                            document.head.appendChild(script);
                            
                            // Add a mutation observer to watch for reCAPTCHA changes
                            setTimeout(function() {
                                const recaptchaContainer = document.getElementById('recaptcha-container');
                                if (recaptchaContainer) {
                                    // Add click listener to the reCAPTCHA container
                                    recaptchaContainer.addEventListener('click', function() {
                                        // If reCAPTCHA was previously expired, this click might be to re-verify
                                        if (!window.recaptchaVerified) {
                                            console.log('reCAPTCHA container clicked, might be re-verifying');
                                            
                                            // Clear any error messages
                                            const form = document.getElementById('yao-contact-form');
                                            if (form && form.__x && form.__x.$data) {
                                                form.__x.$data.errors.recaptcha = '';
                                            }
                                            
                                            // Check again after a delay to catch the new state
                                            setTimeout(checkFormValidity, 500);
                                            setTimeout(checkFormValidity, 1000);
                                            setTimeout(checkFormValidity, 2000);
                                        }
                                    });
                                    
                                    // Set up mutation observer
                                    const observer = new MutationObserver(function(mutations) {
                                        // When reCAPTCHA changes, check form validity
                                        console.log('reCAPTCHA container mutation detected');
                                        checkFormValidity();
                                        
                                        // Check again after a delay
                                        setTimeout(checkFormValidity, 500);
                                    });
                                    
                                    observer.observe(recaptchaContainer, { 
                                        childList: true, 
                                        subtree: true,
                                        attributes: true,
                                        attributeFilter: ['class', 'style']
                                    });
                                }
                            }, 1000);
                        };
                        
                        // Store reCAPTCHA widget ID for reset functionality
                        window.recaptchaWidgetId = null;
                        
                        // Function to reset reCAPTCHA
                        function resetRecaptcha() {
                            if (typeof grecaptcha !== 'undefined' && window.recaptchaWidgetId !== null) {
                                try {
                                    grecaptcha.reset(window.recaptchaWidgetId);
                                    
                                    // Clear the form data
                                    const form = document.getElementById('yao-contact-form');
                                    if (form && form.__x && form.__x.$data) {
                                        form.__x.$data.formData.recaptcha = '';
                                        form.__x.$data.errors.recaptcha = '';
                                    }
                                    
                                    // Clear the hidden input
                                    const hiddenInput = document.getElementById('recaptcha-response');
                                    if (hiddenInput) {
                                        hiddenInput.value = '';
                                    }
                                    
                                    // Reset global state
                                    window.recaptchaVerified = false;
                                    
                                    // Update button state
                                    checkFormValidity();
                                } catch (e) {
                                    console.error('Error resetting reCAPTCHA:', e);
                                }
                            }
                        }
                        
                        // Callback when reCAPTCHA API is loaded
                        function onRecaptchaApiLoad() {
                            // Render the reCAPTCHA
                            window.recaptchaWidgetId = grecaptcha.render('recaptcha-container', {
                                'sitekey': '<?php echo esc_attr(get_option('yao_recaptcha_site_key')); ?>',
                                'callback': onRecaptchaCompleted,
                                'expired-callback': onRecaptchaExpired,
                                'error-callback': onRecaptchaError
                            });
                        }
                    </script>
                    <?php endif; ?>

                    <!-- Submit Button -->
                    <div>
                        <button id="submit-button" type="submit"
                            :disabled="isSubmitting || !isFormValid"
                            :class="{
                                'opacity-75 cursor-not-allowed': isSubmitting || !isFormValid,
                                'bg-yao-primary-600 hover:bg-yao-primary-700': isFormValid && !isSubmitting,
                                'bg-gray-400': !isFormValid && !isSubmitting
                            }"
                            class="w-full text-white py-3 px-6 rounded-md focus:outline-none focus:ring-2 focus:ring-yao-primary-500 focus:ring-offset-2 transition-colors text-lg font-medium">
                            <span x-show="!isSubmitting">Send Message</span>
                            <span x-show="isSubmitting" class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Sending...
                            </span>
                        </button>
                        <p x-show="!isFormValid && (touched.full_name || touched.email || touched.subject || touched.message)"
                           class="text-sm text-center mt-2 text-gray-600">
                           Please fill out all required fields correctly to enable the submit button
                        </p>
                    </div>
                    
                    <!-- Script to directly manage button state -->
                    <script>
                        // Function to check form validity and update button state
                        function checkFormValidity() {
                            const form = document.getElementById('yao-contact-form');
                            if (!form || !form.__x || !form.__x.$data) return;
                            
                            const formData = form.__x.$data;
                            const submitButton = document.getElementById('submit-button');
                            
                            // Check if all required fields are valid
                            const nameValid = formData.formData.full_name && formData.formData.full_name.trim().length > 0;
                            const emailValid = formData.formData.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.formData.email);
                            const subjectValid = formData.formData.subject && formData.formData.subject.trim().length > 0;
                            const messageValid = formData.formData.message && formData.formData.message.trim().length >= 10;
                            
                            // Check reCAPTCHA validity - multiple checks for redundancy
                            let recaptchaValid = false;
                            
                            // 1. Check global state
                            if (window.recaptchaVerified === true) {
                                recaptchaValid = true;
                            }
                            // 2. Check if we have a token in the form data
                            else if (formData.formData.recaptcha && formData.formData.recaptcha.length > 0) {
                                // 3. Verify the token hasn't expired by checking with grecaptcha
                                try {
                                    const currentResponse = grecaptcha.getResponse();
                                    if (currentResponse && currentResponse.length > 0) {
                                        recaptchaValid = true;
                                    } else {
                                        // If grecaptcha returns empty but we have a token, it might have expired
                                        recaptchaValid = false;
                                        // Update form state to reflect expiration
                                        formData.formData.recaptcha = '';
                                        formData.errors.recaptcha = 'reCAPTCHA has expired. Please check the box again.';
                                        window.recaptchaVerified = false;
                                    }
                                } catch (e) {
                                    // If there's an error accessing grecaptcha, assume it's invalid
                                    recaptchaValid = false;
                                }
                            }
                            
                            // Special handling for reCAPTCHA - if it was just completed, force it to be valid
                            if (document.getElementById('recaptcha-response').value.length > 0) {
                                recaptchaValid = true;
                                window.recaptchaVerified = true;
                                formData.errors.recaptcha = '';
                            }
                            
                            // Update button state directly
                            const isValid = nameValid && emailValid && subjectValid && messageValid && recaptchaValid;
                            
                            // Log state for debugging
                            console.log('Form validity check:', { 
                                nameValid, emailValid, subjectValid, messageValid, recaptchaValid, 
                                isValid, 
                                recaptchaValue: document.getElementById('recaptcha-response').value.length > 0,
                                globalState: window.recaptchaVerified
                            });
                            
                            if (isValid && !formData.isSubmitting) {
                                submitButton.disabled = false;
                                submitButton.classList.remove('bg-gray-400');
                                submitButton.classList.add('bg-yao-primary-600', 'hover:bg-yao-primary-700');
                                submitButton.classList.remove('opacity-75', 'cursor-not-allowed');
                                
                                // Also update Alpine.js state
                                formData.isFormValid = true;
                            } else {
                                submitButton.disabled = true;
                                submitButton.classList.add('opacity-75', 'cursor-not-allowed');
                                if (!formData.isSubmitting) {
                                    submitButton.classList.add('bg-gray-400');
                                    submitButton.classList.remove('bg-yao-primary-600', 'hover:bg-yao-primary-700');
                                }
                                
                                // Also update Alpine.js state
                                formData.isFormValid = false;
                            }
                        }
                        
                        // Set up interval to check form validity
                        document.addEventListener('DOMContentLoaded', function() {
                            // Check every 200ms for more responsive updates
                            setInterval(checkFormValidity, 200);
                            
                            // Also check on any input change
                            const form = document.getElementById('yao-contact-form');
                            if (form) {
                                form.addEventListener('input', checkFormValidity);
                            }
                            
                            // Add specific event listener for reCAPTCHA expiration
                            if (typeof grecaptcha !== 'undefined') {
                                // Check reCAPTCHA state every 2 seconds (reCAPTCHA expires after 2 minutes)
                                setInterval(function() {
                                    try {
                                        // If we think reCAPTCHA is valid, verify it still is
                                        if (window.recaptchaVerified) {
                                            const response = grecaptcha.getResponse();
                                            if (!response || response.length === 0) {
                                                // If response is empty but we thought it was valid, it has expired
                                                window.recaptchaVerified = false;
                                                
                                                // Update form state
                                                const form = document.getElementById('yao-contact-form');
                                                if (form && form.__x && form.__x.$data) {
                                                    form.__x.$data.formData.recaptcha = '';
                                                    form.__x.$data.errors.recaptcha = 'reCAPTCHA has expired. Please check the box again.';
                                                }
                                                
                                                // Force button update
                                                checkFormValidity();
                                            }
                                        }
                                    } catch (e) {
                                        console.log('Error checking reCAPTCHA state:', e);
                                    }
                                }, 2000);
                            }
                        });
                    </script>
                </form>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>