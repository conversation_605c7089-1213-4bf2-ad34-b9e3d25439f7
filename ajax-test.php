<?php
/**
 * AJAX Test Page
 */
get_header();
?>

<div class="container">
    <h1>AJAX Test Page</h1>
    
    <div id="ajax-test-result"></div>
    
    <button id="test-ajax-button" class="button">Test AJAX Connection</button>
</div>

<script>
jQuery(document).ready(function($) {
    $('#test-ajax-button').on('click', function() {
        $('#ajax-test-result').html('<p>Testing AJAX connection...</p>');
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'nkhwazi_submit_application_form',
                nonce: '<?php echo wp_create_nonce('nkhwazi_application_form_nonce'); ?>',
                test: 'This is a test'
            },
            success: function(response) {
                $('#ajax-test-result').html('<p>AJAX Success Response:</p><pre>' + JSON.stringify(response, null, 2) + '</pre>');
            },
            error: function(xhr, status, error) {
                $('#ajax-test-result').html('<p>AJAX Error:</p><p>' + status + ': ' + error + '</p><p>Response Text:</p><pre>' + xhr.responseText + '</pre>');
            }
        });
    });
});
</script>

<?php get_footer(); ?>