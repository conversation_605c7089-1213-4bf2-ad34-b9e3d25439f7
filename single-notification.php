<?php
/**
 * Template for displaying single notification
 *
 * @package Nkhwazi_Primary_School
 */

get_header();

// Get ACF fields
$heading = get_field('heading');
$description = get_field('description');
// Note: expiry_date is not displayed as per requirements
?>

<!-- MAIN -->
<main role="main">
    <?php while (have_posts()) : the_post(); ?>
    <!-- Content -->
    <article>
        <div class="line">
            <header class="rounded-div section background-primary background-transparent text-center"
                data-image-src="<?php echo get_template_directory_uri(); ?>/assets/img/parallax-02.jpg">
                <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">School Notification</h1>
            </header>
        </div>

        <div class="section background-white">
            <div class="line">
                <div class="margin">
                    <!-- Notification Details -->
                    <div class="s-12 m-12 l-8 center">
                        <div class="notification-detail background-white padding-2x rounded-div box-shadow margin-bottom-30">
                            <h2 class="text-strong text-size-20 margin-bottom-5"><?php echo esc_html($heading); ?></h2>
                            <small class="text-grey margin-bottom-15 display-block">Posted: <?php echo get_the_date('F j, Y'); ?></small>
                            <div class="margin-bottom-20"><?php echo wp_kses_post($description); ?></div>
                            
                            <!-- Back button -->
                            <div class="text-center margin-top-30">
                                <?php 
                                // Get the notifications page ID - assumes a page with slug 'notifications' exists
                                $notifications_page = get_page_by_path('notifications');
                                $notifications_url = $notifications_page ? get_permalink($notifications_page->ID) : home_url();
                                ?>
                                <a href="<?php echo esc_url($notifications_url); ?>" class="button rounded-btn text-white background-primary">Back to All Notifications</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </article>
    <?php endwhile; ?>
</main>

<?php get_footer(); ?>