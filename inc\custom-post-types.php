<?php
/**
 * Custom Post Types for Nkhwazi Primary School
 */

// Register Custom Post Types
function nkhwazi_register_post_types() {
    
    // Management Post Type (trustees, board_of_management, administration_staff, teaching_staff)
    register_post_type('management', array(
        'labels' => array(
            'name' => 'Management',
            'singular_name' => 'Management Member',
            'add_new' => 'Add New Member',
            'add_new_item' => 'Add New Management Member',
            'edit_item' => 'Edit Management Member',
            'new_item' => 'New Management Member',
            'view_item' => 'View Management Member',
            'search_items' => 'Search Management Members',
            'not_found' => 'No management members found',
            'not_found_in_trash' => 'No management members found in trash',
        ),
        'public' => true,
        'has_archive' => false, // Disable archives - we use dedicated pages instead
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-groups',
        'rewrite' => array('slug' => 'management'),
        'show_in_rest' => true, // Enable <PERSON> editor
        'capability_type' => 'management',
        'map_meta_cap' => true,
    ));

    // Teacher Post Type
    register_post_type('teacher', array(
        'labels' => array(
            'name' => 'Teachers',
            'singular_name' => 'Teacher',
            'add_new' => 'Add New Teacher',
            'add_new_item' => 'Add New Teacher',
            'edit_item' => 'Edit Teacher',
            'new_item' => 'New Teacher',
            'view_item' => 'View Teacher',
            'search_items' => 'Search Teachers',
            'not_found' => 'No teachers found',
            'not_found_in_trash' => 'No teachers found in trash',
        ),
        'public' => true,
        'has_archive' => false, // Disable archives - we use dedicated pages instead
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-welcome-learn-more',
        'rewrite' => array('slug' => 'teachers'),
        'show_in_rest' => true, // Enable Gutenberg editor
        'capability_type' => 'teacher',
        'map_meta_cap' => true,
    ));

    // School Categories Post Type
    register_post_type('school_categories', array(
        'labels' => array(
            'name' => 'School Categories',
            'singular_name' => 'School Category',
            'add_new' => 'Add New Category',
            'add_new_item' => 'Add New School Category',
            'edit_item' => 'Edit School Category',
            'new_item' => 'New School Category',
            'view_item' => 'View School Category',
            'search_items' => 'Search School Categories',
            'not_found' => 'No school categories found',
            'not_found_in_trash' => 'No school categories found in trash',
        ),
        'public' => true,
        'has_archive' => false,
        'supports' => array('title', 'editor'),
        'menu_icon' => 'dashicons-category',
        'rewrite' => array('slug' => 'school-categories'),
        'capability_type' => 'school_category',
        'map_meta_cap' => true,
        'show_in_rest' => true, // Enable Gutenberg editor
    ));

    // Frontpage Slideshow Post Type
    register_post_type('frontpage_slideshow', array(
        'labels' => array(
            'name' => 'Frontpage Slideshow',
            'singular_name' => 'Slide',
            'add_new' => 'Add New Slide',
            'add_new_item' => 'Add New Slide',
            'edit_item' => 'Edit Slide',
            'new_item' => 'New Slide',
            'view_item' => 'View Slide',
            'search_items' => 'Search Slides',
            'not_found' => 'No slides found',
            'not_found_in_trash' => 'No slides found in trash',
        ),
        'public' => true,
        'has_archive' => false,
        'supports' => array('title'),
        'menu_icon' => 'dashicons-images-alt',
        'rewrite' => array('slug' => 'slides'),
        'capability_type' => 'slide',
        'map_meta_cap' => true,
    ));

    // Upcoming Event Post Type
    register_post_type('upcoming_event', array(
        'labels' => array(
            'name' => 'Upcoming Events',
            'singular_name' => 'Event',
            'add_new' => 'Add New Event',
            'add_new_item' => 'Add New Event',
            'edit_item' => 'Edit Event',
            'new_item' => 'New Event',
            'view_item' => 'View Event',
            'search_items' => 'Search Events',
            'not_found' => 'No events found',
            'not_found_in_trash' => 'No events found in trash',
        ),
        'public' => true,
        'has_archive' => false, // Disable archives - we use dedicated pages instead
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-calendar-alt',
        'rewrite' => array('slug' => 'events'),
        'show_in_rest' => true, // Enable Gutenberg editor
        'capability_type' => 'event',
        'map_meta_cap' => true,
    ));

    // Photo Gallery Post Type
    register_post_type('photo_gallery', array(
        'labels' => array(
            'name' => 'Photo Galleries',
            'singular_name' => 'Photo Gallery',
            'add_new' => 'Add New Gallery',
            'add_new_item' => 'Add New Photo Gallery',
            'edit_item' => 'Edit Photo Gallery',
            'new_item' => 'New Photo Gallery',
            'view_item' => 'View Photo Gallery',
            'search_items' => 'Search Photo Galleries',
            'not_found' => 'No photo galleries found',
            'not_found_in_trash' => 'No photo galleries found in trash',
        ),
        'public' => true,
        'has_archive' => false, // Disable archives - we use dedicated pages instead
        'supports' => array('title'),
        'menu_icon' => 'dashicons-format-gallery',
        'rewrite' => array('slug' => 'galleries'),
        'capability_type' => 'gallery',
        'map_meta_cap' => true,
    ));

    // Address Post Type
    register_post_type('address', array(
        'labels' => array(
            'name' => 'Addresses',
            'singular_name' => 'Address',
            'add_new' => 'Add New Address',
            'add_new_item' => 'Add New Address',
            'edit_item' => 'Edit Address',
            'new_item' => 'New Address',
            'view_item' => 'View Address',
            'search_items' => 'Search Addresses',
            'not_found' => 'No addresses found',
            'not_found_in_trash' => 'No addresses found in trash',
        ),
        'public' => true,
        'has_archive' => false,
        'supports' => array('title'),
        'menu_icon' => 'dashicons-location',
        'rewrite' => array('slug' => 'addresses'),
        'capability_type' => 'address',
        'map_meta_cap' => true,
    ));

    // Admin Contact Info Post Type
    register_post_type('admin_contact_info', array(
        'labels' => array(
            'name' => 'Admin Contact Info',
            'singular_name' => 'Admin Contact',
            'add_new' => 'Add New Contact',
            'add_new_item' => 'Add New Admin Contact',
            'edit_item' => 'Edit Admin Contact',
            'new_item' => 'New Admin Contact',
            'view_item' => 'View Admin Contact',
            'search_items' => 'Search Admin Contacts',
            'not_found' => 'No admin contacts found',
            'not_found_in_trash' => 'No admin contacts found in trash',
        ),
        'public' => true,
        'has_archive' => false,
        'supports' => array('title'),
        'menu_icon' => 'dashicons-phone',
        'rewrite' => array('slug' => 'admin-contacts'),
        'capability_type' => 'admin_contact',
        'map_meta_cap' => true,
    ));

    // Nkhwazi Stats Post Type
    register_post_type('nkhwazi_stats', array(
        'labels' => array(
            'name' => 'Nkhwazi Stats',
            'singular_name' => 'Stat',
            'add_new' => 'Add New Stat',
            'add_new_item' => 'Add New Stat',
            'edit_item' => 'Edit Stat',
            'new_item' => 'New Stat',
            'view_item' => 'View Stat',
            'search_items' => 'Search Stats',
            'not_found' => 'No stats found',
            'not_found_in_trash' => 'No stats found in trash',
        ),
        'public' => true,
        'has_archive' => false,
        'supports' => array('title'),
        'menu_icon' => 'dashicons-chart-bar',
        'rewrite' => array('slug' => 'stats'),
        'capability_type' => 'stat',
        'map_meta_cap' => true,
    ));

    // Extracurricular Post Type
    register_post_type('extracurricular', array(
        'labels' => array(
            'name' => 'Extracurricular',
            'singular_name' => 'Extracurricular',
            'add_new' => 'Add New Extracurricular',
            'add_new_item' => 'Add New Extracurricular',
            'edit_item' => 'Edit Extracurricular',
            'new_item' => 'New Extracurricular',
            'view_item' => 'View Extracurricular',
            'search_items' => 'Search Extracurricular',
            'not_found' => 'No extracurricular found',
            'not_found_in_trash' => 'No extracurricular found in trash',
        ),
        'public' => true,
        'has_archive' => false,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-groups',
        'rewrite' => array('slug' => 'extracurricular'),
        'show_in_rest' => true, // Enable Gutenberg editor
        'capability_type' => 'extracurricular',
        'map_meta_cap' => true,
    ));

    // Extracurricular Item Post Type
    register_post_type('extracurricular_item', array(
        'labels' => array(
            'name' => 'Extracurricular Items',
            'singular_name' => 'Item',
            'add_new' => 'Add New Item',
            'add_new_item' => 'Add New Item',
            'edit_item' => 'Edit Item',
            'new_item' => 'New Item',
            'view_item' => 'View Item',
            'search_items' => 'Search Items',
            'not_found' => 'No items found',
            'not_found_in_trash' => 'No items found in trash',
        ),
        'public' => true,
        'has_archive' => false,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-universal-access',
        'rewrite' => array('slug' => 'extracurricular-items'),
        'show_in_rest' => true, // Enable Gutenberg editor
        'capability_type' => 'extracurricular_item',
        'map_meta_cap' => true,
    ));

    // Job Post Type
    register_post_type('job', array(
        'labels' => array(
            'name' => 'Jobs',
            'singular_name' => 'Job',
            'add_new' => 'Add New Job',
            'add_new_item' => 'Add New Job',
            'edit_item' => 'Edit Job',
            'new_item' => 'New Job',
            'view_item' => 'View Job',
            'search_items' => 'Search Jobs',
            'not_found' => 'No jobs found',
            'not_found_in_trash' => 'No jobs found in trash',
        ),
        'public' => true,
        'has_archive' => false, // Disable archives - we use page-jobs.php instead
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-businessman',
        'rewrite' => array('slug' => 'jobs'),
        'show_in_rest' => true, // Enable Gutenberg editor
        'capability_type' => 'job',
        'map_meta_cap' => true,
    ));
    
    // Management Type Post Type
    register_post_type('management_type', array(
        'labels' => array(
            'name' => 'Management Types',
            'singular_name' => 'Management Type',
            'add_new' => 'Add New Management Type',
            'add_new_item' => 'Add New Management Type',
            'edit_item' => 'Edit Management Type',
            'new_item' => 'New Management Type',
            'view_item' => 'View Management Type',
            'search_items' => 'Search Management Types',
            'not_found' => 'No management types found',
            'not_found_in_trash' => 'No management types found in trash',
        ),
        'public' => true,
        'has_archive' => false,
        'supports' => array('title'),
        'menu_icon' => 'dashicons-groups',
        'rewrite' => array('slug' => 'management-types'),
        'show_in_rest' => true, // Enable Gutenberg editor
        'capability_type' => 'management_type',
        'map_meta_cap' => true,
    ));
    
    // Notifications Post Type
    register_post_type('notification', array(
        'labels' => array(
            'name' => 'Notifications',
            'singular_name' => 'Notification',
            'add_new' => 'Add New Notification',
            'add_new_item' => 'Add New Notification',
            'edit_item' => 'Edit Notification',
            'new_item' => 'New Notification',
            'view_item' => 'View Notification',
            'search_items' => 'Search Notifications',
            'not_found' => 'No notifications found',
            'not_found_in_trash' => 'No notifications found in trash',
        ),
        'public' => true,
        'has_archive' => false, // No archive needed, using page-notifications.php instead
        'supports' => array('title'),
        'menu_icon' => 'dashicons-megaphone',
        'rewrite' => array('slug' => 'notification'),
        'show_in_rest' => true, // Enable Gutenberg editor
        'capability_type' => 'notification',
        'map_meta_cap' => true,
    ));
}
add_action('init', 'nkhwazi_register_post_types');

// Register custom image sizes
function nkhwazi_register_image_sizes() {
    // Blog image size
    add_image_size('blog-thumbnail', 720, 405, true);
    
    // Team photo size
    add_image_size('team-photo', 500, 666, true);
    
    // Home slide photo size
    add_image_size('home-slide', 1280, 720, true);
    
    // Photo gallery size
    add_image_size('photo-gallery', 1280, 720, true);
    
    // Gallery card image size
    add_image_size('gallery-card', 496, 279, true);
    
    // Generic page image size (1280 x 500)
    add_image_size('page-image', 1280, 500, true);
    
    // School category card size
    add_image_size('school-category-card', 480, 270, true);
    
    // Extracurricular card size
    add_image_size('extracurricular-card', 480, 270, true);
}
add_action('after_setup_theme', 'nkhwazi_register_image_sizes');