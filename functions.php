<?php
/**
 * Nkhwazi Primary School Theme functions and definitions
 */

if (!defined('_S_VERSION')) {
    // Replace the version number of the theme on each release.
    define('_S_VERSION', '1.0.0');
}

// Include the simple AJAX handler
require_once(get_template_directory() . '/simple-ajax-handler.php');

// Include the application form functionality
require_once(get_template_directory() . '/includes/application-form.php');

// Include the update personal details form functionality
require_once(get_template_directory() . '/includes/update-details-form.php');

// Include the simple contact form functionality
require_once(get_template_directory() . '/includes/simple-contact-form.php');

/**
 * Redirect archives to their respective pages
 * 
 * This function ensures that custom post type archives are redirected to their
 * dedicated pages instead of using archive templates. This keeps the site
 * consistent with the design and prevents duplicate content.
 */
function nkhwazi_redirect_archives() {
    // Only run on the front-end
    if (is_admin()) {
        return;
    }
    
    // Redirect job archives to the jobs page
    if (is_post_type_archive('job')) {
        wp_redirect(get_permalink(436));
        exit;
    }
    
    // Redirect upcoming_event archives to the events page
    if (is_post_type_archive('upcoming_event')) {
        wp_redirect(get_permalink(434));
        exit;
    }
    
    // Redirect school_categories archives to the categories page
    if (is_post_type_archive('school_categories')) {
        wp_redirect(get_permalink(328));
        exit;
    }
    
    // Redirect teacher archives to the teaching staff page
    if (is_post_type_archive('teacher')) {
        wp_redirect(get_permalink(376));
        exit;
    }
    
    // Redirect management archives to the school management page
    if (is_post_type_archive('management')) {
        wp_redirect(get_permalink(378));
        exit;
    }
    
    // Redirect extracurricular_item archives to the extracurricular page
    if (is_post_type_archive('extracurricular_item')) {
        wp_redirect(get_permalink(335));
        exit;
    }
}
add_action('template_redirect', 'nkhwazi_redirect_archives');


/**
 * Test AJAX connection
 */
function nkhwazi_test_ajax_connection() {
    wp_send_json_success(array(
        'message' => 'AJAX connection is working correctly!',
        'time' => current_time('mysql')
    ));
}
add_action('wp_ajax_nkhwazi_test_ajax_connection', 'nkhwazi_test_ajax_connection');
add_action('wp_ajax_nopriv_nkhwazi_test_ajax_connection', 'nkhwazi_test_ajax_connection');

/**
 * Add reCAPTCHA script to footer
 */
function nkhwazi_add_recaptcha_script() {
    global $post;
    $load_recaptcha = false;
    
    if (is_page()) {
        // Check if the page is one of our form pages
        if (is_page('apply-online') || is_page('update-personal-details') || is_page('contact') || is_page('contact-us')) {
            $load_recaptcha = true;
        }
        
        // Check if the page content contains our shortcode
        if (isset($post->post_content) && 
            (has_shortcode($post->post_content, 'nkhwazi_contact_form') || 
             has_shortcode($post->post_content, 'nkhwazi_application_form') || 
             has_shortcode($post->post_content, 'nkhwazi_update_details_form'))) {
            $load_recaptcha = true;
        }
    }
    
    // Always define ajaxurl for frontend
    echo '<script>
        var ajaxurl = "' . admin_url('admin-ajax.php') . '";
    </script>';
    
    // Load reCAPTCHA if needed
    if ($load_recaptcha) {
        echo '<script src="https://www.google.com/recaptcha/api.js" async defer></script>';
    }
    
    // Enqueue AJAX test script (only in development)
    if (defined('WP_DEBUG') && WP_DEBUG) {
        wp_enqueue_script(
            'ajax-test-script',
            get_template_directory_uri() . '/assets/js/ajax-test.js',
            array('jquery'),
            time(), // Use current time as version to prevent caching during development
            true
        );
    }
}
add_action('wp_footer', 'nkhwazi_add_recaptcha_script');

/**
 * Controls which post types should display the meta information in single view
 * 
 * @param bool $show Whether to show meta information
 * @param string $post_type The current post type
 * @return bool Modified decision on whether to show meta
 */
function nkhwazi_control_post_meta_display($show, $post_type) {
    // Add custom post types that should show meta info here
    $post_types_with_meta = array(
        'post',          // Default posts
        'gallery',       // If you have a gallery post type
        'event',         // If you have an event post type
        // Add more as needed
    );
    
    return in_array($post_type, $post_types_with_meta);
}
add_filter('nkhwazi_show_post_meta', 'nkhwazi_control_post_meta_display', 10, 2);

/**
 * Force the use of custom template for extracurricular_item post type
 */
function nkhwazi_custom_template_for_extracurricular_item($template) {
    if (is_singular('extracurricular_item')) {
        $new_template = locate_template(array('single-extracurricular-item.php'));
        if ('' != $new_template) {
            return $new_template;
        }
    }
    return $template;
}
add_filter('template_include', 'nkhwazi_custom_template_for_extracurricular_item', 99);

/**
 * Ensure required categories exist for the blog filter
 * This runs only for administrators to avoid unnecessary DB operations
 */
function nkhwazi_ensure_blog_categories() {
    // Only run for administrators
    if (!is_user_logged_in() || !current_user_can('administrator')) {
        return;
    }
    
    // Define the categories we need
    $required_categories = array(
        'events' => 'School Events',
        'academics' => 'Academics',
        'facilities' => 'Facilities',
        'community' => 'Community Service'
    );
    
    // Check and create each category if it doesn't exist
    foreach ($required_categories as $slug => $name) {
        $category = get_term_by('slug', $slug, 'category');
        
        if (!$category) {
            // Category doesn't exist, create it
            wp_insert_term(
                $name,
                'category',
                array(
                    'slug' => $slug
                )
            );
        }
    }
}
add_action('admin_init', 'nkhwazi_ensure_blog_categories');



// Include ACF fields
require_once get_template_directory() . '/includes/acf-fields.php';

/**
 * Delete expired notifications
 * This function runs daily via WP Cron to check for and delete expired notifications
 */
function nkhwazi_delete_expired_notifications() {
    // Get current date in the format returned by ACF
    $current_date = date('d/m/Y');
    
    // Query for notifications with expiry_date less than or equal to today
    $expired_notifications = new WP_Query(array(
        'post_type' => 'notification',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => 'expiry_date',
                'value' => $current_date,
                'compare' => '<=',
                'type' => 'DATE'
            )
        )
    ));
    
    // Delete each expired notification
    if ($expired_notifications->have_posts()) {
        while ($expired_notifications->have_posts()) {
            $expired_notifications->the_post();
            wp_delete_post(get_the_ID(), true); // true = force delete (bypass trash)
        }
    }
    
    wp_reset_postdata();
}
add_action('nkhwazi_daily_cleanup', 'nkhwazi_delete_expired_notifications');

// Schedule the daily cleanup event if not already scheduled
if (!wp_next_scheduled('nkhwazi_daily_cleanup')) {
    wp_schedule_event(time(), 'daily', 'nkhwazi_daily_cleanup');
}

/**
 * Shortcode to display recent notifications
 * Usage: [recent_notifications count="3"]
 */
function nkhwazi_recent_notifications_shortcode($atts) {
    $atts = shortcode_atts(array(
        'count' => 3,
    ), $atts, 'recent_notifications');
    
    $count = intval($atts['count']);
    
    // Query for recent notifications
    $notifications = new WP_Query(array(
        'post_type' => 'notification',
        'posts_per_page' => $count,
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    $output = '';
    
    if ($notifications->have_posts()) {
        $output .= '<div class="recent-notifications">';
        $output .= '<h3 class="margin-bottom-20">Recent Notifications</h3>';
        $output .= '<div class="grid margin">';
        
        while ($notifications->have_posts()) {
            $notifications->the_post();
            $heading = get_field('heading');
            $description = get_field('description');
            
            $post_date = get_the_date('F j, Y');
            $output .= '<div class="s-12 m-6 l-4 margin-bottom-15">';
            $output .= '<div class="notification-card background-white padding-2x rounded-div box-shadow">';
            $output .= '<h4 class="text-strong margin-bottom-5">' . esc_html($heading) . '</h4>';
            $output .= '<small class="text-grey margin-bottom-10 display-block">Posted: ' . esc_html($post_date) . '</small>';
            $output .= '<div class="margin-bottom-0">' . wp_kses_post($description) . '</div>';
            $output .= '</div>';
            $output .= '</div>';
        }
        
        $output .= '</div>';
        $output .= '<div class="s-12 text-center margin-top-10">';
        
        // Get the notifications page ID - assumes a page with slug 'notifications' exists
        $notifications_page = get_page_by_path('notifications');
        $notifications_url = $notifications_page ? get_permalink($notifications_page->ID) : home_url();
        
        $output .= '<a href="' . esc_url($notifications_url) . '" class="button rounded-btn text-white background-primary">View All Notifications</a>';
        $output .= '</div>';
        $output .= '</div>';
    }
    
    wp_reset_postdata();
    
    return $output;
}
add_shortcode('recent_notifications', 'nkhwazi_recent_notifications_shortcode');

// Debug template loading
function debug_template_being_used() {
    if (is_user_logged_in() && current_user_can('administrator')) {
        global $template;
        echo '<!-- Current template: ' . basename($template) . ' -->';
    }
}
add_action('wp_head', 'debug_template_being_used');

// Fix template redirect issues
function nkhwazischool_template_include($template) {
    global $wp_query;
    
    // Debug info
    if (is_user_logged_in() && current_user_can('administrator')) {
        echo '<!-- Template before: ' . basename($template) . ' -->';
        echo '<!-- Is page: ' . (is_page() ? 'yes' : 'no') . ' -->';
        echo '<!-- Page name: ' . (is_page() ? get_queried_object()->post_name : 'none') . ' -->';
        echo '<!-- Is home: ' . (is_home() ? 'yes' : 'no') . ' -->';
        echo '<!-- Has category param: ' . (isset($_GET['category']) ? 'yes (' . $_GET['category'] . ')' : 'no') . ' -->';
    }
    
    // Check for specific page templates
    if (is_page('blog') || is_home()) {
        $new_template = locate_template(array('home.php'));
        if (!empty($new_template)) {
            return $new_template;
        }
    }
    
    if (is_page('school-categories')) {
        $new_template = locate_template(array('page-school-categories.php'));
        if (!empty($new_template)) {
            return $new_template;
        }
    }
    
    // Check for single school category page - but NOT blog filtering
    // Only redirect to page-single-category.php if:
    // 1. It's explicitly the page-single-category page, OR
    // 2. We're on a school-categories URL path, OR
    // 3. We have a category parameter BUT we're not on the blog/home page
    if (is_page('page-single-category') || 
        preg_match('/^\/school-categories\/([^\/]+)\/?$/', $_SERVER['REQUEST_URI']) ||
        (isset($_GET['category']) && !empty($_GET['category']) && $_GET['category'] !== 'all' && !is_home() && !is_page('blog'))) {
        
        $new_template = locate_template(array('page-single-category.php'));
        if (!empty($new_template)) {
            return $new_template;
        }
    }
    
    // Check for school category with post name URL structure
    $uri = $_SERVER['REQUEST_URI'];
    if (preg_match('/^\/school-categories\/([^\/]+)\/?$/', $uri, $matches)) {
        $_GET['category'] = $matches[1]; // Set the category parameter
        $new_template = locate_template(array('page-single-category.php'));
        if (!empty($new_template)) {
            return $new_template;
        }
    }
    
    return $template;
}
add_filter('template_include', 'nkhwazischool_template_include', 99);

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function nkhwazischool_setup() {
    /*
     * Make theme available for translation.
     */
    load_theme_textdomain('nkhwazischool', get_template_directory() . '/languages');

    // Add default posts and comments RSS feed links to head.
    add_theme_support('automatic-feed-links');

    /*
     * Let WordPress manage the document title.
     */
    add_theme_support('title-tag');

    /*
     * Enable support for Post Thumbnails on posts and pages.
     */
    add_theme_support('post-thumbnails');

    // Register navigation menus
    register_nav_menus(
        array(
            'primary-menu' => esc_html__('Primary Menu', 'nkhwazischool'),
            'footer-menu' => esc_html__('Footer Menu', 'nkhwazischool'),
        )
    );

    /*
     * Switch default core markup to output valid HTML5.
     */
    add_theme_support(
        'html5',
        array(
            'search-form',
            'comment-form',
            'comment-list',
            'gallery',
            'caption',
            'style',
            'script',
        )
    );

    // Add theme support for selective refresh for widgets.
    add_theme_support('customize-selective-refresh-widgets');

    /**
     * Add support for core custom logo.
     */
    add_theme_support(
        'custom-logo',
        array(
            'height'      => 250,
            'width'       => 250,
            'flex-width'  => true,
            'flex-height' => true,
        )
    );
}
add_action('after_setup_theme', 'nkhwazischool_setup');

/**
 * Enqueue scripts and styles.
 */
function nkhwazischool_scripts() {
    // Enqueue CSS files
    wp_enqueue_style('components-css', get_template_directory_uri() . '/assets/css/components.css', array(), _S_VERSION);
    wp_enqueue_style('icons-css', get_template_directory_uri() . '/assets/css/icons.css', array(), _S_VERSION);
    wp_enqueue_style('responsee-css', get_template_directory_uri() . '/assets/css/responsee.css', array(), _S_VERSION);
    wp_enqueue_style('owl-carousel-css', get_template_directory_uri() . '/assets/owl-carousel/owl.carousel.css', array(), _S_VERSION);
    wp_enqueue_style('owl-theme-css', get_template_directory_uri() . '/assets/owl-carousel/owl.theme.css', array(), _S_VERSION);
    wp_enqueue_style('lightcase-css', get_template_directory_uri() . '/assets/css/lightcase.css', array(), _S_VERSION);
    wp_enqueue_style('template-style-css', get_template_directory_uri() . '/assets/css/template-style.css', array(), _S_VERSION);
    wp_enqueue_style('custom-style-css', get_template_directory_uri() . '/assets/css/custom-style.css', array(), _S_VERSION);
    wp_enqueue_style('mobile-fixes-css', get_template_directory_uri() . '/assets/css/mobile-fixes.css', array(), _S_VERSION);
    wp_enqueue_style('pagination-fix-css', get_template_directory_uri() . '/assets/css/pagination-fix.css', array(), _S_VERSION);
    wp_enqueue_style('nkhwazischool-style', get_stylesheet_uri(), array(), _S_VERSION);
    
    // Google Fonts
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Noto+Serif+Georgian:wght@100..900&family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap', array(), null);
    
    // jQuery UI CSS
    wp_enqueue_style('jquery-ui-css', 'https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css', array(), '1.12.1');

    // Enqueue JavaScript files
    wp_enqueue_script('jquery');
    wp_enqueue_script('jquery-ui-core');
    wp_enqueue_script('jquery-ui-datepicker');
    wp_enqueue_script('validation-js', get_template_directory_uri() . '/assets/js/validation.js', array('jquery'), _S_VERSION, true);
    wp_enqueue_script('responsee-js', get_template_directory_uri() . '/assets/js/responsee.js', array('jquery'), _S_VERSION, true);
    wp_enqueue_script('plugins-js', get_template_directory_uri() . '/assets/js/plugins.min.js', array('jquery'), _S_VERSION, true);
    wp_enqueue_script('owl-carousel-js', get_template_directory_uri() . '/assets/owl-carousel/owl.carousel.js', array('jquery'), _S_VERSION, true);
    wp_enqueue_script('jquery-countto-js', get_template_directory_uri() . '/assets/js/jquery.countTo.js', array('jquery'), _S_VERSION, true);
    wp_enqueue_script('template-scripts-js', get_template_directory_uri() . '/assets/js/template-scripts.js', array('jquery', 'jquery-countto-js'), _S_VERSION, true);
    
    // Blog filter script removed - using standard WordPress category links instead
    // if (is_home() || is_page('blog') || is_page_template('blog.php') || is_page_template('home.php')) {
    //     wp_enqueue_script('blog-filter-js', get_template_directory_uri() . '/assets/js/blog-filter.js', array('jquery'), _S_VERSION, true);
    // }
}
add_action('wp_enqueue_scripts', 'nkhwazischool_scripts');

/**
 * Register widget area.
 */
function nkhwazischool_widgets_init() {
    register_sidebar(
        array(
            'name'          => esc_html__('Sidebar', 'nkhwazischool'),
            'id'            => 'sidebar-1',
            'description'   => esc_html__('Add widgets here.', 'nkhwazischool'),
            'before_widget' => '<section id="%1$s" class="widget %2$s">',
            'after_widget'  => '</section>',
            'before_title'  => '<h2 class="widget-title">',
            'after_title'   => '</h2>',
        )
    );
}
add_action('widgets_init', 'nkhwazischool_widgets_init');

/**
 * Custom template tags for this theme.
 */
// require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
// require get_template_directory() . '/inc/template-functions.php';

/**
 * Custom Post Types for Nkhwazi Primary School
 */
require get_template_directory() . '/inc/custom-post-types.php';

/**
 * SCF Fields for Nkhwazi Primary School
 */
if (function_exists('acf_add_local_field_group')) {
    require get_template_directory() . '/inc/scf-fields.php';
}

/**
 * Enqueue admin scripts
 */
function nkhwazischool_admin_scripts() {
    wp_enqueue_script('nkhwazischool-admin-scripts', get_template_directory_uri() . '/assets/js/admin-scripts.js', array('jquery', 'acf-input'), _S_VERSION, true);
}
add_action('acf/input/admin_enqueue_scripts', 'nkhwazischool_admin_scripts');

/**
 * Display a message when no management types are available
 * This function is no longer needed since we have management types now
 * Keeping the function but disabling the action
 */
function nkhwazischool_check_management_types() {
    // Only run on the management post type add/edit screen
    $screen = get_current_screen();
    if ($screen->post_type !== 'management') {
        return;
    }
    
    // Count management types
    $management_types = get_posts(array(
        'post_type' => 'management_type',
        'post_status' => 'publish',
        'numberposts' => -1
    ));
    
    // If no management types exist, show a message
    if (empty($management_types)) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>No management types available.</strong> Please <a href="' . admin_url('post-new.php?post_type=management_type') . '">create some management types</a> first.</p>';
        echo '</div>';
    }
}
// Disabling this action since we now have management types
// add_action('admin_notices', 'nkhwazischool_check_management_types');

/**
 * Add custom rewrite rules for school categories to use post name URL structure
 */
function nkhwazischool_add_rewrite_rules() {
    // Add rewrite rule for school categories
    add_rewrite_rule(
        'school-categories/([^/]+)/?$',
        'index.php?pagename=page-single-category&category=$matches[1]',
        'top'
    );
}
add_action('init', 'nkhwazischool_add_rewrite_rules');

/**
 * Flush rewrite rules on theme activation
 */
function nkhwazischool_rewrite_flush() {
    nkhwazischool_add_rewrite_rules();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'nkhwazischool_rewrite_flush');

/**
 * Fix page template recognition
 */
function nkhwazischool_fix_page_templates($page_templates) {
    // Manually add templates that might not be detected automatically
    $manual_templates = array(
        'page-extracurricular.php' => 'Extracurricular Category',
        'page-extracurricular-activities.php' => 'Extracurricular Activities',
        'single-extracurricular-item.php' => 'Single Extracurricular Activity',
        'page-single-extracurricular.php' => 'Single Extracurricular',
        'page-galleries.php' => 'Galleries',
        'page-apply.php' => 'Apply Page',
        'page-management.php' => 'Management',
        'page-jobs.php' => 'Jobs',
        'page-job-detail.php' => 'Job Detail',
        'page-single-category.php' => 'Single Category',
        'page-school-categories.php' => 'School Categories',
        'page-teaching-staff.php' => 'Teaching Staff',
        'page-update-personal-details.php' => 'Update Personal Details'
    );
    
    // Add manual templates to the list
    foreach ($manual_templates as $file => $name) {
        $page_templates[$file] = $name;
    }
    
    // Auto-detect templates from theme directory
    $theme_dir = get_template_directory();
    $files = glob($theme_dir . '/page-*.php');
    
    if ($files) {
        foreach ($files as $file) {
            $filename = basename($file);
            
            // Skip if already added manually
            if (isset($page_templates[$filename])) {
                continue;
            }
            
            // Read the file content
            $file_content = file_get_contents($file);
            
            // Extract template name from file header
            if (preg_match('/Template Name:(.*)$/mi', $file_content, $matches)) {
                $template_name = trim($matches[1]);
                $page_templates[$filename] = $template_name;
            }
        }
    }
    
    return $page_templates;
}
add_filter('theme_page_templates', 'nkhwazischool_fix_page_templates');

/**
 * Set posts per page to 1 for all archive pages
 */
function nkhwazischool_set_posts_per_page($query) {
    if (!is_admin() && $query->is_main_query()) {
        // Set posts per page to 1 for archives, category pages, etc.
        if ($query->is_archive() || $query->is_home() || $query->is_search()) {
            $query->set('posts_per_page', 1);
            
            // For debugging - only visible to admins
            if (is_user_logged_in() && current_user_can('administrator')) {
                // Add a comment to the HTML output
                add_action('wp_footer', function() use ($query) {
                    echo '<!-- Main Query Debug: Posts per page: 1 -->';
                    echo '<!-- Main Query Debug: Is archive: ' . ($query->is_archive() ? 'yes' : 'no') . ' -->';
                    echo '<!-- Main Query Debug: Is home: ' . ($query->is_home() ? 'yes' : 'no') . ' -->';
                    echo '<!-- Main Query Debug: Is category: ' . ($query->is_category() ? 'yes' : 'no') . ' -->';
                    echo '<!-- Main Query Debug: Found posts: ' . $query->found_posts . ' -->';
                    echo '<!-- Main Query Debug: Max num pages: ' . $query->max_num_pages . ' -->';
                });
            }
        }
    }
    return $query;
}
add_action('pre_get_posts', 'nkhwazischool_set_posts_per_page');

// Flush rewrite rules once to ensure pagination works correctly
// This code will run once and then remove itself
function nkhwazischool_flush_rules_once() {
    if (get_option('nkhwazischool_flush_needed', 'yes') === 'yes') {
        flush_rewrite_rules();
        update_option('nkhwazischool_flush_needed', 'no');
    }
}
add_action('init', 'nkhwazischool_flush_rules_once', 999);