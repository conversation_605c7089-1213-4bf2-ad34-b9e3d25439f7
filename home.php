<?php
/**
 * The template for displaying the blog posts index
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article>
    <div class="line">
      <header class="rounded-div section background-blue background-transparent text-center"
        data-image-src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/parallax-02.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">School Blog</h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <!-- put page content below, do not edit above this comment -->
        <div class="margin">
   
          <div class="category-filter">Filter By Category</div>
          <hr class="break-alt">
            <!-- Categories navigation -->
            <div class="grid margin blog-category-nav">
              <div class="s-6 m-4 l-2 margin-bottom-20">
                <a href="?category=all" class="blog-category-link active">All</a>
              </div>
              <div class="s-6 m-4 l-2 margin-bottom-20">
                <a href="?category=events" class="blog-category-link">School Events</a>
              </div>
              <div class="s-6 m-4 l-2 margin-bottom-20">
                <a href="?category=academics" class="blog-category-link">Academics</a>
              </div>
              <div class="s-6 m-4 l-2 margin-bottom-20">
                <a href="?category=facilities" class="blog-category-link">Facilities</a>
              </div>
              <div class="s-6 m-4 l-2 margin-bottom-20">
                <a href="?category=community" class="blog-category-link">Community Service</a>
              </div>
            </div>
         
          <div class="grid margin">
            <?php
            // Get category filter from URL
            $category = isset($_GET['category']) ? sanitize_text_field($_GET['category']) : 'all';
            
            // Set up query args
            $args = array(
                'post_type' => 'post',
                'posts_per_page' => 6,
                'paged' => get_query_var('paged') ? get_query_var('paged') : 1
            );
            
            // Add category filter if not 'all'
            if ($category !== 'all') {
                $args['category_name'] = $category;
            }
            
            // Run the query
            $blog_query = new WP_Query($args);
            
            if ($blog_query->have_posts()) :
                while ($blog_query->have_posts()) : $blog_query->the_post();
                    // Get the first category
                    $categories = get_the_category();
                    $category_name = !empty($categories) ? $categories[0]->name : '';
                    $category_link = !empty($categories) ? get_category_link($categories[0]->term_id) : '';
            ?>
            <article class="s-12 m-6 l-4 margin-bottom-20 blog-post">
              <?php 
              $blog_image = get_field('blog_image');
              if ($blog_image) : 
                $blog_image_url = wp_get_attachment_image_url($blog_image['ID'], 'blog-thumbnail'); // Use the blog-thumbnail size
                if (!$blog_image_url) {
                  $blog_image_url = $blog_image['url']; // Fallback to original if size doesn't exist
                }
              ?>
              <img class="rounded-image margin-bottom blog-image" src="<?php echo esc_url($blog_image_url); ?>" alt="<?php echo esc_attr($blog_image['alt'] ? $blog_image['alt'] : get_the_title()); ?>">
              <?php endif; ?>
              
              <h3 class="text-strong text-uppercase"><a class="text-dark text-blue-hover" href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
              <div class="grid">
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12"><?php echo get_the_date(); ?></div>
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-author">
                  <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>" class="blog-author">By <?php the_author(); ?></a>
                </div>
                <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-category">
                  <?php if (!empty($category_name)) : ?>
                  <a href="<?php echo esc_url($category_link); ?>" class="blog-category"><?php echo esc_html($category_name); ?></a>
                  <?php endif; ?>
                </div>
              </div>
              <p><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
              <a class="text-more-info text-primary-hover margin-bottom-30" href="<?php the_permalink(); ?>">Blog Details</a>
            </article>
            <?php
                endwhile;
            else:
            ?>
            <div class="s-12 margin-bottom-20">
              <p>No blog posts found.</p>
            </div>
            <?php endif; ?>
          </div><!-- Grid -->

          <!-- Pagination -->
          <div class="line text-center pagination-container">
            <div class="s-12 margin-bottom">
              <ul class="pagination">
                <?php
                $big = 999999999; // need an unlikely integer
                echo paginate_links(array(
                    'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                    'format' => '?paged=%#%',
                    'current' => max(1, get_query_var('paged')),
                    'total' => $blog_query->max_num_pages,
                    'prev_text' => __('Prev', 'nkhwazischool'),
                    'next_text' => __('Next', 'nkhwazischool'),
                    'type' => 'list'
                ));
                wp_reset_postdata();
                ?>
              </ul>
            </div>
          </div>
        </div>

        <!-- put page content above, do not edit below this comment -->

      </div>
    </div>
  </article>
</main>

<?php get_footer(); ?>