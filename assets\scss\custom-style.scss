/* Custom styles for Nkhwazi School */

/* Contact Form Styles */
.contact-form {
    position: relative;
    
    /* Form validation styles */
    input.error, 
    textarea.error, 
    select.error {
        border-left: 4px solid #e74c3c !important;
    }
    
    input.valid, 
    textarea.valid, 
    select.valid {
        border-left: 4px solid #2ecc71 !important;
    }
    
    .field-error-message {
        color: #e74c3c;
        font-size: 12px;
        margin-top: 5px;
        display: block;
    }
    
    /* Character counter styles */
    .field-with-counter {
        position: relative;
        width: 100%;
        margin-bottom: 10px;
        
        .character-counter {
            position: absolute;
            right: 10px;
            top: -20px;
            font-size: 12px;
            color: #777;
            
            &.warning {
                color: #f39c12;
            }
            
            &.error {
                color: #e74c3c;
            }
        }
        
        input, textarea {
            width: 100%;
        }
    }
    
    /* Fix for field error messages */
    .field-error-message {
        display: block;
        margin-top: 5px;
        margin-bottom: 10px;
    }
    
    /* Form message styles */
    .form-message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
        display: none;
        
        &.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        &.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    }
    
    /* Loading indicator */
    .form-loading {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 100;
        text-align: center;
        padding-top: 100px;
        
        .spinner {
            display: inline-block;
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        p {
            margin-top: 15px;
            font-weight: bold;
        }
    }
    
    /* reCAPTCHA container */
    .g-recaptcha-container {
        margin-bottom: 20px;
        
        .recaptcha-error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
        }
    }
    
    /* Center reCAPTCHA */
    .g-recaptcha {
        display: inline-block;
    }
}

/* Animation for spinner */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Make sure the submit button is a button, not a link */
.submit-btn {
    cursor: pointer;
    
    &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .contact-form {
        .g-recaptcha {
            transform: scale(0.85);
            transform-origin: 0 0;
        }
        
        .field-with-counter .character-counter {
            top: -18px;
            font-size: 10px;
        }
    }
}