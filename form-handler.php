<?php
/**
 * Form Handler
 * 
 * This file handles form submissions directly without using AJAX.
 */

// Load WordPress
require_once('../../../../wp-load.php');

// Log the request
error_log('Form handler called');
error_log('POST data: ' . print_r($_POST, true));

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Process the form
    $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';
    $message = isset($_POST['message']) ? sanitize_textarea_field($_POST['message']) : '';
    
    // Create a post to store the submission
    $post_id = wp_insert_post(array(
        'post_title' => 'Form Submission from ' . $name,
        'post_content' => $message,
        'post_type' => 'post',
        'post_status' => 'draft'
    ));
    
    if (is_wp_error($post_id)) {
        $error = $post_id->get_error_message();
    } else {
        $success = true;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Submission</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #2980b9;
        }
        .message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Form Submission</h1>
        
        <?php if (isset($success)): ?>
            <div class="message success">
                <p>Thank you for your submission! Your message has been received.</p>
                <p><a href="form-handler.php">Submit another message</a></p>
            </div>
        <?php elseif (isset($error)): ?>
            <div class="message error">
                <p>An error occurred: <?php echo $error; ?></p>
                <p><a href="form-handler.php">Try again</a></p>
            </div>
        <?php else: ?>
            <p>Please fill out the form below to submit a message.</p>
            
            <form method="post" action="form-handler.php">
                <div class="form-group">
                    <label for="name">Your Name:</label>
                    <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="message">Your Message:</label>
                    <textarea id="message" name="message" rows="4" required></textarea>
                </div>
                
                <button type="submit">Submit</button>
            </form>
        <?php endif; ?>
    </div>
</body>
</html>