<?php
/**
 * Template Name: Job Post Detail
 * Description: A template for displaying detailed information about a specific job posting.
 *              Features a full-width layout with comprehensive job details.
 * 
 * WordPress Conversion Notes:
 * - Will become single-job.php in WordPress theme
 * - $job array will be replaced with WordPress post data and meta fields
 * - The static content will be replaced with dynamic WordPress content
 * 
 * Author: <PERSON>
 */

require_once('includes/header.php');

// Get the job ID from URL parameter
$job_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// STATIC DATA PLACEHOLDER
// In WordPress, this will be replaced with:
// if (have_posts()) : while (have_posts()) : the_post();
// And meta fields will be used for the job details

// This would be replaced with actual data from database
// For now, we'll use sample data
$job = [
    // In WordPress, this would be the_title()
    'title' => 'Mathematics Teacher',
    
    // In WordPress, these would be custom fields (post meta)
    'reference' => 'MT2023-01',
    'excerpt' => 'Join our dedicated team as a Mathematics Teacher. Shape young minds and inspire the next generation of mathematicians.',
    'deadline' => 'June 30, 2023',
    'how_to_apply' => 'Submit your CV, academic certificates, cover letter, and teaching <NAME_EMAIL> or apply in person at the school administration office.',
    
    // In WordPress, this would be the_content()
    'description' => '<h3>About the Role</h3>
    <p>We are seeking an experienced and passionate Mathematics Teacher to join our dedicated team at Nkhwazi Primary School. The successful candidate will be responsible for teaching mathematics to students in grades 5-7, developing engaging lesson plans, and fostering a love for mathematics among our students.</p>
    
    <h3>Key Responsibilities</h3>
    <ul>
        <li>Teach mathematics to primary school students in grades 5-7</li>
        <li>Develop and implement engaging lesson plans aligned with the national curriculum</li>
        <li>Assess student progress and provide constructive feedback</li>
        <li>Identify and support students who require additional assistance</li>
        <li>Collaborate with other teachers to enhance the mathematics curriculum</li>
        <li>Participate in staff meetings and professional development activities</li>
        <li>Communicate effectively with parents regarding student progress</li>
        <li>Organize and supervise mathematics-related extracurricular activities</li>
    </ul>
    
    <h3>Qualifications</h3>
    <ul>
        <li>Bachelor\'s degree in Education or Mathematics</li>
        <li>Teaching certification/license</li>
        <li>Minimum of 2 years teaching experience, preferably in a primary school setting</li>
        <li>Strong knowledge of mathematics curriculum and teaching methodologies</li>
        <li>Excellent communication and interpersonal skills</li>
        <li>Ability to create a positive and inclusive learning environment</li>
        <li>Proficiency in using technology to enhance teaching and learning</li>
    </ul>
    
    <h3>Benefits</h3>
    <ul>
        <li>Competitive salary based on qualifications and experience</li>
        <li>Professional development opportunities</li>
        <li>Supportive and collaborative work environment</li>
        <li>Opportunity to make a significant impact on students\' educational journey</li>
    </ul>'
];

// WordPress conversion note:
// This static data handling will be replaced with WordPress loop:
// if (!have_posts()) {
//     // Handle job not found
//     wp_redirect(home_url('/jobs/'));
//     exit;
// }
?>

<!-- MAIN -->
<main role="main">
  <!-- Content -->
  <article id="post-<?php echo $job_id; ?>" class="job-post"> <!-- WordPress: Will become post_class() -->
    <div class="line">
      <header class="rounded-div section background-primary background-transparent text-center"
        data-image-src="assets/img/parallax-02.jpg">
        <h1 class="text-uppercase text-white margin-bottom-0 text-thin text-line-height-1">Career Opportunity</h1>
      </header>
    </div>

    <div class="section background-white">
      <div class="line">
        <div class="margin">
          <div class="s-12 m-12 l-9 center">
            <!-- Job Details -->
            <div class="job-detail-container">
              <!-- WordPress: Will become the_title() -->
              <h2 class="text-primary entry-title"><?php echo $job['title']; ?></h2>
              
              <div class="job-meta margin-bottom-30">
                <div class="grid">
                  <div class="s-12 m-6 l-4">
                    <!-- WordPress: Will become echo get_post_meta(get_the_ID(), 'job_reference', true); -->
                    <p><strong>Job Reference:</strong> <?php echo $job['reference']; ?></p>
                  </div>
                  <div class="s-12 m-6 l-4">
                    <!-- WordPress: Will become echo get_post_meta(get_the_ID(), 'job_deadline', true); -->
                    <p><strong>Application Deadline:</strong> <?php echo $job['deadline']; ?></p>
                  </div>
                </div>
              </div>
              
              <div class="job-excerpt margin-bottom-30">
                <!-- WordPress: Will become echo get_post_meta(get_the_ID(), 'job_excerpt', true); -->
                <p class="text-size-16"><em><?php echo $job['excerpt']; ?></em></p>
              </div>
              
              <div class="job-description margin-bottom-30 entry-content">
                <!-- WordPress: Will become the_content(); -->
                <?php echo $job['description']; ?>
              </div>
              
              <div class="job-application margin-bottom-30">
                <h3 class="text-primary">How to Apply</h3>
                <!-- WordPress: Will become echo get_post_meta(get_the_ID(), 'job_how_to_apply', true); -->
                <p><?php echo $job['how_to_apply']; ?></p>
              </div>
              
              <div class="job-actions margin-bottom-30">
                <!-- WordPress: Will become echo home_url('/jobs/'); -->
                <a href="page-jobs.php" class="rounded-btn button background-primary text-white">Back to All Jobs</a>
                
                <!-- WordPress: Will use get_post_meta and home_url -->
                <a href="page-apply.php?job_ref=<?php echo urlencode($job['reference']); ?>" class="button rounded-btn background-yellow text-white">Apply Now</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </article>
</main>

<?php require_once('includes/footer.php'); ?>