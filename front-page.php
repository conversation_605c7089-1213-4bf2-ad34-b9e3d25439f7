<?php
/**
 * The front page template file
 *
 * If the user has selected a static page for their homepage, this is what will
 * appear. Learn more: https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Nkhwazi_Primary_School
 */

get_header();
?>

<!-- MAIN -->
<!-- Page ID: 238 (Homepage) -->
<main role="main">

  <!-- Main Carousel -->
  <?php get_template_part('includes/carousel'); ?>
  <div class="line">
    <hr class="break margin-top-40 margin-bottom-0">
  </div>

  <!-- Posts -->
  <section class="section-small-padding background-white">
    <div class="line">
      <div class="margin">
        <div class="s-12 m-12 l-12">

          <!--About Nkhwazi Primary School-->
          <div class="grid margin">
            <article class="s-12 m-12 l-6 margin-m-bottom">
              <h2 class="text-strong text-uppercase rounded-div"><a class="text-dark text-primary-hover" href="<?php echo esc_url(get_permalink(238)); ?>">Nkhwazi Primary School</a></h2>
              <p>Founded in 1971, Nkhwazi Primary School is one of the oldest private schools in Lusaka. It has a
                long-standing tradition of academic excellence and community involvement.</p>
              <a class="text-more-info text-primary-hover margin-top-20" href="<?php echo esc_url(get_permalink(238)); ?>">More About Us</a>
            </article>
            <article class="s-12 m-12 l-6 margin-m-bottom">
              <img class="margin-bottom" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/img-06.jpg" alt="">
            </article>
          </div>

          <!-- Recent Notifications -->
          <hr class="break">
          <div class="line">
            <h2 class="text-padding-small background-primary text-white text-strong text-uppercase rounded-div margin-bottom-30"><a
                href="<?php echo esc_url(get_permalink(426)); ?>"><i class="icon-sli-bell margin-right-10"></i>Recent Notifications</a></h2>
          </div>
        
            <div class="grid margin">
              <?php
              // Get recent notifications
              $args = array(
                  'post_type' => 'notification',
                  'posts_per_page' => 3,
                  'orderby' => 'date',
                  'order' => 'DESC',
              );
              $notifications_query = new WP_Query($args);
              
              if ($notifications_query->have_posts()) :
                  while ($notifications_query->have_posts()) : $notifications_query->the_post();
                      // Get ACF fields
                      $heading = get_field('heading');
                      $description = get_field('description');
                      $post_date = get_the_date('F j, Y');
              ?>
              
                <div class="s-12 m-6 l-4 margin-bottom-20 notification-card padding background-white rounded-div box-shadow">
                  <h3 class="text-strong text-size-20 margin-bottom-5"><?php echo esc_html($heading); ?></h3>
                  <small class="text-grey margin-bottom-10 display-block">Posted: <?php echo esc_html($post_date); ?></small>
                  <div class="margin-bottom-0"><?php echo wp_kses_post($description); ?></div>
            
                </div>
             
              <?php
                  endwhile;
                  wp_reset_postdata();
              endif; ?>
            </div>
            <div class="text-center margin-bottom-10">
              <a href="<?php echo esc_url(get_permalink(426)); ?>" class="button background-primary text-white">View All Notifications</a>
            </div>
          

          <!-- School Categories -->
          <hr class="break">
          <div class="line">
            <h2 class="text-padding-small background-primary text-white text-strong text-uppercase rounded-div margin-bottom-30"><a
                href="<?php echo esc_url(get_permalink(328)); ?>"><i class="icon-spread margin-right-10"></i>School Categories</a></h2>
          </div>
          <div class="line">
            <div class="grid margin">
              <?php
              // Get school categories
              $args = array(
                  'post_type' => 'school_categories',
                  'posts_per_page' => 3,
                  'orderby' => 'menu_order',
                  'order' => 'ASC',
              );
              $categories_query = new WP_Query($args);
              
              if ($categories_query->have_posts()) :
                  while ($categories_query->have_posts()) : $categories_query->the_post();
                      // Get ACF fields
                      $school_category = get_field('school_category');
                      $category_excerpt = get_field('category_excerpt');
                      $grade_range = get_field('grade_range');
                      $age_range = get_field('age_range');
                      $card_img = get_field('card_img');
                      
                      // Get slug for URL
                      $slug = sanitize_title(get_the_title());
              ?>
              <div class="s-12 m-6 l-4 margin-m-bottom">
                <div class="home-category-card blog-post">
                  <?php if ($card_img) : ?>
                    <img class="margin-bottom rounded-image-top" src="<?php echo esc_url(wp_get_attachment_image_url($card_img['ID'], 'school-category-card')); ?>" alt="<?php echo esc_attr(get_the_title()); ?>">
                  <?php endif; ?>
                  <h3 class="text-strong"><a class="text-dark text-primary-hover" href="<?php echo esc_url(get_permalink()); ?>"><?php echo esc_html(get_the_title()); ?></a>
                  </h3>
                  <p><?php echo esc_html($category_excerpt); ?></p>
                  <p><?php echo esc_html($grade_range); ?>/ <?php echo esc_html($age_range); ?></p>
                  <div class="margin-top-bottom-20">
                    <a class="text-more-info text-primary-hover" href="<?php echo esc_url(get_permalink()); ?>">Category Details</a>
                  </div>
                </div>
              </div>
              <?php
                  endwhile;
                  wp_reset_postdata();
              endif; ?>
            </div>
          </div>
          
          <!-- Our Numbers -->
          <hr class="break">
          <div class="line">
            <h2 class="text-padding-small background-blue text-white text-strong text-uppercase rounded-div margin-bottom-30"><i class="icon-chart margin-right-20"></i>Our Numbers Say</h2>
          </div>

          <div class="grid margin nkhwazi-stats">
            <?php
            // Get stats
            $args = array(
                'post_type' => 'nkhwazi_stats',
                'posts_per_page' => 4,
                'orderby' => 'date',
                'order' => 'ASC',
            );
            $stats_query = new WP_Query($args);
            
            if ($stats_query->have_posts()) :
                while ($stats_query->have_posts()) : $stats_query->the_post();
                    // Get ACF fields
                    $stat_body = get_field('stat_body');
                    $stat_number = get_field('stat_number');
                    $is_percentage = get_field('stat_is_percentage');
            ?>
            <div class="s-12 m-6 l-3 number-stat">
              <div class="block">
                <div class="count-to text-center">
                  <span class="timer h1 text-size-60 text-strong text-yellow-darker" data-from="0" data-to="<?php echo esc_attr($stat_number); ?>"
                    data-speed="2000"></span> 
                  <?php
                  // Only show percentage symbol if the field is set to 'yes'
                  if ($is_percentage === 'yes') : ?>
                  <span class="h1 text-size-50 text-strong text-yellow-darker">%</span>
                  <?php endif; ?>
                  <p class="h1 text-size-20 margin-top-10 text-thin text-center"><?php echo esc_html($stat_body); ?></p>
                </div>
              </div>
            </div>
            <?php
                endwhile;
                wp_reset_postdata();
            endif; ?>
          </div>

        <!-- Our Blog -->
          <hr class="break">
          <div class="line">
            <h2 class="text-padding-small background-blue text-white text-strong text-uppercase rounded-div margin-bottom-30"><a
                href="<?php echo esc_url(get_permalink(241)); ?>"><i class="icon-newspaper margin-right-20"></i>Our Blog</a></h2>
          </div>
        
          <div class="grid margin">
            <?php
            // Get the latest 2 blog posts
            $args = array(
                'post_type' => 'post',
                'posts_per_page' => 2,
            );
            $query = new WP_Query($args);
            
            if ($query->have_posts()) :
                while ($query->have_posts()) : $query->the_post();
            ?>
              <article class="s-12 m-12 l-6 margin-m-bottom blog-post">
                <?php 
                $blog_image = get_field('blog_image');
                if ($blog_image) : 
                  $blog_image_url = wp_get_attachment_image_url($blog_image['ID'], 'blog-thumbnail'); // Use the blog-thumbnail size
                  if (!$blog_image_url) {
                    $blog_image_url = $blog_image['url']; // Fallback to original if size doesn't exist
                  }
                ?>
                  <img class="rounded-image-top margin-bottom blog-image" src="<?php echo esc_url($blog_image_url); ?>" alt="<?php echo esc_attr($blog_image['alt'] ? $blog_image['alt'] : get_the_title()); ?>">
                <?php endif; ?>
                
                <h3 class="text-strong"><a class="text-dark text-orange-hover" href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                </h3>
                <div class="grid margin">
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12"><?php echo get_the_date(); ?></div>
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-author"><a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>"
                      class="blog-author">By <?php the_author(); ?></a></div>
                  <div class="s-4 m-4 l-4 margin-bottom text-dark text-size-12 blog-category">
                    <?php
                    $categories = get_the_category();
                    if (!empty($categories)) {
                        echo '<a href="' . esc_url(get_category_link($categories[0]->term_id)) . '" class="blog-category">' . esc_html($categories[0]->name) . '</a>';
                    }
                    ?>
                  </div>
                </div>

                <p><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
                <a class="text-more-info text-primary-hover margin-bottom-30" href="<?php the_permalink(); ?>">Blog Details</a>
              </article>
            <?php
                endwhile;
                wp_reset_postdata();
            endif; ?>
          </div>

          <!-- Upcoming Events -->
          <hr class="break">
          <div class="line">
            <h2 class="text-padding-small background-blue text-white text-strong text-uppercase rounded-div margin-bottom-30"><a
                href="<?php echo esc_url(get_permalink(434)); ?>"><i class="icon-sli-calendar margin-right-20"></i>Upcoming Events</a></h2>
          </div>
        
          <?php get_template_part('includes/upcoming-events'); ?>

        </div>


      </div>
    </div>
  </section>
</main>

<?php
get_footer();